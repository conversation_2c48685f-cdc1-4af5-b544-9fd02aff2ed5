.transition (@element: color, @speed: .3s) {
	transition: @arguments;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.text-shadow (@color, @x: 1px, @y: 1px, @blur: 0px) {
	text-shadow: @x @y @blur @color;
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background-image: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	//background-image: -webkit-linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.scaleX(@factor) {
	transform: scaleX(@factor);
	-webkit-transform: scaleX(@factor);
	-ms-transform: 		 scaleX(@factor);	
}
.scaleY (@factor) {
	transform: scaleY(@factor);
	-webkit-transform: scaleY(@factor);
	-ms-transform: scaleY(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.clear() { 
	/**zoom: 1;*/ clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}
.shadow { content:""; position: absolute; left: 35px; right: 35px; top: 20px; bottom: 5px; box-shadow: 0px 10px 65px rgba(0,0,0,.6); }
.lloader {
	background: #fff url(images/loader.gif) no-repeat center center; background-size: 65px auto;
	img { opacity: 0; .transition(opacity); }
	&.loaded {
		background: #fff;
		img { opacity: 1; }
	}
}
.list{
	list-style: none; padding: 0; margin: 0 0 15px 20px;
	li{
		position: relative; padding: 2px 0 2px 23px;
		&:before{.pseudo(8px, 8px); background: var(--yellow); border-radius: 200px; top: 10px; left: 0;}
	}
}


.icon-barcode() {
  content: "\e933";
}
.icon-info-btn() {
  content: "\e931";
}
.icon-calendar() {
  content: "\e930";
}
.icon-flyer() {
  content: "\e932";
}
.icon-user_notactive() {
  content: "\e92f";
}
.icon-wishlist_notactive() {
  content: "\e92e";
}
.icon-compare_redesign() {
  content: "\e92d";
}
.icon-cart_active() {
  content: "\e92c";
}
.icon-user_active() {
  content: "\e92b";
}
.icon-cart_notactive() {
  content: "\e92a";
}
.icon-wishlist_active() {
  content: "\e929";
}
.icon-coupon() {
  content: "\e928";
}
.icon-question() {
  content: "\e927";
}
.icon-download() {
  content: "\e926";
}
.icon-info() {
  content: "\e925";
}
.icon-zoom-in() {
  content: "\e922";
}
.icon-zoom-out() {
  content: "\e923";
}
.icon-card() {
  content: "\e924";
}
.icon-discount() {
  content: "\e921";
}
.icon-bin() {
  content: "\e920";
}
.icon-gift() {
  content: "\e91f";
}
.icon-close2() {
  content: "\e91d";
}
.icon-close() {
  content: "\e91e";
}
.icon-grid() {
  content: "\e91b";
}
.icon-list() {
  content: "\e91c";
}
.icon-star() {
  content: "\e91a";
}
.icon-phone2() {
  content: "\e919";
}
.icon-pin() {
  content: "\e918";
}
.icon-phone() {
  content: "\e917";
}
.icon-plus() {
  content: "\e916";
}
.icon-quote() {
  content: "\e915";
}
.icon-viber() {
  content: "\e912";
}
.icon-whatsapp() {
  content: "\e913";
}
.icon-envelope() {
  content: "\e914";
}
.icon-arrow-right() {
  content: "\e911";
}
.icon-news() {
  content: "\e910";
}
.icon-arrow-down() {
  content: "\e900";
}
.icon-box() {
  content: "\e901";
}
.icon-cart() {
  content: "\e902";
}
.icon-check() {
  content: "\e903";
}
.icon-compare() {
  content: "\e904";
}
.icon-danger-red() {
  content: "\e905";
}
.icon-facebook() {
  content: "\e906";
}
.icon-home() {
  content: "\e907";
}
.icon-instagram() {
  content: "\e908";
}
.icon-mail() {
  content: "\e909";
}
.icon-pdf() {
  content: "\e90a";
}
.icon-search() {
  content: "\e90b";
}
.icon-truck() {
  content: "\e90c";
}
.icon-user() {
  content: "\e90d";
}
.icon-wallet() {
  content: "\e90e";
}
.icon-wishlist() {
  content: "\e90f";
}