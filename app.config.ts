export default defineAppConfig({
	host: 'https://ctehnike.markerheadless.info',
	lang: 'hr',
	baseCompatibility: '1.30.0',
	publish: {
		postsResponseFields: ['id', 'url_without_domain', 'title', 'main_image_thumbs', 'short_description', 'images'],
	},
	compare: {
		limit: 3,
	},
	viewOrder: {
		footerLabels: true,
	},
	google: {
		gtm: {
			env: ['production'],
			//gdpr: 'marketing'
		},
		ga4: {
			env: ['production'], // učitavanje samo u produkcijskom okruženju (nakon builda)
			gdpr: '' // učitavanje google analytics 4 koda samo nakon prihvaćanja analytics privola
		},
		tracking: {
			gdpr: 'analytics',
			debug: true,
			affiliation: '',
			viewItemList: {
				name: 'view_item_list',
			},
			viewItem: {
				name: 'view_item',
			},
			purchase: {
				name: 'purchaseSuccess',
			},
			addProduct: {
				name: 'AddToCart',
				hooks: {
					mapData(data) {
						if(!data?.ecommerce?.items?.length) return data;
						const finalData = {
							event: 'AddToCart',
							currency: data.ecommerce.items[0].currency,
							ecommerce: {
								add: {
									products: data.ecommerce.items.map((item) => ({
										id: item.item_code ? item.item_code : '',
										name: item.item_name ? item.item_name : '',
										category: item.item_category ? item.item_category : '',
										brand: item.item_brand ? item.item_brand : '',
										price: item.price ? item.price : '',
										quantity: item.quantity ? item.quantity : '',
										image: item.image ? item.image : '',
										variant: item.item_variant ? item.item_variant : '',
									}))
								}
							}
						}
						return finalData;
					},
				}
			},
			events: ['viewItemList', 'viewItem', 'addProduct', 'beginCheckout', 'purchase'],
		},
		remarketing: {
			env: ['production','development'], // u kojem okruženju se šalju eventi (development, production)
			debug: true, // u konzoli se prikazuje poruka sa poslanim eventom i statusom. Koristiti samo u dev fazi
			events: ['home', 'offerdetail', 'searchresults', 'category', 'conversionintent', 'conversion'],
			gdpr: '',
		}
	},
	facebook: {
		pixel: {
			env: ['production'],
			apiKey: '647088069007610',
			target: 'head',
			gdpr: 'analytics'
		},
	},
	structuredData: {
		enabled: true,
		basic: {
			name: 'Centar Tehnike d.o.o.',
			organizationName: 'Centar Tehnike d.o.o.',
			logo: 'https://www.centar-tehnike.hr/media/images/logo.svg',
			phone: '+38531499573',
			email: '<EMAIL>',
			socialNetworkUrls: '"https://www.facebook.com/CentarTehnikeOsijek/"',
		},
	},
})