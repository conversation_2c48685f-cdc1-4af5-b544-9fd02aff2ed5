<template>
	<div>
		<!-- Example 1: BaseForm with name prop -->
		<BaseForm name="contactForm" v-slot="{meta, values, errors}">
			<BaseFormInput name="email" type="email" placeholder="Email" />
			<BaseFormInput name="message" type="textarea" placeholder="Message" />
			<button type="submit">Submit Contact</button>
		</BaseForm>

		<!-- Example 2: BaseFormSiteForm with name prop -->
		<BaseFormSiteForm name="newsletterForm" code="newsletter" v-slot="{meta, values, errors, fields}">
			<div v-for="field in fields" :key="field.name">
				<BaseFormInput :field="field" />
			</div>
			<button type="submit">Subscribe</button>
		</BaseFormSiteForm>

		<!-- Example 3: BaseFormSiteForm without name prop (uses code as name) -->
		<BaseFormSiteForm code="contact" v-slot="{meta, values, errors, fields}">
			<div v-for="field in fields" :key="field.name">
				<BaseFormInput :field="field" />
			</div>
			<button type="submit">Send Message</button>
		</BaseFormSiteForm>

		<!-- Display current form states -->
		<div class="debug-info">
			<h3>Current Form States:</h3>
			<pre>{{ JSON.stringify(baseForms, null, 2) }}</pre>
		</div>
	</div>
</template>

<script setup>
	// Access the baseForms state to see stored metadata
	const baseForms = useState('baseForms', () => ({}));
</script>

<style scoped>
	.debug-info {
		margin-top: 20px;
		padding: 10px;
		background-color: #f5f5f5;
		border-radius: 4px;
	}
</style>
