// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
	extends: ['../nuxt-base'],
	css: ['@/assets/style.css'],
	vite: {
		css: {
			preprocessorOptions: {
				less: {
					additionalData: `@import "@/assets/_vars.less"; @import "@/assets/_mixins.less";`,
				},
			},
		},
		define: {
			__LOCATIONS__: JSON.stringify(true),
			__RECAPTCHA__: JSON.stringify(true),
			__GTM_TRACKING__: JSON.stringify(true),
			__REMARKETING__: JSON.stringify(true),
			__STRUCTURED_DATA__: JSON.stringify(true),
			__KEKSPAY__: JSON.stringify(true),
		},
	},
	dir: {
		'public': 'media',
	},
	$production: {
		sourcemap: {
			server: false,
			client: false,
		},
		routeRules: {
			'*': {
				swr: 120,
				cache: {base: 'db'},
			},
		},
	},
	nitro: {
		minify: true,
		storage: {
			db: {
				driver: 'redis',
				host: 'redis_ctehnike_markerheadless_info_nuxt',
				port: 6379,
				username: 'default',
				password: 'LqBKEDGSBM',
				base: 'ctehnike_dev',
			},
		},
		devStorage: {
			db: {
				driver: 'fs',
				base: '.app_cache_data',
			},
		},
	},
	compatibilityDate: '2024-07-23',
});
