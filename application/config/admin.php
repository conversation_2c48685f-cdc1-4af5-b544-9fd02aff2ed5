<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'massive_print' => [
        'webshoporder',
    ],
    'full_readonly_fields' => [
        /* syntax: '<model>' => ['<field1>', '<field2>'], */
        'catalogproduct' => ['basic_price', 'discount_percent'],
    ],
    'list_field_model_column_tracking_from_to' => ['catalogproduct.basic_price_base', 'catalogproduct.discount_percent_base', 'catalogproduct.discount_base', 'catalogproduct.purchase_price', 'catalogproduct.internal_code2'],
    "action_display" => [
        "add_delivery_order" => "Narudžba/e prebačene u pripremu za kurirsku službu",
        "order_to_2023" => "Narudžbe kreirane u 2022 prebaci u 2023"
    ],
    'webshoporder_transfer_from_2022_to_2023_enable' => true,
    'colors' => [
        'webshoporder' => [
            'status' => [
                'djelomicno_poslano' => '#d5fdd5', // svjetlija zelena
            ],
        ],
    ],
    'import2' => [
        'display_errors' => true,
        'model' => 'catalogproduct',
        'title' => 'Import',
        'main_separator' => ',',
        'catalogproduct' => [
            'fields' => [
                'code',
                //'slug',
                'available_qty',
                'ean_code',
                'internal_code2',
                //'basic_price',
                //'discount_percent',
                //'discount_percent_base',
                //'discount',
                'price',
                'purchase_price',
                //'main_image',
                //'calculate_price',
                //'basic_price_base',
                'volumen',
                'package_weight',
                'weight',
                'visible',
                'content_hr',
                'short_description_hr',
                'comment',
                'credit_card_installments_supplement',
                'title_hr',
                'seo_title_hr',
                'seo_h1_hr',
                'manufacturer',
                'exclude_delivery',
            ],
            'extra_category_columns' => [],
            'default_tax_amount' => '25',
            'autodetect_code_field_by_asterisks' => true,
            'use_advanced_logger' => true,
            'allow_only_update' => true,
            'use_code_as_string' => true,
            'force_fields_from_import_table' => true,
            'product_use_price_locked' => true,
        ],
        'catalogproductdiscount' => [
            'fields' => [
                'code',
                'discount_percent_base',
                'calculate_price',
                'price',
                'credit_card_installments_supplement',
                'date_active_from',
                'date_active_to',
            ],
            'use_advanced_logger' => true,
        ],
    ],
];