<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'generate_thumb' => [
        'big' => ['width' => 760, 'height' => NULL, 'crop' => FALSE],
        'big_crop' => ['width' => 760, 'height' => 340, 'crop' => TRUE],
        'medium' => ['width' => 420, 'height' => NULL, 'crop' => FALSE],
        'medium_crop' => ['width' => 420, 'height' => 380, 'crop' => TRUE],
        'small_crop' => ['width' => 220, 'height' => 140, 'crop' => TRUE],
    ],
    'generate_thumb_upload' => [
        'rotatorelement-image' => [
            ['width' => 100, 'height' => 300, 'crop' => FALSE],
            ['width' => 435, 'height' => 300, 'crop' => TRUE],
            ['width' => 675, 'height' => 320, 'crop' => TRUE],
            ['width' => 1400, 'height' => 600, 'crop' => FALSE],
            ['width' => 1400, 'height' => 400, 'crop' => TRUE],
            ['width' => 980, 'height' => 670, 'crop' => TRUE],
            ['width' => 920, 'height' => 300, 'crop' => TRUE],
            ['width' => 118, 'height' => 100, 'crop' => TRUE],
        ],
        'rotatorelement-image_2' => [
            ['width' => 640, 'height' => 480, 'crop' => TRUE],
            ['width' => 980, 'height' => 1225, 'crop' => FALSE],
            ['width' => 980, 'height' => 670, 'crop' => TRUE],
            ['width' => 980, 'height' => 670, 'crop' => TRUE],
        ],        
        'locationpointfile-file' => [
            ['width' => 250, 'height' => 135, 'crop' => TRUE],
        ],
        'publishfile-file' => [
            ['width' => 920, 'height' => 480, 'crop' => TRUE],
            ['width' => 435, 'height' => 230, 'crop' => TRUE],
            ['width' => 1400, 'height' => 800, 'crop' => TRUE],
        ],
        'catalogmanufacturer-image' => [
            ['width' => 95, 'height' => 30, 'crop' => FALSE],
            ['width' => 350, 'height' => 60, 'crop' => FALSE],
            ['width' => 100, 'height' => 30, 'crop' => FALSE],
        ],
        'catalogproductfile-file' => [
            ['width' => 1300, 'height' => 1300, 'crop' => FALSE],
            ['width' => 650, 'height' => 650, 'crop' => FALSE],
            ['width' => 265, 'height' => 265, 'crop' => FALSE],
            ['width' => 144, 'height' => 144, 'crop' => FALSE],
            ['width' => 100, 'height' => 100, 'crop' => FALSE],
            ['width' => 110, 'height' => 110, 'crop' => FALSE],
            ['width' => 56, 'height' => 56, 'crop' => FALSE],
            ['width' => 180, 'height' => 180, 'crop' => FALSE],
        ],
        'cataloglist-list_main_image' => [
			['width' => 60, 'height' => 200, 'crop' => FALSE],
		],
        'catalogcategory-image' => [
			['width' => 288, 'height' => 288, 'crop' => FALSE],
		],
        'staticcontentpageitem-image' => [
			['width' => 1400, 'height' => 600, 'crop' => FALSE],
            ['width' => 1400, 'height' => 400, 'crop' => TRUE],
            ['width' => 980, 'height' => 670, 'crop' => TRUE],
            ['width' => 920, 'height' => 300, 'crop' => TRUE],
            ['width' => 435, 'height' => 230, 'crop' => TRUE],
            ['width' => 265, 'height' => 265, 'crop' => FALSE],
            ['width' => 95, 'height' => 30, 'crop' => FALSE],
            ['width' => 60, 'height' => 200, 'crop' => FALSE],
            ['width' => 1920, 'height' => 660, 'crop' => FALSE],
		],
        'staticcontentpageitem-image2' => [
			['width' => 980, 'height' => 1225, 'crop' => FALSE],
			['width' => 980, 'height' => 670, 'crop' => TRUE],
		],
    ],
];
