<?php

defined('SYSPATH') or die('No direct script access.');

return [
    'mode' => 'allow_all_except',
    'localhost_domain_list' => ['http://localhost:3000', 'http://localhost:3000/', 'http://0.0.0.0:3000', 'http://0.0.0.0:3000/', ],// domene na kojima FE pokreće lokalni razvoj
    'web_login_equals_nuxt_login' => true,
    'info_extra_configs' => ['recaptcha_site_key'],  
    'customer_fields' => [
        'forms' => [
            'webshop' => [
                'customer' => [
                    'auto_set_data' => true,
                    'fields' => ['first_name', 'last_name', 'address', 'house_number', 'zipcode', 'city', 'location', 'phone', 'message',
                        'b_same_as_shipping', 'b_first_name', 'b_last_name', 'b_location', 'b_address', 'b_house_number', 'b_phone',
                        'b_r1', 'b_company_name', 'b_company_oib', 'b_company_address',
                        ],
                    'required_fields' => ['first_name', 'last_name', 'address', 'house_number', 'zipcode', 'city', 'location', 'phone'],
                    'step' => 10,
                ],
                'review-order' => [
                    'auto_set_data' => false,
                    'fields' => ['message','accept_terms','newsletter'],
                    'required_fields' => ['accept_terms'],
                    'step' => 30,
                ],
            ],
            'auth' => [
                'signup' => [
                    'auto_set_data' => false,
                    'fields' => [
                        'email', 'password', 'password_confirm',
                        'first_name', 'last_name',
                        'newsletter', 'accept_terms',
                    ],
                    'required_fields' => [
                        'email', 'password', 'password_confirm',
                        'first_name', 'last_name',
                        'accept_terms',
                    ],
                ],
                'edit' => [
                    'auto_set_data' => true,
                    'fields' => ['first_name', 'last_name', 'address', 'zipcode', 'city', 'house_number', 'location', 'phone', 'company_name', 'company_oib', 'company_address', 'newsletter'],
                    'required_fields' => ['first_name', 'last_name', 'address', 'house_number', 'city', 'zipcode', 'location', 'phone'],
                ],
            ],
        ],
        'opens_fields' => [
            'b_same_as_shipping' => [
                true => [], // obavezno prvo true
                false => ['b_first_name', 'b_last_name', 'b_zipcode', 'b_city', 'b_location', 'b_address', 'b_house_number', 'b_phone'],
            ],
            'b_r1' => [
                true => ['b_company_oib', 'b_company_name', 'b_company_address'], // obavezno prvo true
                false => [],
            ],
        ],
    ],
    'mapping' => [
        'cart' => [
            'parcels' => [
                'items' => [
                    'item' => [
                        'basic' => [
                            'id' => 'id',
                            'code' => 'code',
                            'title' => 'title',
                            'subtitle' => 'subtitle',
                            'url' => 'url',
                            'image' => 'main_image',
                            'last_piece_sale' => 'last_piece_sale',
                            'warehouses_single_pickup_display' => 'warehouses_single_pickup_display',
                            'attributes_special' => 'attributes_special',
                            'documents' => 'documents',
                        ],
                    ]
                ]
            ]
        ]
    ],
    'webshop' => [
        'order_item_ignore_request_item_type' => true,
    ],
];