<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth page-checkout page-checkout-step1<?php $this->endblock('page_class'); ?>

<?php $this->block('h1'); ?>
	<h1 class="wc-step1-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('content_layout'); ?>
<?php foreach($products as $product_code => $product_data):?>
    <div>
        <span data-tracking_gtm_products="Checkout|<?php echo $product_data['code']; ?>">
            <span style="display: none;" data-product_code="<?php echo $product_code; ?>"><?php echo $product_data['code']; ?></span>
            <span style="display: none;" data-product_title="<?php echo $product_code ?>"><?php echo $product_data['title'] ?></span>
            <span style="display: none;" data-product_price="<?php echo $product_code;; ?>"><?php echo Utils::currency_format($product_data['price']); ?></span>
            <span style="display: none;" data-product_manufacturer_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'manufacturer_title')); ?></span>
            <span style="display: none;" data-product_category_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'category_title')); ?></span>
        </span>
    </div>
<?php endforeach;?>
<div class="auth-wrapper">

	<?php if(!empty(Arr::get($cmslabel, 'webshop_disabled'))): ?>
		<div class="webshop-disabled-note"><?php echo Arr::get($cmslabel, 'webshop_disabled'); ?></div>
	<?php else: ?>
	<?php if (!$user): ?>
		<div class="a-col a-col1 a-login-col1 wc-step-col1">
			<h2 class="a-subtitle"><?php echo Arr::get($cmslabel, 'guest_checkout', 'Želite kupiti bez registracije?'); ?></h2>
			<div class="wc-cnt"><?php echo Arr::get($cms_page, 'content'); ?></div>
			<div class="wc-col-cnt">
				<form class="form-label wc-guest-form ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login" data-tracking_save_post_data="1" data-tracking_gtm_checkout_advanced="1|<?php echo Utils::token_id(); ?>">
					<input type="hidden" name="guest_checkout" value="1" />
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<p class="field fz0 field-email">
						<label for="field-email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
						<input type="email" name="email" id="field-email" />
						<span id="field-error-email" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<button class="btn btn-gray btn-wc-guest" type="submit" name="guest_checkout" value="1"><span><?php echo Arr::get($cmslabel, 'continue_without_signup', 'Nastavi kao gost'); ?></span></button>
				</form>
			</div>
		</div>

		<div class="a-col a-col2 a-login-col2 wc-step-col2">
			<h2 class="a-subtitle"><?php echo Arr::get($cmslabel, 'have_account', 'Već imate korisnički račun?'); ?></h2>
			<div class="wc-cnt"><?php echo Arr::get($cmslabel, 'login_to_buy', 'Prijavite se putem emaila za brzu kupnju'); ?></div>
			<div class="wc-col-cnt">
				<form class="form-label wc-login-form step1 ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login" data-tracking_gtm_checkout_advanced="1|<?php echo Utils::token_id(); ?>">
					<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<input type="hidden" name="login" value="1" />
					<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
					<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>

					<p class="field field-email">
						<label for="field-email2"><?php echo Arr::get($cmslabel, 'email'); ?></label>
						<input type="email" name="email" id="field-email2" />
						<span id="field-error-email" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<p class="field field-password">
						<label for="field-password"><?php echo Arr::get($cmslabel, 'password'); ?></label>
						<input type="password" name="password" id="field-password" />
					</p>
					<p class="remember field-remember">
						<input type="checkbox" name="remember" id="id_rememberme" value="1" checked />
						<label for="id_rememberme"><?php echo Arr::get($cmslabel, 'remember'); ?></label>
					</p>
					<p class="submit wc-step1-submit">
						<button class="btn btn-gray btn-wc-login" type="submit"><span><?php echo Arr::get($cmslabel, 'login', 'Prijava'); ?></span></button>
						<span class="auth-links wc-auth-links">
							<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
						</span>
					</p>
				</form>
			</div>

			<?php if (Kohana::config('app.auth.use_usersocials') === TRUE): ?>
				<div class="clear auth-social">
					<div class="auth-social-title"><?php echo Arr::get($cmslabel, 'social_login'); ?></div>
					<?php $usersocial_base = Utils::app_absolute_url($info['lang'], 'auth', 'hybridauth', FALSE); ?>
					<?php foreach (Kohana::config('authsocial.providers') as $provider => $provider_config): ?>
						<?php if (Arr::get($provider_config, 'enabled')): ?>
							<div class="auth-social-item auth-social-<?php echo strtolower($provider); ?>"><a href="<?php echo $usersocial_base; ?>?provider=<?php echo $provider; ?>"><?php echo (Arr::get($cmslabel, 'social_'.strtolower($provider))) ? Arr::get($cmslabel, 'social_'.strtolower($provider)) : $provider; ?></a></div>
						<?php endif; ?>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>
	<?php else: ?>
		<form class="ajax_siteform" method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" id="webshop_form_login">
			<p class="loggedin-next-step"><button class="btn-big" type="submit" name="already_login" value="1"><?php echo Arr::get($cmslabel, 'goto_step2_button', 'Nastavi na sljedeći korak'); ?></button></p>
		</form>
	<?php endif; ?>
	<?php endif; ?>
</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('loyalty'); ?> <?php $this->endblock('loyalty'); ?>