<div class="shoppingcart_items_small cart-small">
	<!-- Cart items -->
	<div class="w-table-small">
		<?php $i=1; ?>
		<?php foreach ($products as $product_code => $product_data): ?>
			<?php $product_status = $products_status[$product_code]; ?>
			<div class="wwp wwp-quick" id="product-<?php echo $product_code; ?>" data-shopping_cart_preview="item">
				<div class="wwp-col wwp-col1">
					<a href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove', 6);" class="wwp-remove"><?php echo Arr::get($cmslabel, 'remove'); ?></a>
					<input class="wp-input-qty product_qty_input" type="hidden" name="qty_webshop_preview[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>">
					<figure class="wwp-image">
						<a href="<?php echo $product_data['url']; ?>"><img <?php echo Thumb::generate($product_data['main_image'], array('width' => 56, 'height' => 56, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-56.jpg')); ?> alt="<?php echo $product_data['title']; ?>" /></a>
					</figure>
				</div>
				
				<div class="wwp-col wwp-col2">
					<div class="wwp-title"><a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a></div>
					
					<div class="wwp-cnt">
						<div class="wwp-cnt-col1">
							<div class="wwp-code"><?php echo (!empty($product_data['variation'])) ? $product_data['variation_code'] : $product_data['code']; ?></div>
						</div>

						<div class="wwp-price">
							<div class="wp-price">
								<?php if ($product_status['total_basic'] > $product_status['total']): ?>
									<div<?php if(!$product_status['discount']): ?>style="display: none"<?php endif; ?> class="wwp-price-old product_total_basic" data-shoppingcart_product_total_basic="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></div>
									<div class="wwp-price-current red">
										<span class="product_total" data-display_format="%PRICE%" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="product_total_second" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									</div>
								<?php else: ?>
									<div class="wwp-price-current">
										<span class="product_total" data-display_format="%PRICE%" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="product_total_second" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									</div>
								<?php endif ?>
								<?php 
								/*FIXME PROG potrebno podesiti ispis rata za kartice -->
								<div class="wwp-price-note"><?php //echo str_replace(['%MIN%', '%MAX%'], [$card_price['min'], $card_price['max']], Arr::get($cmslabel, 'installments_price2')); ?></div>*/ 
								?>
							</div>

							<?php echo View::factory('catalog/widget/energy_badge', ['item' => $product_data, 'class' => 'wwp-attr-energy']); ?>
							<?php if(!empty($product_status['extra_price_lowest']) AND $product_status['extra_price_lowest'] > 0): ?>
								<div class="wwp-lowest-price">
									<?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
									<?php echo Utils::currency_format($product_status['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
									<?php echo Utils::get_second_pricetag_string($product_status['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
								</div>
							<?php endif; ?>
						</div>
					</div>			
				</div>
			</div>
			<?php $i++; ?>
		<?php endforeach; ?>
	</div>
</div>