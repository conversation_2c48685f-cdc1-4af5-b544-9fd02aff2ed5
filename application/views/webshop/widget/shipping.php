<?php
$shippings = Webshop::shippings(['lang' => $info['lang']]);
$selected_shipping = $shopping_cart_info['selected_shipping'];

$shipping_address = Html::customer_address($customer_data, $info['lang'], Arr::extract($cmslabel, ['oib']));
$shipping_address .= '<br><a href="'.Utils::app_absolute_url($info['lang'], 'webshop', 'shipping').'#webshop_form" class="btn-change-address"><span>'.Arr::get($cmslabel, 'change_shipping_address', 'Promijeni adresu dostave').'</span></a>';
?>

<p class="global-error <?php if (!$selected_shipping): ?>active<?php endif; ?>" data-shipping_none="1"><?php echo Arr::get($cmslabel, 'none_shipping'); ?></p>

<?php foreach ($shippings AS $shipping): ?>
	<div class="shipping-row <?php if (!isset($shopping_cart_info['available_shippings_option'][$shipping['id']])): ?>hidden<?php endif; ?><?php if(count($shippings) <= 1): ?> single-shipping<?php endif; ?>">
		<?php $shipping_selected = ($selected_shipping == $shipping['id'] AND isset($shopping_cart_info['available_shippings_option'][$shipping['id']])); ?>
		<span class="shipping_info shipping_info_<?php echo $shipping['id']; ?> price cart_info_total_extra_shipping" data-ignore_showhide="1" <?php if ($shipping_selected): ?>style="display: none"<?php endif; ?>><?php if (!$shopping_cart_info['total_extra_shipping']): ?><?php echo Arr::get($cmslabel, 'free'); ?><?php else: ?><?php echo Utils::currency_format($shopping_cart_info['total_extra_shipping'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?><?php endif; ?></span>
		<input type="radio" name="shipping" value="<?php echo $shipping['id']; ?>" id="field-shipping-<?php echo $shipping['id']; ?>" <?php if ($shipping_selected): ?>checked<?php endif; ?><?php if (!isset($shopping_cart_info['available_shippings_option'][$shipping['id']])): ?>disabled<?php endif; ?>>
		<label for="field-shipping-<?php echo $shipping['id']; ?>" class="<?php if(empty($shipping['show_shipping_address'])): ?>hide<?php endif; ?>"><?php echo $shipping['title']; ?></label>
		<div class="shipping-data shipping_info shipping_info_<?php echo $shipping['id']; ?><?php if(empty($shipping['show_shipping_address'])): ?> custom<?php endif; ?>" <?php if (!$shipping_selected): ?>style="display: none"<?php endif; ?>>
			<?php if ($shipping['show_shipping_address']): ?>
				<?php if ($shipping['description']): ?>
					<?php echo $shipping['description']; ?><br/><br/>
				<?php endif; ?>
				<?php echo $shipping_address; ?>
			<?php else: ?>
				<div class="shipping-restriction-note payment">
                    <?php echo $shipping['description']; ?>
                    <br><a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'shipping')?>#webshop_form" class="btn-change-address"><span> <?php echo Arr::get($cmslabel, 'change_shipping_address', 'Promijeni adresu dostave'); ?></span></a>
                </div>
			<?php endif; ?>
			<?php if (isset($shipping['widget_content']) AND $shipping['widget_content']): ?>
				<?php echo $shipping['widget_content']; ?>
			<?php endif; ?>
		</div>
	</div>
<?php endforeach; ?>