<div class="shoppingcart_items_small clear">

	<!-- Cart items -->
	<div class="ww-preview-items shopping_cart_preview_items">
		<?php echo View::factory('webshop/widget/shopping_cart_items_small_quick', array('shopping_cart_info' => $shopping_cart, 'products' => $shopping_cart_products, 'products_status' => $shopping_cart_status)); ?>
	</div>

	<div class="ww-totals-cnt">
		<!-- Totals -->
		<div class="ww-totals">
			<?php if (isset($shopping_cart['total_extra_discount'])):?>
				<div class="ww-discount cart_info_total_extra_discount_box" <?php if ($shopping_cart['total_extra_discount'] == 0): ?>style="display: none"<?php endif; ?>>
					<span class="ww-label"><?php echo Arr::get($cmslabel, 'discount'); ?>:</span>
					<span class="ww-value cart_info_total_extra_discount">
						<?php echo Utils::currency_format($shopping_cart['total_extra_discount'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_discount'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</span>
				</div>
			<?php endif; ?>
			<div class="ww-totals-shipping">
				<span class="ww-label"><?php echo Arr::get($cmslabel, 'shipping'); ?>:</span>
				<span class="ww-value cart_info_total_extra_shipping">
					<?php if ($shopping_cart['total_extra_shipping'] == 0): ?>
						<span><?php echo Arr::get($cmslabel, 'free'); ?></span>
					<?php else: ?>
						<?php echo Utils::currency_format($shopping_cart['total_extra_shipping'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					<?php endif; ?>
				</span>
			</div>
			<div class="ww-total">
				<span class="ww-label"><?php echo Arr::get($cmslabel, 'totals', 'Sveukupno'); ?>:</span>
				<span class="ww-value cart_info_total total_price">
					<?php echo Utils::currency_format($shopping_cart['total'] * $currency['exchange'], $currency['display']); ?>
					<?php echo Utils::get_second_pricetag_string($shopping_cart['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
				</span>
			</div>
		</div>

		<!-- Min order alert -->
		<?php if (isset($shopping_cart['total_extra_shipping_min_total_error']) AND $shopping_cart['total_extra_shipping_min_total_error']): ?>
			<div class="ww-minprice minprice-tooltip" style="display:none;">
				<?php
				echo str_replace(array(
					'%TOTAL_MIN%',
					'%TOTAL_MISSING%',
						), array(
					Utils::currency_format($shopping_cart['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
					Utils::currency_format($shopping_cart['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
						), Arr::get($cmslabel, 'minimal_order_price_full'));
				?>
			</div>
		<?php endif; ?>

		<div class="ww-preview-footer">
			<div class="ww-preview-btns">
				<a class="btn btn-green ww-btn-view" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><span><?php echo Arr::get($cmslabel, 'view_shopping_cart'); ?></span></a>
			</div>

			<div class="ww-preview-note cart_info_total_extra_shipping_to_free_box"<?php if (empty($shopping_cart['total_extra_shipping_to_free'])): ?> style="display: none"<?php endif; ?>>
				<span class="ww-preview-note-label"><?php echo Arr::get($cmslabel, 'min_total_missing'); ?> 
					<strong class="w-missing-shipping-value cart_info_total_extra_shipping_to_free">
						<?php echo Utils::currency_format($shopping_cart['total_extra_shipping_to_free'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_to_free'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</strong>
				</span>
			</div>

			<div class="free-delivery-missing cart_info_total_extra_shipping_to_free_box" <?php if ($shopping_cart['total_extra_shipping_to_free'] == 0): ?> style="display: none"<?php endif; ?>>
				<span class="free-delivery-missing-bg cart_info_total_extra_shipping_to_free_percent" style="width: <?php echo Arr::get($shopping_cart, 'total_extra_shipping_to_free_percent'); ?>;"></span>
				<span class="free-delivery-missing-num">
					<span class="cart_info_total_items_total">
						<?php echo Utils::currency_format($shopping_cart['total_items_total'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($shopping_cart['total_items_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</span> &nbsp;/&nbsp; 
					<span class="cart_info_total_extra_shipping_free_above">
						<?php echo Utils::currency_format($shopping_cart['total_extra_shipping_free_above'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_free_above'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</span>
				</span>
			</div>

			<div class="ww-preview-free-delivery"><?php echo Arr::get($cmslabel, 'free_delivery'); ?></div>
		</div>
	</div>
</div>