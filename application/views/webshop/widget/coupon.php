<?php $mode = isset($mode) ? $mode : ''; ?>
<?php if (Kohana::config('app.webshop.use_coupons')): ?>
	<?php $have_coupon = (isset($shopping_cart_info['total_extra_coupon_code']) AND count($shopping_cart_info['total_extra_coupon_code'])); ?>
	<?php $have_coupon_product = (isset($shopping_cart_info['total_extra_coupon_product_code']) AND count($shopping_cart_info['total_extra_coupon_product_code'])); ?>
	<div class="ww-coupons<?php if ($have_coupon OR $have_coupon_product): ?> active<?php endif; ?>" data-coupon_active="webshop_coupon">
		<?php if($mode == 'checkout'): ?>
			<div class="wc-coupons-header" onclick="toggleBox(['.wc-coupons-body', '.wc-btn-toggle-coupons']);">
				<div class="wc-coupons-header-label"><?php echo Arr::get($cmslabel, 'coupon_have_coupon', 'Imaš kupon ili poklon bon'); ?></div><span class="wc-btn-toggle wc-btn-toggle-coupons"><?php echo Arr::get($cmslabel, 'toggle_coupon'); ?><span class="toggle-icon"></span></span>
			</div>
		<?php endif; ?>

		<div class="wc-coupons-body">
			<!-- Add coupon form -->
			<div class="ww-coupons-form">
				<?php if($mode != 'checkout'): ?>
					<label for="coupon_code" class="ww-coupons-label"><?php echo Arr::get($cmslabel, 'coupon_have_coupon', 'Imaš kupon ili poklon bon'); ?><span class="wc-btn-toggle wc-btn-toggle-coupons"><?php echo Arr::get($cmslabel, 'toggle_coupon'); ?><span class="toggle-icon"></span></span></label>
				<?php endif; ?>
				<div class="ww-coupons-add">
					<input type="text" name="coupon_code" id="coupon_code" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
					<a class="btn btn-blue ww-btn-add" href="javascript:cmscoupon.set('webshop_coupon')"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
					<div class="coupon_message" style="display: none"></div>
				</div>
			</div>

			<!-- Used coupon -->
			<div class="ww-coupons-active">
				<span class="ww-coupons-title"><?php echo str_replace('%COUPON_CODE%', implode(',', $shopping_cart_info['total_extra_coupon_code']), Arr::get($cmslabel, 'coupon_included_code')); ?></span>
				<a class="ww-coupon-delete" href="javascript:cmscoupon.remove('webshop_coupon', '_all');"><span><?php echo Arr::get($cmslabel, 'coupon_remove', 'Ukloni'); ?></span></a>
                <div class="coupon_message_second" style="display: none"></div>
			</div>

			<!-- List of available coupons -->
			<?php $coupons = Webshop::coupons(array('lang' => $info['lang'], 'only_my' => TRUE, 'user_email' => $info['user_email'])); ?>
			<?php if($coupons): ?>
				<div class="ww-coupons-list">
					<h5 class="ww-coupons-list-title"><?php echo Arr::get($cmslabel, 'coupon_available', 'Dostupni kuponi'); ?></h5>
					<table class="ww-coupons-table">
					<?php foreach ($coupons AS $coupon): ?>	
						<tr class="<?php if (!empty($active_coupons) AND in_array($coupon['code'], $active_coupons)): ?>active<?php endif; ?>" data-coupon_active="webshop_coupon_<?php echo $coupon['code']; ?>">
							<td class="col-code"><?php echo $coupon['code']; ?></td>
							<td class="col-type"><?php echo ($coupon['type'] == 'f') ? Utils::currency_format($coupon['coupon_price']).Utils::get_second_pricetag_string($coupon['coupon_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']) : ($coupon['coupon_percent']*100).'%'; ?></td>
							<td class="col-link">
								<a class="btn-coupon-add" href="javascript:cmscoupon.set('webshop_coupon', '<?php echo $coupon['code']; ?>');"><?php echo Arr::get($cmslabel, 'coupon_use', 'Koristi'); ?></a>
								<a class="btn-coupon-remove" href="javascript:cmscoupon.remove('webshop_coupon', '<?php echo $coupon['code']; ?>');"><?php echo Arr::get($cmslabel, 'coupon_remove', 'Ukloni'); ?></a>
							</td>
						</tr>
					<?php endforeach; ?>
					</table>
				</div>	
			<?php endif; ?>	
		</div>
	</div>
<?php endif; ?>