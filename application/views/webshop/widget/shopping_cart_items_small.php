<div class="shoppingcart_items_small cart-small">
	<!-- Cart items -->
	<div class="w-table-small">
		<?php $i=1; ?>
		<?php foreach ($products as $product_code => $product_data): ?>
			<?php $product_status = $products_status[$product_code]; ?>
			<div class="wwp" id="product-<?php echo $product_code; ?>" data-shopping_cart_preview="item">
				<div class="wwp-col wwp-col1">
					<figure class="wwp-image">
						<a href="<?php echo $product_data['url']; ?>"><img <?php echo Thumb::generate($product_data['main_image'], array('width' => 100, 'height' => 100, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-100.jpg')); ?> alt="<?php echo $product_data['title']; ?>" /></a>
					</figure>
				</div>
				
				<div class="wwp-col wwp-col2">
					<div class="wwp-title"><a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a></div>
					
					<div class="wwp-cnt">
						<div class="wwp-cnt-col1">
							<div class="wwp-code"><span class="wwp-label"><?php echo Arr::get($cmslabel, 'code'); ?>:</span> <?php echo (!empty($product_data['variation'])) ? $product_data['variation_code'] : $product_data['code']; ?></div>
							<?php if (!empty($product_data['variation'])): ?>
								<div class="wwp-atributes">
									<?php if ($product_data['variation_attributes']): ?>
										<?php echo $product_data['variation_attributes']; ?>
									<?php elseif ($product_data['variation_title']): ?>
										<?php echo $product_data['variation_title']; ?>
									<?php elseif ($product_data['variation_code']): ?>
										<?php echo $product_data['variation_code']; ?>
									<?php endif; ?>
								</div>
							<?php endif; ?>
							
							<div class="wwp-coupons">
								<?php if (!empty($product_data['coupon_price'])): ?>
									<?php echo Arr::get($cmslabel, 'coupon_value'); ?>: <?php echo $product_data['coupon_price']; ?>
								<?php elseif (!empty($product_data['bonus_total'])): ?>
									<?php echo Arr::get($cmslabel, 'bonus_total_value'); ?>: <?php echo $product_data['bonus_total']; ?>
								<?php endif; ?>
							</div>
						</div>

						<div class="wwp-price">
							<div class="wp-price">
								<?php if ($product_status['total_basic'] > $product_status['total']): ?>
									<div<?php if(!$product_status['discount']): ?>style="display: none"<?php endif; ?> class="wwp-price-old product_total_basic" data-shoppingcart_product_total_basic="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_basic'] * $currency['exchange'], $currency['display']); ?></div>
									<div class="wwp-price-current red">
										<span class="product_total" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="product_total_second" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
									</div>
								<?php else: ?>
									<div class="wwp-price-current">
										<span class="product_total" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
										<span class="product_total_second" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="conversion"><?php echo Utils::get_second_pricetag_string($product_status['total'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
									</div>
								<?php endif ?>
							</div>
							<div class="wwp-price-count">
								<span class="product_qty"><?php echo $product_status['qty']; ?></span> x 
								<span data-shoppingcart_product_price="<?php echo $product_code; ?>">
									<?php echo Utils::currency_format($product_status['price'] * $currency['exchange'], $currency['display']); ?>
									<span><?php echo Utils::get_second_pricetag_string($product_status['price'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
								</span>
							</div>
							<?php if (Kohana::config('app.catalog.multitax')): ?>
								<div class="wwp-multitax"><?php echo Arr::get($cmslabel, 'tax'); ?> <?php echo ($product_status['tax'] * 100); ?>%</div>
							<?php endif; ?>
						</div>
					</div>			
				</div>
			</div>
			<?php $i++; ?>
		<?php endforeach; ?>
	</div>
</div>