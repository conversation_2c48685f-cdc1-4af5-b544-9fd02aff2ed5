<div class="ww cart<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1" data-shoppingcart_url="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>">
	<a class="ww-items" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>">
		<div class="cart-empty"><?php echo Arr::get($cmslabel, 'ww_empty'); ?></div>
		<span class="ww-counter">
			<span class="value total-items cart_info_item_count total_items"><?php echo $shopping_cart['item_count']; ?></span> kom
		</span>
		<span class="value cart_info_total_items total_price price">
			<?php echo Utils::currency_format($shopping_cart['total_items'] * $currency['exchange'], $currency['display']); ?><br>
			<?php echo Utils::get_second_pricetag_string($shopping_cart['total_items'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?>
		</span>
	</a>

	<!-- Cart preview -->
	<div class="ww-preview shopping_cart_preview" style="display: none;">
		<?php echo View::factory('webshop/widget/shopping_cart_preview', [
				'shopping_cart' => $shopping_cart,
				'shopping_cart_products' => $shopping_cart_products,
				'shopping_cart_status' => $shopping_cart_status,
			]);
		?>
	</div>

	<!-- Min order alert -->
	<span class="cart_info_total_items_base" style="position: absolute; top: -500px;"><?php echo @$shopping_cart['total_items_base']; ?></span>
</div>