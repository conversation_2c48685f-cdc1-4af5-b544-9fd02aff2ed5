<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-checkout-step3<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('header'); ?>
	<div class="wc-header">
		<div class="wc-wrapper">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('content_layout'); ?>
	<div class="wc-container">
		<div class="w-sidebar-container">
			<div class="w-sidebar-support">
				<p class="sidebar-help-title"><?php echo Arr::get($cmslabel, 'help_and_support_title'); ?></p>
				<?php echo Arr::get($cmslabel, 'help_and_support'); ?>
				<div class="w-sidebar-cards"><?php echo Arr::get($cmslabel, 'cards'); ?></div>
			</div>

			<div class="w-sidebar-note">
				<?php echo Arr::get($cmslabel, 'safe_purchase'); ?>
				<div class="w-sidebar-payment-note">
					<?php echo Arr::get($cmslabel, 'payment_note'); ?>
				</div>
			</div>
		</div>


		<?php if(!empty(Arr::get($cmslabel, 'webshop_disabled'))): ?>
			<div class="webshop-disabled-note"><?php echo Arr::get($cmslabel, 'webshop_disabled'); ?></div>
		<?php else: ?>
		<form class="step3 form-label ajax_siteform ajax_siteform_loading form-cart" action="#webshop_form" method="post" name="webshop" id="webshop_form" data-tracking_gtm_checkout="3|<?php echo Utils::token_id(); ?>">
			<!-- Steps -->
			<?php echo View::factory('webshop/widget/step', ['current_step' => 3]); ?>

			<!-- Column 1 -->
			<div class="wc-col wc-col1 wc-step3-col1">
				<?php //echo Arr::get($cms_page, 'content'); ?>

				<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'choose_payment_type'); ?></h2>
				<!-- Payment options -->
				<div class="payment-options">
					<?php foreach ($customer_fields as $field): ?>
						<?php if (isset($errors[$field][0]) AND ($error = $errors[$field][0])): ?>
							<p class="error"><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></p>
						<?php endif; ?>
						<div class="field-<?php echo $field; ?><?php if(count($available_payments) <= 1): ?> single-payment<?php endif; ?>">
							<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?> *<?php endif; ?></label>
							<?php if ($field == 'payment'): ?>
								<?php if (sizeof($available_payments_none) == 1): ?>
									<div class="cart_info_payment_box_normal <?php if ($shopping_cart_info['total'] > 0): ?>active<?php endif; ?>">
										<?php echo Form::select_as_radio2($field, $available_payments, ($shopping_cart_info['total'] > 0) ? $data->payment->id : ''); ?>
									</div>
									<div class="cart_info_payment_box_0 <?php if ($shopping_cart_info['total'] == 0): ?>active<?php endif; ?>">
										<?php echo Form::select_as_radio2($field, $available_payments_none, ($shopping_cart_info['total'] == 0) ? key($available_payments_none) : ''); ?>
									</div>
								<?php else: ?>
									<?php echo Form::select_as_radio2($field, $available_payments, $data->payment->id); ?>
								<?php endif; ?>
							<?php else: ?>
								<?php echo $data->input($field, 'form'); ?>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>
				</div>

				<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'shipping_type', 'Način dostave'); ?></h2>

				<?php if ( ! Kohana::config('app.webshop.bill_on_shipping_step')): ?>
					<fieldset>
						<?php if (sizeof($customer_bill_fields)): ?>
							<?php foreach ($customer_bill_fields as $field): ?>
								<?php if (isset($errors[$field][0]) AND ($error = $errors[$field][0])): ?>
									<p class="error"><?php echo Arr::get(@$cmslabel, "error_{$error}", $error); ?></p>
								<?php endif; ?>
								<p class="field-<?php echo $field; ?>">
									<label for="field-<?php echo $field; ?>"><?php echo @$cmslabel[substr($field, 2, strlen($field))]; ?><?php if (in_array($field, $request_fields)): ?> *:<?php endif; ?></label>
									<?php echo $data->input($field, 'form'); ?>
								</p>
							<?php endforeach; ?>
							<?php foreach ($default_bill_value as $field => $value): ?>
								<input type="hidden" name="default-<?php echo $field; ?>" value="<?php echo $value; ?>" />
							<?php endforeach; ?>
						<?php else: ?>
							<?php echo Arr::get($cmslabel, 'same_as_shipping'); ?>
						<?php endif; ?>
					</fieldset>
				<?php endif; ?>

				<div class="section-bill">
					<?php if (Kohana::config('app.webshop.review_order_on_payment_step')): ?>
						<div class="section-shipping">
							<?php echo View::factory('webshop/widget/shipping', array('shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data)); ?>
						</div>

						<?php if (Arr::get($customer_data, 'b_same_as_shipping') != 1 AND Arr::get($customer_data, 'b_first_name')): ?>
							<div class="wc-bill-address">
								<p><strong><?php echo Arr::get($cmslabel, 'bill_address'); ?></strong></p>

								<?php if(Arr::get($customer_data, 'b_first_name') OR Arr::get($customer_data, 'b_last_name')): ?>
									<p><?php echo Arr::get($customer_data, 'b_first_name'); ?> <?php echo Arr::get($customer_data, 'b_last_name'); ?></p>
								<?php endif; ?>

								<?php if(Arr::get($customer_data, 'b_oib')): ?>
									<p><?php echo Arr::get($cmslabel, 'oib'); ?>: <?php Arr::get($customer_data, 'b_oib'); ?></p>
								<?php endif; ?>

								<?php if(Arr::get($customer_data, 'b_address')): ?>
									<?php $address = Arr::get($customer_data, 'b_address'); ?>
                                    <?php if(Arr::get($customer_data, 'b_house_number', false)): ?>
                                        <?php $address .= ' ' . Arr::get($customer_data, 'b_house_number'); ?>
                                    <?php endif; ?>
									<p><?php echo $address; ?></p>
								<?php endif; ?>

								<?php if(Arr::get($customer_data, 'b_zipcode') OR Arr::get($customer_data, 'b_city')): ?>
									<p><?php echo Arr::get($customer_data, 'b_zipcode'); ?> <?php echo Arr::get($customer_data, 'b_city'); ?></p>
								<?php endif; ?>

								<?php if(Arr::get($customer_data, 'b_country')): ?>
									<p><?php echo (Arr::get($customer_data, 'b_country')) ? Jelly::query('webshopcountry', Arr::get($customer_data, 'b_country'))->select()->description($info['lang']) : ''; ?></p>
								<?php endif; ?>

								<?php if(Arr::get($customer_data, 'b_email')): ?>
									<p><?php echo Arr::get($customer_data, 'b_email'); ?></p>
								<?php endif; ?>
							</div>
						<?php endif; ?>

						<?php if (Arr::get($customer_data, 'b_r1') == 1): ?>
							<div class="wc-r1">
								<p><strong><?php echo Arr::get($cmslabel, 'r1'); ?></strong></p>
								<?php if(!empty(Arr::get($customer_data, 'b_company_name'))): ?>
									<p><?php echo Arr::get($cmslabel, 'company_name'); ?>: <?php echo Arr::get($customer_data, 'b_company_name'); ?></p>
								<?php endif; ?>
								<?php if(!empty(Arr::get($customer_data, 'b_company_oib'))): ?>
									<p><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo Arr::get($customer_data, 'b_company_oib'); ?></p>
								<?php endif; ?>
								<?php if(Arr::get($customer_data, 'b_company_address')): ?>
									<p><?php echo Arr::get($cmslabel, 'company_address'); ?>: <?php echo Arr::get($customer_data, 'b_company_address'); ?></p>
								<?php endif; ?>
							</div>
						<?php elseif (Arr::get($customer_data, 'b_company_name')): ?>
							<div class="wc-r1">
								<br/><br/><?php echo Arr::get($cmslabel, 'company_name'); ?>: <?php echo Arr::get($customer_data, 'b_company_name'); ?>
								<br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo Arr::get($customer_data, 'b_company_oib'); ?>
								<br/><?php echo Arr::get($cmslabel, 'company_address'); ?>: <?php echo Arr::get($customer_data, 'b_company_address'); ?>
							</div>
						<?php endif; ?>
					<?php endif; ?>
				</div>

				<!-- Coupons -->
				<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info, 'mode' => 'checkout')); ?>

				<!-- Shopping cart -->
				<div class="wc-cart-title" onclick="toggleBox(['.wc-cart-container', '.wc-btn-toggle-cart']);">
					<?php echo Arr::get($cmslabel, 'cart_totals_title', 'Ukupno u košarici'); ?>:
					<strong class="cart_info_total">
						<?php echo Utils::currency_format($shopping_cart_info['total'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($shopping_cart_info['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</strong>
					<span class="wc-btn-toggle wc-btn-toggle-cart"><span class="o"><?php echo Arr::get($cmslabel, 'toggle_cart_open'); ?></span><span class="c"><?php echo Arr::get($cmslabel, 'toggle_cart_close'); ?></span><span class="toggle-icon"></span></span>
					<div class="wc-shipping-remaining cart_info_total_extra_shipping_to_free_box"<?php if ($shopping_cart['total_extra_shipping_to_free'] == 0): ?> style="display: none"<?php endif; ?>>
						<span>
							<?php echo Arr::get($cmslabel, 'min_total_missing'); ?>
							<span class="wc-shipping-remaining-value cart_info_total_extra_shipping_to_free">
								<?php echo Utils::currency_format($shopping_cart['total_extra_shipping_to_free'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_to_free'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</span>
						</span>
					</div>
				</div>
				<div class="wc-cart-container">
					<?php echo View::factory('webshop/widget/shopping_cart_items_small', array('shopping_cart_info' => $shopping_cart_info, 'products' => $products, 'products_status' => $products_status)); ?>

					<!-- Totals -->
					<div class="w-totals-cnt">
						<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>
						<a class="w-btn-change btn-arrow" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>"><span><?php echo Arr::get($cmslabel, 'change_cart', 'Promijeni sadržaj košarice'); ?></span></a>
					</div>
				</div>

				<div class="step3-message">
					<p class="field step3-field-message">
						<label for="field-message" class="label-message"><?php echo Arr::get($cmslabel, 'checkout_message', 'Napomena'); ?></label>
						<textarea id="field-message" name="message" cols="40" rows="8" class="field_text field_text_message"><?php echo Arr::get($customer_data, 'message'); ?></textarea>
						<span id="field-error-message" class="field_error error" style="display: none"></span>
					</p>
				</div>

				<div class="clear wc-section-finish">
					<div class="global_error global-error"<?php if ( ! count($errors)): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>
						<div class="minprice-tooltip">
						<?php echo str_replace(array(
								'%TOTAL_MIN%',
								'%TOTAL_MISSING%',
								),
							array(
								Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
								Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
							),
							Arr::get($cmslabel, 'minimal_order_price_full'));
						?>
						</div>
						<a href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', ''); ?>" class="btn btn-green btn-checkout btn-finish"><?php echo Arr::get($cmslabel, 'edit_shopping_cart'); ?></a>
					<?php else: ?>
						<?php if (isset($accept_terms_field) AND isset($accept_terms_error)): ?>
							<div class="webshop-alert-terms" data-accept_terms="note"><?php echo Arr::get($cmslabel, 'terms_error'); ?></div>
						<?php endif; ?>
						
						<div class="wc-step3-btns-container">
							<div class="wc-terms">
								<?php if (isset($accept_terms_field) AND isset($accept_terms_error)): ?>
									<?php /*
									<div class="terms-container">
										<div class="newwindow"><?php echo Arr::get($cmslabel, 'accept_terms_new_window'); ?></div>
										<iframe src="<?php echo Arr::get($cmslabel, 'terms_quick_url'); ?>" scrolling="no" frameborder="0" width="100%" height="85"></iframe>
									</div>
									*/ ?>

									<?php echo View::factory('webshop/widget/accept_terms', array('accept_terms_field' => $accept_terms_field, 'accept_terms_error' => $accept_terms_error)); ?>
								<?php endif; ?>

								<?php if (isset($accept_terms_2_field) AND isset($accept_terms_2_error)): ?>
									<?php echo View::factory('webshop/widget/accept_terms_2', array('accept_terms_field' => $accept_terms_2_field, 'accept_terms_error' => $accept_terms_2_error)); ?>
								<?php endif; ?>

								<div class="webshop_newsletter webshop-accept-terms webshop-newsletter">
									<?php echo Form::checkbox('newsletter', '1', Arr::get($_POST, 'newsletter', FALSE), ['id' => 'field-newsletter']); ?>
									<label for="field-newsletter"><span><?php echo Arr::get($cmslabel, 'newsletter', 'Želim primati newsletter'); ?></span></label>
								</div>
							</div>

							<div class="clear wc-btns step3-wc-btns">
								<button type="submit" class="btn btn-checkout btn-finish" data-accept_terms_checked="1"><?php echo Arr::get($cmslabel, 'confirm_order'); ?></button>
							</div>
						</div>
					<?php endif; ?>
				</div>
			</div>
		</form>
	<?php endif; ?>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('footer'); ?> <?php $this->endblock('footer'); ?>
<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>