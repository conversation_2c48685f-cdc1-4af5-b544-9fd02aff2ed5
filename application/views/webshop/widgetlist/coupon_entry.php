<tr>
	<td class="col-code"><?php echo $item['code']; ?></td>
	<td class="col-description"><?php echo ($item['description']) ? $item['description'] : '-'; ?></td>
	<td class="col-value">-<?php echo ($item['type'] == 'f') ? Utils::currency_format($item['coupon_price']).Utils::get_second_pricetag_string($item['coupon_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']) : ($item['coupon_percent']*100).'%'; ?></td>
	<?php if ($item['active']): ?>
		<td class="col-valid">
			<?php if ($item['datetime_expire']): ?>
				<?php echo date('d.m.Y, H:i', $item['datetime_expire']); ?>h
			<?php else: ?>
				-
			<?php endif; ?>
		</td>
	<?php else: ?>
		<td class="col-valid">
			<?php if ($item['used']): ?>
				iskorišten
			<?php else: ?>
				<?php if ($item['datetime_expire'] AND $item['datetime_expire'] < time()): ?>istekao <?php echo date('d.m.Y', $item['datetime_expire']); ?><?php else: ?>-<?php endif; ?>
			<?php endif; ?>
		</td>
	<?php endif; ?>
</tr>
