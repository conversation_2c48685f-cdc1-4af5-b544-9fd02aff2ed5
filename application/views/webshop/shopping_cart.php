<?php $this->extend('default'); ?>
<?php $this->block('page_class'); ?> whitebg<?php $this->endblock('page_class'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content_layout'); ?>

	<?php if(!empty(Arr::get($cmslabel, 'webshop_disabled'))): ?>
		<div class="webshop-disabled-note"><?php echo Arr::get($cmslabel, 'webshop_disabled'); ?></div>
	<?php else: ?>
	<?php if (sizeof($products)): ?>
		<div class="clear w-cart" id="view_cart">
			<div class="w-col w-col1">
				<h1 class="w-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?><?php if(sizeof($products)): ?><span class="w-counter<?php if ($shopping_cart['total_items']): ?> active<?php endif; ?>" data-shoppingcart_active="1">(<span class="cart_info_item_count"><?php echo $shopping_cart['item_count']; ?></span>)</span><?php endif; ?></h1>
				<?php echo Arr::get($cms_page, 'content'); ?>

				<form action="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'set_qty', FALSE); ?>" method="post" name="shopping_cart">
					<?php echo View::factory('webshop/widget/loyalty_new', array('shopping_cart_info' => $shopping_cart_info)); ?>
					<!-- Cart table -->
					<div class="w-table">
						<?php $i=1; ?>
						<?php foreach ($products as $product_code => $product_data): ?>
                            <span data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $product_data['code']; ?>">
	                        <span style="display: none;" data-product_code="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'code'); ?></span>
	                        <span style="display: none;" data-product_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'title'); ?></span>
	                        <span style="display: none;" data-product_category_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'category_title'); ?></span>
<!--	                        <span style="display: none;" data-product_category="--><?php //echo $product_code; ?><!--">--><?php //echo 'Kategorija ' . Arr::get($product_data, 'category_title'); ?><!--</span>-->
	                        <span style="display: none;" data-product_manufacturer_title="<?php echo $product_code; ?>"><?php echo Arr::get($product_data, 'manufacturer_title'); ?></span>
	                        <span style="display: none;" data-product_price="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_data['price']); ?></span>
                            </span>
							<?php $product_status = $products_status[$product_code]; ?>
							<div class="wp" id="product_details_<?php echo $product_code; ?>">
								<input type="hidden" name="product_price" value="<?php echo $product_status['price'] ?>" />
								<input type="hidden" name="product_total" value="<?php echo $product_status['total'] ?>" />
								<div class="wp-image">
									<a href="<?php echo $product_data['url']; ?>"><img <?php echo Thumb::generate($product_data['main_image'], array('width' => 100, 'height' => 100, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-100.jpg')); ?> alt="<?php echo $product_data['title']; ?>" /></a>
									<a class="wp-btn-delete wp-btn-delete-m" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');"><span><?php echo Arr::get($cmslabel, 'remove_product', 'Ukloni'); ?></span></a>
								</div>
								<div class="wp-row-col2">
									<h2 class="wp-title">
										<a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>
										<?php if ($product_data['attributes']): ?>
											<span class="wp-attribute"><?php echo $product_data['attributes']; ?></span>
										<?php endif; ?>
									</h2>
									<div class="wp-cnt">
										<div class="wp-attrs">
											<div class="wp-title-container">
												<div class="wp-code"><?php echo (!empty($product_data['variation_code'])) ? $product_data['variation_code'] : $product_data['code']; ?></div>
												<?php if (!empty($product_data['variation'])): ?>
													<div class="wp-variations">
														<?php if ($product_data['variation_attributes']): ?>
															<?php $variation_items = explode(',', $product_data['variation_attributes']) ?>
															<?php foreach ($variation_items as $variation_item): ?>
																<p><?php echo $variation_item ?></p>
															<?php endforeach; ?>
														<?php elseif ($product_data['variation_title']): ?>
															<?php echo $product_data['variation_title']; ?>
														<?php elseif ($product_data['variation_code']): ?>
															<?php echo $product_data['variation_code']; ?>
														<?php endif; ?>
													</div>
												<?php endif; ?>

												<?php if (!empty($product_data['coupon_price'])): ?>
													<?php echo Arr::get($cmslabel, 'coupon_value', 'Vrijednost kupona'); ?>: <?php echo $product_data['coupon_price']; ?>
												<?php elseif (!empty($product_data['bonus_total'])): ?>
													<?php echo Arr::get($cmslabel, 'bonus_total_value'); ?>: <?php echo $product_data['bonus_total']; ?>
												<?php elseif ($product_data['type'] == 'download'): ?>
													<?php echo Arr::get($cmslabel, 'download_files', 'Preuzimanje datoteka'); ?>
												<?php endif; ?>
											</div>

											<a class="wp-btn-delete wp-btn-delete-d" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', 'remove');"><span><?php echo Arr::get($cmslabel, 'remove_product', 'Obriši'); ?></span></a>
										</div>
										<div class="wp-qty-container">
											<div class="cd-qty wp-qty">
												<a class="btn-qty btn-qty-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '-');"></a>
												<input class="qty-input product_qty_input" tabindex="<?php echo $i; ?>" type="text" name="qty[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>" />
												<a class="btn-qty btn-qty-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '+');"></a>
												<?php $unit = (!empty($product_data['unit'])) ? $product_data['unit'] : Arr::get($cmslabel, 'unit', 'kom'); ?>
												<span class="wp-unit"><?php echo $unit; ?></span>
												<span class="wp-message product_message product_message_<?php echo $product_code; ?>" style="display: none"></span>
											</div>
										</div>
										<?php 
											$has_energy_bagde = false; 
											if(!empty($product_data['attributes_special'])) {
												foreach($product_data['attributes_special'] as $attr) {
													if($attr['attribute_code'] == 'energetski-razred') {
														$has_energy_bagde = true;
													}
												}
											}
										?>
										<div class="wp-total<?php if ($product_status['total_basic'] > $product_status['total']): ?> has-discount<?php endif; ?><?php if(!$has_energy_bagde): ?> no-energy-badge<?php endif; ?>">
											<div>
												<?php if ($product_status['total_basic'] > $product_status['total']): ?>
													<div class="wp-price-current red">
														<span class="product_total" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
													</div>
												<?php else: ?>
													<div class="wp-price-current">
														<span class="product_total" data-currency_format="full_price_currency" data-display_format="%PRICE%" data-conversion_type="noconversion"><?php echo Utils::currency_format($product_status['total'] * $currency['exchange'], $currency['display']); ?></span>
													</div>
												<?php endif ?>
												<div class="wp-qty-count"<div class="wwp-price-count" data-shoppingcart_product_qty_box="<?php echo $product_status['code']; ?>" <?php if ($product_status['qty'] == 1): ?>style="display: none"<?php endif; ?>>
													<span class="product_qty"><?php echo $product_status['qty']; ?></span> x
													<?php echo Utils::currency_format($product_status['price'] * $currency['exchange'], $currency['display']); ?>
													<span class="product-qty-second"><?php echo Utils::get_second_pricetag_string($product_status['price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
												</div>
												<?php if (Kohana::config('app.catalog.multitax')): ?>
													<div class="wp-multitax"><?php echo Arr::get($cmslabel, 'tax'); ?> <?php echo ($product_status['tax'] * 100); ?>%</div>
												<?php endif; ?>
											</div>
											<?php echo View::factory('catalog/widget/energy_badge', ['item' => $product_data, 'class' => 'wp-attr-energy']); ?>
										</div>
									</div>
								</div>
							</div>
							<?php $i++; ?>
						<?php endforeach; ?>
					</div>
				</form>

				<!-- Coupon -->
				<?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>
				<div class="clear"></div>
			</div>

			<div class="w-col w-col2">
				<div class="w-totals box-shadow">
					<div class="w-totals-title"><?php echo Arr::get($cmslabel, 'shopping_cart_total'); ?></div>
					<div class="w-totals-wrapper">
						<!-- Totals -->
						<?php echo View::factory('webshop/widget/total', array('shopping_cart_info' => $shopping_cart_info)); ?>

						<!-- Min order alert -->
						<?php if (isset($shopping_cart_info['total_extra_shipping_min_total_error']) AND $shopping_cart_info['total_extra_shipping_min_total_error']): ?>
							<div class="w-minprice minprice-tooltip" style="display:none;">
								<?php echo str_replace(array(
										'%TOTAL_MIN%',
										'%TOTAL_MISSING%',
										),
									array(
										Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
										Utils::currency_format($shopping_cart_info['total_extra_shipping_min_total_missing'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($shopping_cart_info['total_extra_shipping_min_total_missing'], ['currency_code' => $currency['code'], 'display_format' => 'standard']),
									),
									Arr::get($cmslabel, 'minimal_order_price_full'));
								?>
							</div>
						<?php endif; ?>

						<!-- Finish shopping -->
						<a class="btn btn-yellow btn-arrow btn-m w-btn-finish cart-finish-shopping" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'payment'); ?>#webshop_form"><span><?php echo Arr::get($cmslabel, 'finish_shopping', 'Dovrši kupovinu'); ?></span></a>
					</div>
				</div>

				<div class="ww-shipping">
					<div class="ww-preview-note cart_info_total_extra_shipping_to_free_box"<?php if (empty($shopping_cart['total_extra_shipping_to_free'])): ?> style="display: none"<?php endif; ?>>
						<span class="ww-preview-note-label">
							<?php echo Arr::get($cmslabel, 'min_total_missing'); ?>
							<strong class="w-missing-shipping-value cart_info_total_extra_shipping_to_free">
								<?php echo Utils::currency_format($shopping_cart['total_extra_shipping_to_free'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_to_free'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</strong>
						</span>
					</div>

					<div class="free-delivery-missing cart_info_total_extra_shipping_to_free_box" <?php if ($shopping_cart['total_extra_shipping_to_free'] == 0): ?> style="display: none"<?php endif; ?>>
						<span class="free-delivery-missing-bg cart_info_total_extra_shipping_to_free_percent" style="width: <?php echo Arr::get($shopping_cart, 'total_extra_shipping_to_free_percent'); ?>;"></span>
						<span class="free-delivery-missing-num">
							<span class="cart_info_total_items_total">
								<?php echo Utils::currency_format($shopping_cart['total_items_total'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($shopping_cart['total_items_total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</span> &nbsp;/&nbsp;
							<span class="cart_info_total_extra_shipping_free_above">
								<?php echo Utils::currency_format($shopping_cart['total_extra_shipping_free_above'] * $currency['exchange'], $currency['display']); ?>
								<?php echo Utils::get_second_pricetag_string($shopping_cart['total_extra_shipping_free_above'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</span>
						</span>
					</div>

					<div class="ww-preview-free-delivery"><?php echo Arr::get($cmslabel, 'free_delivery'); ?></div>
				</div>

				<div class="payment-note">
					<?php echo Arr::get($cmslabel, 'payment_note'); ?>
				</div>
			</div>
		</div>
		<div id="empty_shopping_cart" class="empty-cart" style="display: none;"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
	<?php else: ?>
		<div class="empty-cart"><?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?></div>
	<?php endif; ?>
	<?php endif; ?>
<?php $this->endblock('content_layout'); ?>