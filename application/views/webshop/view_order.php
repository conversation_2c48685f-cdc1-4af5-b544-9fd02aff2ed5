<?php if (!isset($skip_default_order_extension)): ?>
    <?php $this->extend('default_order'); ?>
<?php endif; ?>

<?php
$currency_display = Kohana::config('app.webshop.order_view_currency_use_strip_tags') ? strip_tags($item->currency->display) : $item->currency->display;
$currency_code = strtoupper($item->currency->code);
$use_vies = (Kohana::config('app.webshop.use_b_vies') AND $item->country->id != Webshop::default_country());
$order_no_title = (!empty(Arr::get($cmslabel, $content_category . '_no_'. $item->payment->code)))
    ? Arr::get($cmslabel, $content_category . '_no_'. $item->payment->code)
    : Arr::get($cmslabel, $content_category . '_no', 'Narudžba');
$order_view_id = Kohana::config('app.webshop.order_view_id');
$only_visible = Kohana::config('app.webshop.show_only_visible_warehouses');
$admin_location_filter = (Kohana::config('app.location.admin_location_filter'));
$show_tax_by_country = Kohana::config('app.webshop.show_tax_countries');
$hide_payment = isset($hide_payment) ? $hide_payment : false;

if (Kohana::config('app.webshop.order_display_ean_on_view_order')) {
    $ean_code = Utils::generate_ean13("00", $item->get_number());
    require_once Kohana::find_file('vendor/barcode/barcode/', 'BarcodeGenerator');
    require_once Kohana::find_file('vendor/barcode/barcode/', 'BarcodeGeneratorHTML');
    $bar = new \barcode\barcode\BarcodeGeneratorHTML();
    $ean = $bar->getBarcode($ean_code[0], $bar::TYPE_EAN_13, 1, 50, '', true);
}
?>

<?php $this->block('title'); ?><?php echo $order_no_title; ?>: <?php if(Kohana::config('app.webshop.order_use_id_as_order_number')): ?><?php echo $item->id; ?><?php else:?><?php echo $item->get_number(); ?><?php endif; ?> | <?php echo Kohana::config('app')->get('sitename'); ?><?php $this->endblock('title'); ?>

<?php $this->block('content'); ?>
<?php if ($mode == 'payment'): ?>
    <?php if ($item->show_payment_transfer()): ?>
        <?php echo View::factory('webshop/widget/payment_transfer', ['order' => $item]); ?>
        <div class="clear"></div>
    <?php else: ?>
        <a href="?"><?php echo __('Nalog za uplati nije dostupan. Vrati se na prikaz narudžbe!'); ?></a>
    <?php endif; ?>
<?php elseif ($mode == 'payment_2d_barcode'): ?>
    <?php if ($item->show_payment_transfer()): ?>
        <?php echo View::factory('webshop/widget/payment_transfer', ['order' => $item, 'mode' => '2d_barcode']); ?>
        <div class="clear"></div>
    <?php else: ?>
        <a href="?"><?php echo __('Nalog za uplati nije dostupan. Vrati se na prikaz narudžbe!'); ?></a>
    <?php endif; ?>
<?php else: ?>
    <?php if (!empty($cmslabel['order_view_header']) AND strlen($cmslabel['order_view_header']) > 10): ?>
        <table width="100%" cellpadding="0" cellspacing="0">
            <tr>
                <td align="left" valign="top" width="50%" style="padding-left: 0; margin-left: 0">
                    <?php echo $cmslabel['order_view_header']; ?>
                </td>
                <td align="right" valign="top" width="50%">
                    <?php echo Arr::get($cmslabel, 'order_logo'); ?>
                </td>
            </tr>
        </table>
        <?php if (Kohana::config('app.webshop.order_view_total_layout') == '2'): ?>
            <?php if (Kohana::config('app.webshop.order_clientmail_number') OR !empty($info['user_staff'])): ?>
                <h1 style="margin: 0; padding: 0 0 5px"><?php echo $order_no_title; ?>: <?php echo $item->get_number(); ?> / <?php echo Date::humanize($item->datetime_created); ?></h1>
                <?php if (isset($item->id) AND $item->id != $item->get_number() AND $order_view_id): ?>
                    <p><?php echo Arr::get($cmslabel, 'order_id', 'ID'); ?>: <strong><?php echo $item->id; ?></strong></p>
                <?php endif; ?>
            <?php endif; ?>
            <?php if (!empty($item->internal_number)): ?>
                <p><?php echo Arr::get($cmslabel, 'order_internal_number', 'Br. narudžbe kupca'); ?>: <strong><?php echo $item->internal_number; ?></strong></p>
            <?php endif; ?>
        <?php else: ?>
            <?php if (Kohana::config('app.webshop.order_clientmail_number') OR !empty($info['user_staff'])): ?>
                <?php if(!Kohana::config('app.webshop.order_use_id_as_order_number')): ?>
                    <h1 style="margin: 0; padding: 0 0 5px"><?php echo $order_no_title; ?>: <?php echo $item->get_number(); ?></h1>
                    <?php if (isset($item->id) AND $item->id != $item->get_number() AND $order_view_id): ?>
                        <p><?php echo Arr::get($cmslabel, 'order_id', 'ID'); ?>: <strong><?php echo $item->id; ?></strong></p>
                    <?php endif; ?>
                <?php else: ?>
                    <h1 style="margin: 0; padding: 0 0 5px"><?php echo $order_no_title; ?>: <?php echo $item->id; ?></h1>
                <?php endif; ?>
            <?php endif; ?>
            <?php if (!empty($item->internal_number)): ?>
                <p><?php echo Arr::get($cmslabel, 'order_internal_number', 'Br. narudžbe kupca'); ?>: <strong><?php echo $item->internal_number; ?></strong></p>
            <?php endif; ?>
            <p><?php echo Arr::get($cmslabel, 'order_date', __('Datum')); ?>: <?php echo Date::humanize($item->datetime_created); ?></p>
        <?php endif; ?>
        <h3 style="margin: 0 0 13px;">
            <?php echo Arr::get($cmslabel, 'status'); ?>:
            <?php if (Kohana::config('app.webshop.use_packets') AND !empty($packets = Webshop::packets_data($item->id))): ?>
                <strong><?php echo $packets['status']; ?></strong><br><br>
                <?php echo Arr::get($cmslabel, 'packet_id'); ?>:<br><br>
                <?php foreach ($packets['packets'] as $packet => $shipping_url): ?>
                    <strong><li><?php echo $packet; ?></li></strong>
                <?php endforeach; ?>
                <br />
                <a href="<?php echo $shipping_url; ?>"><?php echo Arr::get($cmslabel, 'shipping_url_title'); ?></a>
            <?php else: ?>
                <strong><?php echo Utf8::strtolower($item->status->title($item->lang)); ?></strong><?php if (($status_description = $item->status->description($item->lang))): ?> (<?php echo $status_description; ?>)<?php endif; ?>
                <?php if (isset($item->order->number)): ?>
                    <br/><?php echo Arr::get($cmslabel, 'order_no'); ?>: <?php echo $item->order->number; ?>
                <?php endif; ?>
            <?php endif; ?>
        </h3>
    <?php elseif (!empty($cmslabel['order_logo']) AND strlen($cmslabel['order_logo']) > 150): ?>
        <?php if (Kohana::config('app.webshop.order_display_ean_on_view_order')) : ?>
            <div style="clear: both;">
                <div style="float:left;width:70%;">
                    <?php echo Arr::get($cmslabel, 'order_logo'); ?>
                </div>
                <div style="float:right;width:30%;margin-top: 50px;">
                    <?php if (!empty($ean)) {
                        echo $ean;
                    } ?>
                </div>
            </div>
            <div style="clear: both;">
        <?php else: ?>
            <?php echo Arr::get($cmslabel, 'order_logo'); ?>
        <?php endif; ?>
        <?php if (Kohana::config('app.webshop.order_view_total_layout') == '2'): ?>
            <?php if (Kohana::config('app.webshop.order_clientmail_number') OR !empty($info['user_staff'])): ?>
                <h1 style="margin: 0; padding: 0 0 5px"><?php echo $order_no_title; ?>: <?php echo $item->get_number(); ?> / <?php echo Date::humanize($item->datetime_created); ?></h1>
                <?php if (isset($item->id) AND $item->id != $item->get_number() AND $order_view_id): ?>
                    <p><?php echo Arr::get($cmslabel, 'order_id', 'ID'); ?>: <strong><?php echo $item->id; ?></strong></p>
                <?php endif; ?>
            <?php endif; ?>
            <?php if (!empty($item->internal_number)): ?>
                <p><?php echo Arr::get($cmslabel, 'order_internal_number', 'Br. narudžbe kupca'); ?>: <strong><?php echo $item->internal_number; ?></strong></p>
            <?php endif; ?>
            <?php if (Kohana::config('app.webshop.order_display_ean_on_view_order')) : ?>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <?php if (Kohana::config('app.webshop.order_clientmail_number') OR !empty($info['user_staff'])): ?>
                <h1 style="margin: 0; padding: 0 0 5px"><?php echo $order_no_title; ?>: <?php echo $item->get_number(); ?></h1>
                <?php if (isset($item->id) AND $item->id != $item->get_number() AND $order_view_id): ?>
                    <p><?php echo Arr::get($cmslabel, 'order_id', 'ID'); ?>: <strong><?php echo $item->id; ?></strong></p>
                <?php endif; ?>
            <?php endif; ?>
            <?php if (!empty($item->internal_number)): ?>
                <p><?php echo Arr::get($cmslabel, 'order_internal_number', 'Br. narudžbe kupca'); ?>: <strong><?php echo $item->internal_number; ?></strong></p>
            <?php endif; ?>
            <p><?php echo Arr::get($cmslabel, 'order_date', __('Datum')); ?>: <?php echo Date::humanize($item->datetime_created); ?></p>
        <?php endif; ?>
        <h3 style="margin: 0 0 13px;">
            <?php echo Arr::get($cmslabel, 'status'); ?>:
            <strong><?php echo Utf8::strtolower($item->status->title($item->lang)); ?></strong><?php if (($status_description = $item->status->description($item->lang))): ?> (<?php echo $status_description; ?>)<?php endif; ?>
            <?php if (isset($item->order->number)): ?>
                <br/><?php echo $order_no_title; ?>: <?php echo $item->order->number; ?>
            <?php endif; ?>
        </h3>
    <?php else: ?>
        <table width="100%" cellpadding="0" cellspacing="0">
            <tr>
                <td align="left" valign="top" width="50%" style="padding-left: 0; margin-left: 0">
                    <?php if (Kohana::config('app.webshop.order_view_total_layout') == '2'): ?>
                        <?php if (Kohana::config('app.webshop.order_clientmail_number') OR !empty($info['user_staff'])): ?>
                            <h1 style="margin: 0; padding: 0 0 5px"><?php echo $order_no_title; ?>: <?php echo $item->get_number(); ?> / <?php echo Date::humanize($item->datetime_created); ?></h1>
                            <?php if (isset($item->id) AND $item->id != $item->get_number() AND $order_view_id): ?>
                                <p><?php echo Arr::get($cmslabel, 'order_id', 'ID'); ?>: <strong><?php echo $item->id; ?></strong></p>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if (!empty($item->internal_number)): ?>
                            <p><?php echo Arr::get($cmslabel, 'order_internal_number', 'Br. narudžbe kupca'); ?>: <strong><?php echo $item->internal_number; ?></strong></p>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php if (Kohana::config('app.webshop.order_clientmail_number') OR !empty($info['user_staff'])): ?>
                            <h1 style="margin: 0; padding: 0 0 5px"><?php echo $order_no_title; ?>: <?php echo $item->get_number(); ?></h1>
                            <?php if (isset($item->id) AND $item->id != $item->get_number() AND $order_view_id): ?>
                                <p><?php echo Arr::get($cmslabel, 'order_id', 'ID'); ?>: <?php echo $item->id; ?></p>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if (!empty($item->internal_number)): ?>
                            <p><?php echo Arr::get($cmslabel, 'order_internal_number', 'Br. narudžbe kupca'); ?>: <strong><?php echo $item->internal_number; ?></strong></p>
                        <?php endif; ?>
                        <p><?php echo Arr::get($cmslabel, 'order_date', __('Datum')); ?>: <?php echo Date::humanize($item->datetime_created); ?></p>
                    <?php endif; ?>
                    <h3 style="margin: 0 0 13px;">
                        <?php echo Arr::get($cmslabel, 'status'); ?>:
                        <strong><?php echo Utf8::strtolower($item->status->title($item->lang)); ?></strong><?php if (($status_description = $item->status->description($item->lang))): ?> (<?php echo $status_description; ?>)<?php endif; ?>
                        <?php if (isset($item->order->number)): ?>
                            <br/><?php echo Arr::get($cmslabel, 'order_no'); ?>: <?php echo $item->order->number; ?>
                        <?php endif; ?>
                    </h3>
                </td>
                <td align="right" valign="top" width="50%">
                    <?php echo Arr::get($cmslabel, 'order_logo'); ?>
                </td>
                <?php if (Kohana::config('app.achievement.use_loyaltybadges')): ?>
                    <?php $priority_badges = $item->get_user_priority_badges(); ?>
                    <?php if ($priority_badges): ?>
                        <?php foreach ($priority_badges AS $priority_badge): ?>
                            <td align="right">
                                <img src="<?php echo Thumb::generate($priority_badge['image'], 150, 50, FALSE, 'thumb', TRUE, '/media/images/no-image-50'); ?>" alt="" width="70" />
                            </td>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </tr>
        </table>
    <?php endif; ?>

    <table width="100%" class="table" cellpadding="0" cellspacing="0">
        <tr>
            <?php if (Kohana::config('app.webshop.payment_bill_default_customer')): ?>
                <td align="left" valign="top" width="50%" style="border-right: 1px solid #CCC">
                    <?php if ($item->is_shipping_pickup()): ?>
                        <?php echo Arr::get($cmslabel, 'bill_address_pickup', 'Podaci za račun'); ?>:
                        <br/>
                    <?php else: ?>
                        <strong><?php echo Arr::get($cmslabel, 'bill_address'); ?>:</strong><br/>
                    <?php endif; ?>
                    <?php if ($item->b_same_as_shipping): ?>
                        <?php if ($item->is_shipping_pickup()): ?>
                            <?php echo Arr::get($cmslabel, 'same_as_shipping_pickup', 'Jednaki su podacima o kupcu'); ?>
                        <?php else: ?>
                            <?php echo Arr::get($cmslabel, 'same_as_shipping'); ?>
                        <?php endif;?>
                    <?php else: ?>
                        <?php if (Kohana::config('app.webshop.order_use_fullname_with_company') AND $item->b_company_name): ?>
                            <?php echo $item->b_company_name . ' ' . Arr::get($cmslabel, 'order_company_with_fullname', 'n/r'); ?>
                        <?php endif; ?>
                        <?php echo $item->b_first_name . ' ' . $item->b_last_name; ?>
                        <?php if (!$item->b_r1): ?>
                            <?php if ($item->b_company_oib): ?>
                                <br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo $item->b_company_oib; ?><?php endif; ?>
                            <?php if ($item->b_company_address): ?>
                                <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/><?php echo $item->b_company_address; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                        <br/><?php echo $item->b_address; echo ((Kohana::config('app.auth.use_house_number') AND !empty($item->b_house_number)) ? ' ' . $item->b_house_number : ''); ?>, <?php echo $item->b_zipcode . ' ' . $item->b_city; ?><?php if (Kohana::config('app.webshop.order_view_country')): ?>, <?php echo Arr::get($item->b_country->descriptions, $item->lang, $item->b_country->code); ?><?php endif; ?>
                        <?php if ($item->b_phone): ?>
                            <br/><?php echo Arr::get($cmslabel, 'phone'); ?>: <?php echo $item->b_phone; ?><?php endif; ?>
                        <br/><?php echo $item->b_email; ?>
                    <?php endif; ?>
                    <?php if ($item->b_r1): ?>
                        <br/><strong><?php echo Arr::get($cmslabel, 'r1'); ?></strong>
                        <br/><?php echo $item->b_company_name; ?>
                        <?php if ($item->b_company_name): ?>
                            <br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo $item->b_company_oib; ?><?php endif; ?>
                        <?php if ($item->b_company_address): ?>
                            <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/><?php echo $item->b_company_address; ?><?php endif; ?>
                        <?php if (!empty($item->b_company_zipcode)): ?>
                            <br/><?php echo Arr::get($cmslabel, 'company_zipcode'); ?>:<br/><?php echo $item->b_company_zipcode; ?>
                        <?php endif; ?>
                        <?php if (!empty($item->b_company_city)): ?>
                            <br/><?php echo Arr::get($cmslabel, 'company_city'); ?>:<br/><?php echo $item->b_company_city; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </td>
                <td align="left" valign="top" width="50%">
                    <?php if (!empty($item->same_as_bill) AND $item->is_shipping_pickup()): ?>
                        <strong><?php echo Arr::get($cmslabel, 'same_as_bill_pickup'); ?></strong>
                    <?php else: ?>
                        <?php $customer_category = Kohana::config('app.webshop.customer_category.choices'); ?>
                        <?php if ($content_category == 'order'): ?>
                            <?php if ($item->is_shipping_pickup()): ?>
                                <strong><?php echo Arr::get($cmslabel, 'shipping_address_pickup', 'Podaci o kupcu'); ?>:</strong>
                                <br/>
                            <?php else: ?>
                                <strong><?php echo Arr::get($cmslabel, 'customer_details'); ?><?php echo Arr::get($cmslabel, 'customer_details_shipping'); ?><?php if (count($customer_category) > 1): ?> (<?php echo Arr::get($customer_category, $item->customer_category); ?>)<?php endif; ?>:</strong><br/>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if (Kohana::config('app.webshop.order_use_fullname_with_company') AND $item->company_name AND !$item->company_address): ?>
                            <?php echo $item->company_name . ' ' . Arr::get($cmslabel, 'order_company_with_fullname', 'n/r'); ?>
                        <?php endif; ?>
                        <?php echo $item->first_name . ' ' . $item->last_name; ?>
                        <?php if (!empty($item->oib)): ?>
                            <br/><?php echo Arr::get($cmslabel, 'oib', 'OIB'); ?>: <?php echo $item->oib; ?><?php endif; ?>
                        <?php if (!empty($item->iban)): ?>
                            <br/><?php echo Arr::get($cmslabel, 'iban', 'IBAN'); ?>: <?php echo $item->iban; ?><?php endif; ?>
                        <br/><?php echo $item->address; echo ((Kohana::config('app.auth.use_house_number') AND !empty($item->house_number)) ? ' ' . $item->house_number : ''); ?>, <?php echo $item->zipcode . ' ' . $item->city; ?><?php if (Kohana::config('app.webshop.order_view_country')): ?>, <?php echo Arr::get($item->country->descriptions, $item->lang, $item->country->code); ?><?php endif; ?>
                        <br/><?php echo Arr::get($cmslabel, 'phone'); ?>: <?php echo $item->phone; ?>
                        <br/><?php echo $item->email; ?>
                        <?php if ($item->company_name AND $item->company_address): ?>
                            <br/><br/><?php echo $item->company_name; ?>
                            <?php if ($item->company_address): ?>
                                <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/><?php echo $item->company_address; ?><?php endif; ?>
                        <?php endif; ?>
                        <?php if (Kohana::config('app.webshop.view_order_shipping_info_use_company_oib') AND $item->company_oib): ?>
                            <br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo $item->company_oib; ?><?php endif; ?>
                    <?php endif; ?>
                </td>
            <?php else: ?>
                <td align="left" valign="top" width="50%" style="border-right: 1px solid #CCC">
                    <?php $customer_category = Kohana::config('app.webshop.customer_category.choices'); ?>
                    <?php if ($content_category == 'order'): ?>
                        <?php if ($item->is_shipping_pickup()): ?>
                            <strong><?php echo Arr::get($cmslabel, 'customer_details_pickup', 'Podaci o kupcu'); ?><?php if (count($customer_category) > 1): ?> (<?php echo Arr::get($customer_category, $item->customer_category); ?>)<?php endif; ?>:</strong><br/>
                        <?php else: ?>
                            <strong><?php echo Arr::get($cmslabel, 'customer_details'); ?><?php if (count($customer_category) > 1): ?> (<?php echo Arr::get($customer_category, $item->customer_category); ?>)<?php endif; ?>:</strong><br/>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if (Kohana::config('app.webshop.order_use_only_company')): ?>
                        <?php if ($item->company_name): ?>
                            <?php echo $item->company_name; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php if (Kohana::config('app.webshop.order_use_fullname_with_company') AND $item->company_name AND !$item->company_address): ?>
                            <?php echo $item->company_name; ?><?php echo Arr::get($cmslabel, 'order_company_with_fullname', 'n/r'); ?>
                        <?php endif; ?>
                        <?php if (Kohana::config('app.webshop.order_use_company_name_always') AND $item->company_name): ?>
                            <?php echo $item->company_name; ?>
                            </br>
                        <?php endif; ?>
                        <?php echo $item->first_name . ' ' . $item->last_name; ?>
                        <?php if (empty($item->company_name) AND !empty($item->company->company_name)): ?>
                            <br/><?php echo $item->company->company_name; ?>
                        <?php endif; ?>
                        <?php if (!empty($item->shipping_company)): ?>
                            <br/> <?php echo Arr::get($cmslabel, 'shipping_company'); ?>: <?php echo $item->shipping_company; ?>
                        <?php endif; ?>
                    <?php endif;?>

                    <?php if(Kohana::config('app.sitename') == 'b2b.comet.hr'): ?>
                        <?php if ($item->b_company_oib): ?><br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo $item->b_company_oib; ?><?php endif; ?>
                        <?php if ($item->b_company_address): ?><br/><?php echo $item->b_company_address; ?><?php endif; ?>
                    <?php else: ?>
                        <?php if (!empty($item->oib)): ?><br/><?php echo Arr::get($cmslabel, 'oib', 'OIB'); ?>: <?php echo $item->oib; ?><?php endif; ?>
                        <?php if (!empty($item->iban)): ?><br/><?php echo Arr::get($cmslabel, 'iban', 'IBAN'); ?>: <?php echo $item->iban; ?><?php endif; ?>
                        <br/><?php echo $item->address; echo ((Kohana::config('app.auth.use_house_number') AND !empty($item->house_number)) ? ' ' . $item->house_number : ''); ?>, <?php echo $item->zipcode . ' ' . $item->city; ?><?php if (Kohana::config('app.webshop.order_view_country')): ?>, <?php echo Arr::get($item->country->descriptions, $item->lang, $item->country->code); ?><?php endif; ?>
                    <?php endif; ?>
                    <br/><?php echo Arr::get($cmslabel, 'phone'); ?>: <?php echo $item->phone; ?>
                    <?php if (!Kohana::config('app.webshop.payment_bill_default_customer')): ?>
                        <br/><?php echo $item->email; ?>
                    <?php endif; ?>
                    <?php if ($item->company_name AND $item->company_address): ?>
                        <br/>
                        <?php if (Kohana::config('app.webshop.order_use_company_personal_data')): ?>
                            <br/><?php if(Kohana::config('app.sitename') == 'b2b.comet.hr'): ?><strong>Naručitelj: </strong><?php endif; ?>
                            <?php echo $item->first_name . ' ' . $item->last_name; ?>
                        <?php endif; ?>
                        <br/><?php echo $item->company_name; ?>
                        <?php if ($item->company_address): ?>
                            <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/><?php echo $item->company_address; ?><?php endif; ?>
                    <?php endif; ?>
                    <?php if ($item->company_oib): ?>
                        <br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo $item->company_oib; ?>
                    <?php endif; ?>

                    <?php $loyalty_code = $item->get_user_loyalty_code(); ?>
                    <?php $loyalty_item_name = $item->get_user_loyalty_item_name(); ?>
                    <?php if ($loyalty_code): ?>
                        <?php if (Kohana::config('app.auth.loyalty_code_type') == 'ean13'): ?>
                            <br/><br/><?php echo Arr::get($cmslabel, 'loyalty_card', 'Loyalty kartica'); ?>:
                            <div data-barkod="<?php echo $loyalty_code; ?>" style="margin-top: 10px;"></div>
                        <?php else: ?>
                            <br/><br/><?php echo Arr::get($cmslabel, 'loyalty_card', 'Loyalty kartica'); ?>: <?php echo $loyalty_code; ?>
                            <?php if (!empty($loyalty_item_name)): ?>
                                <br/><?php echo Arr::get($cmslabel, 'loyalty_item_level', 'Razred')?>: <?php echo $loyalty_item_name; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if (Kohana::config('app.webshop.order_use_collected_loyalty_points') AND !empty($item->collected_loyalty_points)) : ?>
                        <br/><?php echo str_replace('%COLLECTED_LOYALTY_POINTS%', $item->collected_loyalty_points, Arr::get($cmslabel, 'collected_loyalty_points', 'Kada se narudžba fakturira skupiti ćete %COLLECTED_LOYALTY_POINTS% loyalty bodova')); ?>
                    <?php endif; ?>

                    <?php if (Kohana::config('app.webshop.payment_bill_as_shipping')): ?>
                        <?php if ($item->b_r1): ?>
                            <br/><br/><strong><?php echo Arr::get($cmslabel, ($use_vies) ? 'vies' : 'r1'); ?></strong>
                            <br/><?php echo $item->b_company_name; ?>
                            <?php if ($item->b_company_name): ?>
                                <br/><?php echo Arr::get($cmslabel, ($use_vies) ? 'vies_number' : 'company_oib'); ?>: <?php echo $item->b_company_oib; ?><?php endif; ?>
                            <?php if ($item->b_company_address): ?>
                                <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/>
                                <?php echo $item->b_company_address; ?><?php if (!empty($item->b_company_zipcode)): ?>, <?php echo $item->b_company_zipcode; ?><?php endif; ?>
                                <?php if (!empty($item->b_company_city)): ?><?php echo $item->b_company_city; ?><?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php if (Kohana::config('app.webshop.order_customer_signup_info')): ?>
                        <?php if ($item->user_id): ?>
                            <br/><strong>Registrirani korisnik</strong>
                        <?php else: ?>
                            <br/><strong>Gost (neregistrirani) korisnik</strong>
                        <?php endif; ?>
                    <?php endif; ?>
                </td>
                <td align="left" valign="top" width="50%">
                    <?php if ($item->is_shipping_pickup()): ?>
                        <?php echo Arr::get($cmslabel, 'bill_address_pickup', 'Račun će biti izdan prilikom preuzimanja paketa u trgovini.'); ?><br/>
                    <?php else: ?>
                        <strong><?php echo Arr::get($cmslabel, 'bill_address'); ?></strong><br/>
                    <?php endif; ?>
                    <?php if ($item->b_same_as_shipping): ?>
                        <?php if (!$item->is_shipping_pickup()): ?>
                            <?php if (Kohana::config('app.webshop.order_use_bill_same_address')): ?>
                                <?php if ($item->b_company_name): ?>
                                    <?php echo $item->b_company_name; ?>
                                <?php endif; ?>
                                <?php if (!$item->b_r1): ?>
                                    <?php if ($item->b_company_address): ?>
                                        <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/><?php echo $item->b_company_address; ?>
                                    <?php endif; ?>
                                    <?php if ($item->b_company_oib): ?>
                                        <br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo $item->b_company_oib; ?><?php endif; ?>
                                <?php endif; ?>
                                <?php if (empty($item->b_company_address)): ?>
                                    <br/><?php echo $item->b_address; echo ((Kohana::config('app.auth.use_house_number') AND !empty($item->b_house_number)) ? ' ' . $item->b_house_number : ''); ?>, <?php echo $item->b_zipcode . ' ' . $item->b_city; ?><?php if (Kohana::config('app.webshop.order_view_country')): ?>, <?php echo Arr::get($item->b_country->descriptions, $item->lang, $item->b_country->code); ?><?php endif; ?>
                                <?php endif; ?>
                            <?php else: ?>
                                <?php echo Arr::get($cmslabel, 'same_as_shipping'); ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <?php if (Kohana::config('app.webshop.order_use_fullname_with_company') AND $item->b_company_name): ?>
                            <?php echo $item->b_company_name; ?><?php echo Arr::get($cmslabel, 'order_company_with_fullname', 'n/r'); ?>
                        <?php endif; ?>
                        <?php echo $item->b_first_name . ' ' . $item->b_last_name; ?>
                        <?php if (!Kohana::config('app.webshop.payment_bill_as_shipping')): ?>
                            <?php if ($item->b_company_oib): ?>
                                <br/><?php echo Arr::get($cmslabel, 'company_oib'); ?>: <?php echo $item->b_company_oib; ?><?php endif; ?>
                            <?php if ($item->b_company_address): ?>
                                <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/><?php echo $item->b_company_address; ?><?php endif; ?>
                        <?php endif; ?>
                        <br/><?php echo $item->b_address; echo ((Kohana::config('app.auth.use_house_number') AND !empty($item->b_house_number)) ? ' ' . $item->b_house_number : ''); ?>, <?php echo $item->b_zipcode . ' ' . $item->b_city; ?><?php if (Kohana::config('app.webshop.order_view_country')): ?>, <?php echo Arr::get($item->b_country->descriptions, $item->lang, $item->b_country->code); ?><?php endif; ?>
                        <?php if ($item->b_phone): ?>
                            <br/><?php echo Arr::get($cmslabel, 'phone'); ?>: <?php echo $item->b_phone; ?><?php endif; ?>
                        <?php if (Kohana::config('app.webshop.payment_bill_default_customer')): ?>
                            <br/><?php echo $item->b_email; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if (!Kohana::config('app.webshop.payment_bill_as_shipping')): ?>
                        <?php if ($item->b_r1): ?>
                            <br/><br/><strong><?php echo Arr::get($cmslabel, ($use_vies) ? 'vies' : 'r1'); ?></strong>
                            <br/><?php echo $item->b_company_name; ?>
                            <?php if ($item->b_company_name): ?>
                                <br/><?php echo Arr::get($cmslabel, ($use_vies) ? 'vies_number' : 'company_oib'); ?>: <?php echo $item->b_company_oib; ?><?php endif; ?>
                            <?php if ($item->b_company_address): ?>
                                <br/><?php echo Arr::get($cmslabel, 'company_address'); ?>:<br/>
                                <?php echo $item->b_company_address; ?><?php if (!empty($item->b_company_zipcode)): ?>, <?php echo $item->b_company_zipcode; ?><?php endif; ?>
                                <?php if (!empty($item->b_company_city)): ?><?php echo $item->b_company_city; ?><?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </td>
            <?php endif; ?>
        </tr>
    </table>

    <br class="clear"/>&nbsp;

    <?php if ($mode == 'barcode'): ?>
        <table width="100%" class="table" cellpadding="0" cellspacing="0">
            <?php if ($content_category == 'order'): ?>
                <?php $order_view_itemimage = Kohana::config('app.webshop.order_view_itemimage'); ?>
                <tr>
                    <th width="1%"></th>
                    <th width="10%">Šifra</th>
                    <th>Naziv</th>
                    <th width="10%">Kol</th>
                    <th width="15%">Cijena</th>
                    <th width="10%">Uk</th>
                    <th width="15%">Barkod</th>
                </tr>
            <?php endif; ?>
            <?php $i = 1; ?>
            <?php if (isset($item->items) AND count($item->items)): ?>
                <?php $calculate_real_discount = Kohana::config('app.catalog.calculate_real_discount'); ?>
                <?php $show_item_coupons = Kohana::config('app.webshop.show_item_coupons'); ?>
                <?php $use_new_view = Kohana::config('app.webshop.use_new_view'); ?>
                <?php foreach ($item->items as $item_item): ?>
                    <tr>
                        <td align="center" valign="top"><?php echo $i; ?></td>
                        <td valign="top">
                            <?php if (!empty($item_item->internal_code)): ?>
                                <?php echo $item_item->internal_code; ?>
                            <?php else: ?>
                                <?php echo $item_item->get_code(); ?>
                            <?php endif; ?>
                        </td>
                        <td valign="top">
                            <strong><?php echo $item_item->title; ?></strong>
                            <?php if (!empty($item_item->subtitle) AND $item_item->subtitle != $item_item->title): ?>
                                <br/><?php echo Arr::get($cmslabel, 'orderitem_subtitle'); ?><?php echo $item_item->subtitle; ?>
                            <?php endif; ?>
                            <?php if (!Kohana::config('app.webshop.orderitem_disable_unit_description') AND !empty($item_item->unit_description)): ?><?php echo $item_item->unit_description; ?><?php endif; ?>
                            <?php if (!empty($item_item->description) AND !Kohana::config('app.webshop.orderitem_hide_description')): ?>
                                <br/><?php echo nl2br(trim($item_item->description, ', ')); ?>
                            <?php endif; ?>
                            <?php if (!empty($item_item->services_text)): ?>
                                <br/><?php echo nl2br(trim($item_item->services_text, ', ')); ?>
                            <?php endif; ?>
                            <?php if (!empty($item_item->duration_date)): ?>
                                <br/><?php echo Arr::get($cmslabel, 'duration_date', __('Datum dolaska')); ?>: <?php echo Date::humanize($item_item->duration_date, ', '); ?>
                            <?php endif; ?>
                        </td>
                        <td valign="top">
                            <?php if (isset($item_item->unit) AND $item_item->unit == 'kg'): ?><?php echo number_format($item_item->qty, 2, ',', '.'); ?>&nbsp;<?php echo Inflector::units($item_item->unit, $item->lang); ?><?php else: ?><?php echo $item_item->qty; ?>&nbsp;<?php echo Inflector::units($item_item->unit, $item->lang); ?><?php endif; ?>
                        </td>
                        <td valign="top">
                            <?php if (((($calculate_real_discount AND $item_item->basic_price > $item_item->price AND !$show_item_coupons) OR ((!$calculate_real_discount OR $show_item_coupons) AND $item_item->discount > 0)) AND empty($item->user_disabled_b2b_price)) AND empty(Kohana::config('app.webshop.order_view_remove_basic_price_for_discounted_products'))): ?>
                                <small>
                                    <del><?php echo Utils::currency_format($item_item->basic_price * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->basic_price * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></del>
                                    -<?php echo number_format($item_item->get_discount_percent(), Kohana::config('app.webshop.order_discount_round')); ?>
                                    %
                                </small><br>
                            <?php endif; ?>
                            <?php echo Utils::currency_format($item_item->price * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->price * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created, ]); ?>
                        </td>
                        <td valign="top">
                            <?php echo Utils::currency_format(($item_item->tax_include_in_price ? $item_item->total() : $item_item->total_basic()) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string(($item_item->tax_include_in_price ? $item_item->total() : $item_item->total_basic()) * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                        </td>
                        <td valign="top">
                            <div data-barkod="<?php echo $item_item->code; ?>" style="margin-bottom: 20px;"></div>
                        </td>
                    </tr>
                    <?php $i++; ?>
                <?php endforeach; ?>
                <?php if (Kohana::config('app.webshop.order_barcode_view_extraitems') AND isset($item->itemsextra) AND count($item->itemsextra)): ?>
                    <?php foreach ($item->itemsextra AS $item_item): ?>
                        <?php if ($item_item->category != 'coupon' AND $item_item->category != 'loyalty'): continue; endif; ?>
                        <tr>
                            <td align="center" valign="top"><?php echo $i; ?></td>
                            <td valign="top"><?php echo $item_item->code; ?></td>
                            <td valign="top"><strong><?php echo $item_item->title; ?></strong></td>
                            <td valign="top">1</td>
                            <td valign="top"><?php echo Utils::currency_format($item_item->total * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->total * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></td>
                            <td valign="top">
                                <?php echo Utils::currency_format($item_item->total * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->total * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                            </td>
                            <td valign="top">
                                <div data-barkod="<?php echo $item_item->code; ?>" style="margin-bottom: 20px;"></div>
                            </td>
                        </tr>
                        <?php $i++; ?>
                    <?php endforeach; ?>
                <?php endif; ?>
            <?php endif; ?>
        </table>
        </div>
        <?php if ($item->message): ?>
            <p><strong><?php echo Arr::get($cmslabel, 'client_message', __('Poruka kupca')); ?>
                    :</strong> <?php echo nl2br($item->message); ?></p>
        <?php endif; ?>
    <?php else: ?>
        <?php if ($redemption_can_change): ?>
            <form action="" method="POST" name="order_status" id="order_status">
            <p>
                <strong><?php echo Arr::get($cmslabel, 'redemption_info', 'Označi stavke koje želiš prihvatiti. Nakon odabira, klikni na Potvrdi'); ?></strong>
            </p>
        <?php endif; ?>

        <?php $this->block('order_items'); ?>
        <table width="100%" class="table" cellpadding="0" cellspacing="0">
            <?php // Ako negdje bude problema, vratiti product polje na 17%, total na 15%, ja sam stavio 19% zbog sync-av i testirao na par webova. ?>
            <?php if ($content_category == 'order'): ?>
                <?php $order_view_itemimage = Kohana::config('app.webshop.order_view_itemimage'); ?>
                <?php $order_covers_advanced = Kohana::config('app.webshop.use_covers_advanced'); ?>
                <tr>
                    <th width="3%"></th>
                    <?php if ($order_view_itemimage): ?>
                        <th width="5%"></th>
                        <th width="8%"><?php echo Arr::get($cmslabel, 'code'); ?></th>
                    <?php else: ?>
                        <th width="8%"><?php echo Arr::get($cmslabel, 'code'); ?></th>
                        <th width="1%"></th>
                    <?php endif; ?>
                    <?php if (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)): ?>
                        <th width="<?php echo (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)) ? 15 : 19; ?>%"><?php echo Arr::get($cmslabel, 'supplier_code', 'Šifra vanjskog dobavljača'); ?></th>
                    <?php endif; ?>
                    <th <?php if (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)): ?> width="20%" <?php endif; ?>><?php echo Arr::get($cmslabel, 'product'); ?></th>
                    <?php if (!$item->user_disabled_price): ?>
                        <th width="<?php echo (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)) ? 15 : 19; ?>%" style="text-align: right"><?php echo Arr::get($cmslabel, 'price'); ?></th>
                    <?php endif; ?>
                    <?php if ($order_covers_advanced): ?>
                        <th style="text-align:center; ;"><?php echo Arr::get($cmslabel, 'for_wrapping', 'Zamotati'); ?></th>
                    <?php endif; ?>
                    <th width="<?php echo (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)) ? 10 : 8; ?>%" style="text-align: right"><?php echo Arr::get($cmslabel, 'quantity'); ?></th>
                    <?php if (!$item->user_disabled_price): ?>
                        <th width="<?php echo (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)) ? 15 : 19; ?>%" style="text-align: right"><?php echo Arr::get($cmslabel, $item->total_label(), 'Ukupno'); ?></th>
                    <?php endif; ?>
                </tr>
            <?php endif; ?>

            <?php $i = 1; ?>
            <?php $update_qty = (Kohana::config('app.catalog.use_available_qty') AND Kohana::config('app.webshop.check_qty')); ?>
            <?php $order_highlight_qty = (Kohana::config('app.webshop.order_highlight_qty')); ?>
            <?php if (isset($item->items) AND count($item->items)): ?>
                <?php $calculate_real_discount = Kohana::config('app.catalog.calculate_real_discount'); ?>
                <?php $show_item_coupons = Kohana::config('app.webshop.show_item_coupons'); ?>
                <?php $use_new_view = Kohana::config('app.webshop.use_new_view'); ?>
                <?php foreach ($item->items as $item_item): ?>
                    <?php if (empty($item->b2b) AND !empty($item->loyalty_discount_percent) AND $item->loyalty_discount_percent > 0 AND !empty($item_item->loyalty_price) AND $item_item->loyalty_price > 0 AND $item_item->loyalty_price !== $item_item->price): ?>
                        <?php $item_item->price = $item_item->loyalty_price; ?>
                        <?php $item_item->discount = $item->loyalty_discount; ?>
                    <?php endif; ?>
                    <tr id="items_<?php echo $item_item->id; ?>" class="item_row">
                        <td align="center" valign="top">
                            <?php if ($redemption_can_change): ?>
                                <input type="checkbox" name="part_items[<?php echo $item_item->id; ?>]" class=""
                                       value="1"
                                       title="<?php echo Arr::get($cmslabel, 'part_confirm_item', 'Prihvati ovu stavku'); ?>"
                                       checked/>
                            <?php else: ?>
                                <?php echo $i; ?>
                            <?php endif; ?>
                        </td>
                        <?php if ($order_view_itemimage): ?>
                            <td valign="top">
                                <?php if (isset($item_item->main_image) AND $item_item->main_image): ?>
                                    <?php if (Kohana::config('app.webshop.orderitem_use_image_url')): ?>
                                        <?php if ($image_size = Kohana::config('app.webshop.orderitem_use_image_size')): ?>
                                            <a href="<?php echo $item_item->get_absolute_url(); ?>"><img style="width: auto; height: auto; max-width: <?php echo $image_size; ?>px;" src="<?php echo Thumb::generate($item_item->main_image, $image_size, $image_size, FALSE, 'thumb', TRUE, '/shared/images/space.gif'); ?>" alt="" width="100"/></a>
                                        <?php else: ?>
                                            <a href="<?php echo $item_item->get_absolute_url(); ?>"><img style="width: auto; height: auto; max-width: 100%;" src="<?php echo Thumb::generate($item_item->main_image, 50, 50, FALSE, 'thumb', TRUE, '/shared/images/space.gif'); ?>" alt="" width="35"/></a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php if($image_size = Kohana::config('app.webshop.orderitem_use_image_size')): ?>
                                            <?php $thumb_size = Kohana::config('app.webshop.orderitem_use_image_thumb_size'); ?>
                                            <?php $image_width = (!empty($thumb_size)) ? $thumb_size : 50; ?>
                                            <?php $image_height = (!empty($thumb_size)) ? $thumb_size : 50; ?>
                                            <img style="width: auto; height: auto; max-width: <?php echo $image_size;?>px" src="<?php echo Thumb::generate($item_item->main_image, $image_width, $image_height, FALSE, 'thumb', TRUE, '/shared/images/space.gif'); ?>"
                                                 alt="" width="35"/>
                                        <?php else: ?>
                                            <img style="width: auto; height: auto; max-width: 100%;" src="<?php echo Thumb::generate($item_item->main_image, 50, 50, FALSE, 'thumb', TRUE, '/shared/images/space.gif'); ?>"
                                                 alt="" width="35"/>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                            <td valign="top">
                                <em style="white-space: nowrap"><?php echo $item_item->get_code(); ?></em>
                                <?php if (Kohana::config('app.webshop.order_show_ean_code') AND !empty($item_item->ean_code)): ?>
                                    <br/><em style="white-space: nowrap"><?php echo $item_item->ean_code; ?></em>
                                <?php endif; ?>
                            </td>
                        <?php else: ?>
                            <td valign="top">
                                <?php echo $item_item->get_code(); ?>
                                <?php if (Kohana::config('app.webshop.order_show_ean_code') AND !empty($item_item->ean_code)): ?>
                                    <br/><?php echo $item_item->ean_code; ?>
                                <?php endif; ?>
                            </td>
                            <td valign="top"></td>
                        <?php endif; ?>

                        <?php if (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)): ?>
                            <td><?php echo $item_item->internal_code2; ?></td>
                        <?php endif; ?>

                        <td valign="top">
                            <?php if ($redemption AND empty($item_item->redemption_confirm)): ?>
                                <strong><strike><?php echo $item_item->title; ?></strike></strong>
                            <?php else: ?>
                                <strong><?php echo !empty($item_item->new_title) ? $item_item->new_title : $item_item->title; ?></strong>
                                <?php if (!empty($item_item->subtitle) AND $item_item->subtitle != $item_item->title): ?>
                                    <br/><?php echo Arr::get($cmslabel, 'orderitem_subtitle'); ?><?php echo $item_item->subtitle; ?>
                                <?php endif; ?>
                                <?php if (Kohana::config('app.webshop.order_qty_alert') AND $item_item->available_qty < 1): ?>
                                    <br/><?php echo Arr::get($cmslabel, 'order_catalogproduct_not_availabe'); ?>
                                <?php endif; ?>
                                <?php if (Kohana::config('app.webshop.orderitem_use_configurator_description') AND !empty($item_item->configurator_description)): ?>
                                    <br> <?php echo nl2br($item_item->configurator_description); ?>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if (!Kohana::config('app.webshop.orderitem_disable_unit_description') AND !empty($item_item->unit_description)): ?>
                                <?php echo $item_item->unit_description; ?>
                            <?php endif; ?>
                            <?php if (!empty($item_item->description) AND !Kohana::config('app.webshop.orderitem_hide_description')): ?>
                                <br/><?php echo nl2br(trim($item_item->description, ', ')); ?>
                            <?php endif; ?>
                            <?php if (!empty($item_item->services_text)): ?>
                                <br/><?php echo Arr::get($cmslabel, 'services_text', __('Dodatno')); ?>:<br><?php echo nl2br(trim($item_item->services_text, ', ')); ?>
                            <?php endif; ?>
                            <?php if (!empty($item_item->duration_date)): ?>
                                <br/><?php echo Arr::get($cmslabel, 'duration_date', __('Datum dolaska')); ?>: <?php echo Date::humanize($item_item->duration_date, ', '); ?>
                            <?php endif; ?>
                            <?php if (!empty($item_item->personalization_text)): ?>
                                <br/><?php echo Arr::get($cmslabel, 'personalization_text', 'Personalizirani tekst'); ?>: <?php echo trim($item_item->personalization_text, ', '); ?>
                                <?php if (!empty($item_item->personalization_style)): ?>
                                    <?php echo ' - ' . trim($item_item->personalization_style, ', '); ?>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if (!empty($item_item->state) OR !empty($item_item->notes_for_client)): ?>
                                <?php if (!empty($item_item->state)): ?>
                                    <p>
                                        Stanje: <?php echo Arr::get(Kohana::config('app.webshop.orderitem_state.choices'), $item_item->state); ?></p>
                                <?php endif; ?>
                                <?php if (!empty($item_item->notes_for_client)): ?>
                                    <p>Napomena: <?php echo $item_item->notes_for_client; ?></p>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if (Kohana::config('app.catalog.use_engravings') AND !empty($item_item->engraving)): ?>
                                <?php $primary_section_title = ''; ?>
                                <?php $secondary_section_title = ''; ?>
                                <?php if (!empty($item_item->engraving_data)): ?>
                                    <?php $engraving_config = json_decode($item_item->engraving_data, true); ?>
                                    <?php $primary_section_title = (!empty($engraving_config['multiple_engraving']) AND !empty($engraving_config['engraving_title']))
                                        ? $engraving_config['engraving_title']
                                        : ''; ?>
                                    <?php $secondary_section_title = (!empty($engraving_config['multiple_secondary_engraving']) AND !empty($engraving_config['secondary_engraving_title']))
                                        ? $engraving_config['secondary_engraving_title']
                                        : ''; ?>
                                <?php endif; ?>

                                <?php $engraving_config = json_decode($item_item->engraving, true); ?>
                                <?php $primary_engraving_data = Arr::get($engraving_config, 'primary', []); ?>
                                <?php $secondary_engraving_data = (Kohana::config('app.catalog.engraving_use_secondary_engraving_option'))
                                    ? Arr::get($engraving_config, 'secondary', [])
                                    : []; ?>
                                <?php $alignments = Catalog::engraving_options(['category' => 'alignments']); ?>
                                <br><br><strong><?php echo Arr::get($cmslabel, 'order_item_engraving_title', 'Opcije graviranja:'); ?></strong><br/>
                                <?php foreach($primary_engraving_data as $engraving_item_id => $engraving_item_data): ?>
                                    <?php
                                    $engraving_items = Catalog::engraving_items([
                                        'lang' => $item->lang,
                                        'filters' => [
                                            'id' => $engraving_item_id,
                                        ],
                                        'single' => true,
                                    ]);
                                    ?>

                                    <?php if (!empty($primary_section_title)): ?>
                                        <strong><?php echo $primary_section_title; ?></strong>
                                    <?php endif; ?>
                                    <?php foreach ($engraving_item_data as $input_key => $input_config): ?>
                                        <br/>
                                        <?php if (!empty($engraving_items["{$input_key}_main_title"]) AND empty($primary_section_title)): ?>
                                            <strong><?php echo $engraving_items["{$input_key}_main_title"]; ?></strong>
                                        <?php endif; ?>
                                        <p>
                                            <span><?php echo $input_config['title']; ?>: </span>
                                            <span><?php echo Utils::formatEngravingText($input_config['content'], null, ['width' => 20, 'height' => 20, 'html_tag' => true, 'default_image' => '/media/images/no-image-50.jpg']); ?></span>
                                        </p>

                                        <?php $alignment_data = Arr::get($alignments, $input_config['alignment'], []); ?>
                                        <?php $alignment_title = Arr::get($alignment_data, 'title', ''); ?>
                                        <?php if (!empty($alignment_title)): ?>
                                            <span><span><?php echo Arr::get($cmslabel, 'engraving_alignment_title', 'Poravnanje:'); ?> </span><?php echo $alignment_title; ?></span><br/>
                                        <?php endif; ?>
                                    <?php endforeach;?>
                                <?php endforeach; ?>
                                <?php if (!empty($secondary_engraving_data)): ?>
                                    <br/>
                                    <?php foreach($secondary_engraving_data as $engraving_item_id => $engraving_item_data): ?>
                                        <?php
                                        $engraving_items = Catalog::engraving_items([
                                            'lang' => $item->lang,
                                            'filters' => [
                                                'id' => $engraving_item_id,
                                            ],
                                            'single' => true,
                                        ]);
                                        ?>
                                        <?php if (!empty($secondary_section_title)): ?>
                                            <strong><?php echo $secondary_section_title; ?></strong>
                                        <?php endif; ?>
                                        <?php foreach ($engraving_item_data as $input_key => $input_config): ?>
                                            <br/>
                                            <?php if (!empty($engraving_items["{$input_key}_main_title"]) AND empty($secondary_section_title)): ?>
                                                <strong><?php echo $engraving_items["{$input_key}_main_title"]; ?></strong>
                                            <?php endif; ?>
                                            <p>
                                                <span><?php echo $input_config['title']; ?>: </span>
                                                <span><?php echo Utils::formatEngravingText($input_config['content'], null, ['width' => 20, 'height' => 20, 'html_tag' => true, 'default_image' => '/media/images/no-image-50.jpg']); ?></span>
                                            </p>
                                            <?php $alignment_data = Arr::get($alignments, $input_config['alignment'], []); ?>
                                            <?php $alignment_title = Arr::get($alignment_data, 'title', ''); ?>
                                            <?php if (!empty($alignment_title)): ?>
                                                <span><span><?php echo Arr::get($cmslabel, 'engraving_alignment_title', 'Poravnanje:'); ?> </span><?php echo $alignment_title; ?></span><br/>
                                            <?php endif; ?>
                                        <?php endforeach;?>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                            <?php if (Kohana::config('app.webshop.generate_barcode_using_api') AND !empty($item_item->ean_code) AND !empty($item_item->barcode_image)): ?>
                                <br/><img width="180" src="<?php echo $item_item->get_barcode_image();?>" alt="<?php echo $item_item->ean_code; ?>">
                            <?php endif; ?>
                            <?php if (Kohana::config('app.catalog.attributeitem_always_displayed') AND !empty($item_item->always_displayed_attributes)): ?>
                                <?php $always_displayed_attributes = json_decode($item_item->always_displayed_attributes, true);
                                foreach ($always_displayed_attributes as $attribute): ?>
                                    <?php if (!empty(Arr::get($cmslabel, "always_displayed_{$attribute['code']}", ''))): ?>
                                        <p><?php echo Arr::get($cmslabel, "always_displayed_{$attribute['code']}", ''); ?></p>
                                    <?php else: ?>
                                        <p><?php echo Arr::get($attribute, 'title', ''); ?></p>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </td>
                        <?php if (!$item->user_disabled_price): ?>
                            <?php
                            $badge = 0;
                            if (Kohana::config('app.webshop.orderitem_show_pricelowest_discount')) {
                                $badge = Utils::calculate_discount($item_item->extra_price_lowest, $item_item->price);
                            }
                            ?>
                            <td align="right" valign="top">
                                <?php if (((($item_item->basic_price > $item_item->price AND (($calculate_real_discount AND !$show_item_coupons) OR ($use_new_view AND $show_item_coupons))) OR ((!$calculate_real_discount OR $show_item_coupons) AND $item_item->discount > 0)) AND empty($item->user_disabled_b2b_price)) AND empty(Kohana::config('app.webshop.order_view_remove_basic_price_for_discounted_products'))): ?>
                                    <small>
                                        <del><?php echo Utils::currency_format($item_item->basic_price * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->basic_price * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></del>
                                        -
                                        <?php if (!empty($badge)) : ?>
                                            <?php echo number_format($badge, Kohana::config('app.webshop.order_discount_round')); ?>
                                        <?php elseif (Kohana::config('app.loyalty.use_loyalties') AND !empty($item->loyalty_discount_percent) AND $item_item->loyalty_price != $item_item->basic_price AND $item_item->get_discount_percent() == 0): ?>
                                            <?php echo number_format($item->loyalty_discount_percent, Kohana::config('app.webshop.order_discount_round')); ?>
                                        <?php else: ?>
                                            <?php echo number_format($item_item->get_discount_percent(), Kohana::config('app.webshop.order_discount_round')); ?>
                                        <?php endif; ?>
                                        %
                                    </small><br>
                                <?php endif; ?>
                                <?php if (($calculate_real_discount AND $item_item->basic_price > $item_item->price AND $show_item_coupons AND !empty($item_item->price_without_discount) AND !$use_new_view)): ?>
                                    <?php echo Utils::currency_format(($item_item->price_without_discount) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->price_without_discount * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php else: ?>
                                    <?php echo Utils::currency_format($item_item->price * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->price * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php endif; ?>
                                <?php if (Kohana::config('app.catalog.multitax')  AND Kohana::config('app.webshop.order_mail_multitax_item') AND $item_item->price AND ((!empty($show_tax_by_country) AND in_array($item->country->code, $show_tax_by_country)) OR empty($show_tax_by_country))): ?>
                                    <br>
                                    <small><?php echo Arr::get($cmslabel, 'tax') . ' ' . $item_item->tax * 100; ?>


                                        %
                                    </small>
                                <?php endif; ?>

                                <?php if (!empty($item_item->use_edu_discount ?? false)): ?>
                                    <br><small><?php echo Arr::get($cmslabel, 'edu_mail_discount', 'Edu popust') . ' ' . Utils::currency_format($item_item->price - ($item_item->price * $item_item->edu_discount), $currency_display) . Utils::get_second_pricetag_string($item_item->total_basic * $item->exchange, ['currency_code' => $item->currency->code, 'display_format' => 'admin']); ?></small>
                                <?php endif; ?>
                            </td>
                        <?php endif; ?>
                        <?php if ($order_covers_advanced): ?>
                            <td align="center" valign="top">
                                <?php
                                if (Kohana::config('app.webshop.webshopcover_use_qty')) {
                                    echo $item_item->cover_qty;
                                } else {
                                    echo ($item_item->cover_qty > 0) ? 'DA' : 'NE';
                                }
                                ?>
                            </td>
                        <?php endif; ?>
                        <td align="right" valign="top">
                                <span style="padding: 3px;font-size:12px; line-height:16px;<?php if (!empty($info['user_staff']) AND $order_highlight_qty AND $item_item->qty >= $order_highlight_qty): ?>border: 2px solid #ff0000;<?php else: ?>border: 0px solid #ffffff;<?php endif; ?>">
                                    <strong><?php if (isset($item_item->unit) AND $item_item->unit == 'kg'): ?><?php echo number_format($item_item->qty, 2, ',', '.'); ?>&nbsp;<?php echo Inflector::units($item_item->unit, $item->lang); ?><?php else: ?><?php echo $item_item->qty; ?>&nbsp;<?php echo Inflector::units($item_item->unit, $item->lang); ?><?php endif; ?></strong>
                                    <?php if (Kohana::config('app.webshop.orderitem_calculate_unit_qty') AND !empty($item_item->unitd_qty) AND !empty($item_item->unitd_unit)): ?>
                                        <br><small>= <?php echo ($item_item->qty * $item_item->unitd_qty).' '.$item_item->unitd_unit; ?></small>
                                    <?php endif; ?>
                                </span>

                            <?php if (!empty($info['user_staff']) AND Kohana::config('app.webshop.manager_qty_info')): ?>
                                <?php if ($update_qty AND $item_item->warehouse): ?>
                                    <br><span style="font-size:11px; line-height:14px;" title="Dostupno"><?php echo Arr::get($cmslabel, 'available_quantity_short', __('dost:')); ?>
                                            <br><?php echo str_replace(': ', ':&nbsp;', nl2br($item_item->warehouse)); ?></span>
                                <?php elseif (in_array($info['user_level'], Kohana::config('app.webshop.order_view_item_qty_available_to_staff'))
                                    AND $update_qty AND ($item_item_warehouses = $item_item->warehouses(true, $only_visible, $admin_location_filter, ',', 'S'))):?>
                                    <br><span style="font-size:11px; line-height:14px;" title="Dostupno"><?php echo Arr::get($cmslabel, 'available_quantity_short', __('dost:')) . $item_item_warehouses; ?></span>
                                <?php elseif ($update_qty AND ($item_item_warehouses = $item_item->warehouses(true, $only_visible, $admin_location_filter))):?>
                                    <br><span style="font-size:11px; line-height:14px;" title="Dostupno"><?php echo Arr::get($cmslabel, 'available_quantity_short', __('dostupno')); ?>
                                            <br><?php echo $item_item_warehouses; ?></span>
                                    <?php if (!Kohana::config('app.webshop.hide_available_qty_in_warehouse_list')): ?>
                                        <br><span style="font-size:11px; line-height:14px;" title="Dostupno"><?php echo Arr::get($cmslabel, 'available_quantity_short', __('dost:')); ?>&nbsp;<?php echo $item_item->available_qty; ?></span>
                                    <?php endif; ?>
                                <?php elseif ($update_qty AND $item_item->available_qty != NULL): ?>
                                    <br><span style="font-size:11px; line-height:14px;" title="Dostupno"><?php echo Arr::get($cmslabel, 'available_quantity_short', __('dost:')); ?>&nbsp;<?php echo $item_item->available_qty; ?></span>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if (!empty($info['user_staff']) AND Kohana::config('app.catalog.product_use_warehouse_position')): ?>
                                <br><span style="font-size:11px; line-height:14px;" title="Skladišna pozicija"><?php echo Arr::get($cmslabel, 'warehouse_position', __('skl: ')) . (!empty($item_item->warehouse_position) ? $item_item->warehouse_position : 'X'); ?><br></span>
                            <?php endif; ?>
                        </td>
                        <?php if (!$item->user_disabled_price): ?>
                            <td align="right" valign="top" data-total_basic="<?php echo $item_item->total_basic(); ?>"
                                data-total_tax="<?php echo $item_item->total_tax(); ?>"
                                data-total="<?php echo $item_item->total(); ?>">
                                <?php if (((($item_item->basic_price > $item_item->price AND (($calculate_real_discount AND !$show_item_coupons) OR ($use_new_view AND $show_item_coupons))) OR ((!$calculate_real_discount OR $show_item_coupons) AND $item_item->discount > 0)) AND empty($item->user_disabled_b2b_price)) AND empty(Kohana::config('app.webshop.order_view_remove_basic_price_for_discounted_products'))): ?>
                                    <small>
                                        <del><?php echo Utils::currency_format(($item_item->tax_include_in_price ? $item_item->basic_price_total() : $item_item->basic_price_total_basic()) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string(($item_item->tax_include_in_price ? $item_item->basic_price_total() : $item_item->basic_price_total_basic()) * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></del>
                                        -
                                        <?php if (!empty($badge)) : ?>
                                            <?php echo number_format($badge, Kohana::config('app.webshop.order_discount_round')); ?>
                                        <?php elseif (Kohana::config('app.loyalty.use_loyalties') AND !empty($item->loyalty_discount_percent) AND $item_item->loyalty_price != $item_item->basic_price AND $item_item->get_discount_percent() == 0): ?>
                                            <?php echo number_format($item->loyalty_discount_percent, Kohana::config('app.webshop.order_discount_round')); ?>
                                        <?php else: ?>
                                            <?php echo number_format($item_item->get_discount_percent(), Kohana::config('app.webshop.order_discount_round')); ?>
                                        <?php endif; ?>
                                        %
                                    </small><br>
                                <?php endif; ?>
                                <?php if (!$redemption OR ($redemption AND $item_item->redemption_confirm)) : ?>
                                    <?php echo Utils::currency_format(($item_item->tax_include_in_price ? $item_item->total() : $item_item->total_basic()) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string(($item_item->tax_include_in_price ? $item_item->total() : $item_item->total_basic()) * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php endif; ?>
                                <?php if (Kohana::config('app.sitename') == 'drogerija.hr'): ?><br>
                                    <small><?php echo Arr::get($cmslabel, 'no_tax', __('Bez PDV-a:')); ?><?php echo Utils::currency_format($item_item->total_basic(), $currency_display) . Utils::get_second_pricetag_string($item_item->total_basic() * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></small><?php endif; ?>
                            </td>
                        <?php endif; ?>
                    </tr>
                    <?php if (!empty($item_item->discount_loyalty_code)):?>
                        <style>
                            tr#coupons_row {
                                border-top: none !important;
                            }
                            tr#coupons_row > td {
                                border-top: none !important;
                            }
                            tr#loyalty_row {
                                border-top: none !important;
                            }
                            tr#loyalty_row > td {
                                border-bottom: none !important;
                            }
                        </style>
                    <?php endif;?>
                    <?php if (!empty($item_item->discount_loyalty_code) AND !empty($item->collected_loyalty_points)): ?>
                        <tr id="loyalty_row" style="border:none!important;">
                            <td></td>
                            <td></td>
                            <td></td>
                            <td><b><?php echo $item_item->discount_loyalty_code; ?>: <?php echo $item_item->discount_loyalty . '%'; ?></b></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    <?php endif; ?>
                    <?php if (!empty($show_item_coupons)): ?>
                        <tr <?php if (!empty($item_item->discount_loyalty_code)):?>id="coupons_row" <?php endif;?> style="border-top: none !important;">
                            <td></td>
                            <td></td>
                            <td></td>
                            <td>
                                <?php if(!empty($item_item->discount_coupon_description) OR !empty($item_item->discount_coupon_code)): ?>
                                    <b><?php echo 'Kupon: '; ?></b>
                                <?php endif; ?>
                                <?php if (!empty($item_item->discount_coupon_description)): ?>
                                    <b><?php echo $item_item->discount_coupon_description; ?></b><br/>
                                <?php endif; ?>
                                <?php if (!empty($item_item->discount_coupon_code)): ?>
                                    <?php if (!empty($item_item->discount_coupon_description)): ?><?php endif; ?><?php echo $item_item->discount_coupon_code; ?><?php echo (!empty($item_item->discount_coupon) AND Kohana::config('app.webshop.show_item_coupons_percent')) ? ' (' . $item_item->discount_coupon * 100 . '%' . ')' : ''; ?>
                                <?php endif; ?>
                            </td>
                            <?php if (!$item->user_disabled_price AND !$use_new_view): ?>
                                <td align="right" valign="top">
                                    <?php if (!empty($item_item->discount_coupon_total)): ?>
                                        <?php echo '-'.Utils::currency_format($item_item->discount_coupon_total * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->discount_coupon_total * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                    <?php endif; ?>
                                </td>
                            <?php elseif ($use_new_view):?>
                                <td></td>
                            <?php endif; ?>
                            <td></td>
                            <?php if (!$item->user_disabled_price): ?>
                                <td></td>
                            <?php endif; ?>
                        </tr>
                        <?php if (Kohana::config('app.webshop.coupon.use_combine') AND Kohana::config('app.webshop.order_item_coupon_multiple_fields')): ?>
                            <?php for ($i = 1; $i <= Kohana::config('app.webshop.order_item_coupon_multiple_fields'); $i++): ?>
                                <?php $discount_coupon_total_field = 'discount_coupon_total_' . $i; ?>
                                <?php $discount_coupon_description_field = 'discount_coupon_description_' . $i; ?>
                                <?php $discount_coupon_code_field = 'discount_coupon_code_' . $i; ?>
                                <?php $discount_coupon_field = 'discount_coupon_' . $i; ?>
                                <?php if (!empty($item_item->$discount_coupon_total_field)): ?>
                                    <tr <?php if (!empty($item_item->discount_loyalty_code)):?>id="coupons_row" <?php endif;?> style="border-top: none !important;">
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td>
                                            <?php if(!empty($item_item->$discount_coupon_description_field) OR !empty($item_item->$discount_coupon_code_field)): ?>
                                                <b><?php echo 'Kupon: '; ?></b>
                                            <?php endif; ?>
                                            <?php if (!empty($item_item->$discount_coupon_description_field)): ?>
                                                <b><?php echo $item_item->$discount_coupon_description_field; ?></b><br/>
                                            <?php endif; ?>
                                            <?php if (!empty($item_item->$discount_coupon_code_field)): ?>
                                                <?php if (!empty($item_item->$discount_coupon_description_field)): ?><?php endif; ?><?php echo $item_item->$discount_coupon_code_field; ?><?php echo (!empty($item_item->$discount_coupon_field) AND Kohana::config('app.webshop.show_item_coupons_percent')) ? ' (' . $item_item->$discount_coupon_field * 100 . '%' . ')' : ''; ?>
                                            <?php endif; ?>
                                        </td>
                                        <?php if (!$item->user_disabled_price AND !$use_new_view): ?>
                                            <td align="right" valign="top">
                                                <?php if (!empty($item_item->$discount_coupon_total_field)): ?>
                                                    <?php echo '-'.Utils::currency_format($item_item->$discount_coupon_total_field * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->$discount_coupon_total_field * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                                <?php endif; ?>
                                            </td>
                                        <?php elseif ($use_new_view):?>
                                            <td></td>
                                        <?php endif; ?>
                                        <td></td>
                                        <?php if (!$item->user_disabled_price): ?>
                                            <td></td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endif; ?>
                            <?php endfor; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php $i++; ?>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php $tax_include_in_price = Kohana::config('app.catalog.product_tax_include_in_price'); ?>
            <?php $itemsextra_code = []; ?>
            <?php if (isset($item->itemsextra) AND count($item->itemsextra)): ?><?php foreach ($item->itemsextra as $item_item):?>
                <?php array_push($itemsextra_code, $item_item->category); ?>
                <?php
                if (in_array($item_item->category . '.' . $item_item->code, Kohana::config('app.webshop.extra_items_disable_print'))) {
                    continue;
                }

                $item_is_manual = ($item_item->category == 'shipping' AND $item_item->total == 0 AND !empty($item->shipping->category) AND $item->shipping->category == 'manual');
                ?>
                <?php if (Kohana::config('app.catalog.product_use_shipping_discount') AND $item_item->category == 'shipping' AND !empty($item_item->shipping_discount)): ?>
                    <tr>
                        <td align="center" valign="top"><?php echo $i; $i++;?></td>
                        <td valign="top"></td>
                        <td valign="top"></td>
                        <td valign="top"><strong><?php echo Arr::get($cmslabel, 'total_extra_shipping_discount', 'Popust na dostavu'); ?></strong></td>
                        <td valign="top" align="right">
                            <?php echo Utils::currency_format($item_item->shipping_discount * $item->exchange, $item->currency->display); ?>
                            <?php echo Utils::get_second_pricetag_string($item_item->shipping_discount, ['currency_code' => $item->currency->code, 'display_format' => 'simple']); ?>
                            <?php if (Kohana::config('app.catalog.multitax') AND $item_item->total): ?>
                                <br>
                                <small><?php echo Arr::get($cmslabel, 'tax') . ' ' . $item_item->tax() * 100; ?>
                                    %
                                </small>
                            <?php endif; ?>
                        </td>
                        <td valign="top" align="right"><span style="padding: 3px; border: 2px solid #ffffff">
                                    1&nbsp;
                            </span></td>
                        <td valign="top" align="right">
                            <?php echo Utils::currency_format($item_item->shipping_discount * $item->exchange, $item->currency->display); ?>
                            <?php echo Utils::get_second_pricetag_string($item_item->shipping_discount, ['currency_code' => $item->currency->code, 'display_format' => 'simple']); ?>
                        </td>
                    </tr>
                <?php endif;?>
                <tr>
                    <td align="center" valign="top"><?php echo $i; ?></td>
                    <td valign="top">
                        <?php if (Kohana::config('app.webshop.use_advanced_personalized_message') AND Kohana::config('app.webshop.extraitem_use_image') AND Kohana::config('app.webshop.order_extraitem_use_image') AND !empty($item_item->image)) :?>
                            <?php $thumb_size = Kohana::config('app.webshop.orderitem_use_image_thumb_size'); ?>
                            <?php $image_width = (!empty($thumb_size)) ? $thumb_size : 24; ?>
                            <?php $image_height = (!empty($thumb_size)) ? $thumb_size : 24; ?>
                            <img src="<?php echo $info['site_url'] . '/upload/' .$item_item->image; ?>" width="<?php echo $image_width; ?>" height="<?php echo $image_height; ?>" alt="">
                        <?php elseif (in_array($item_item->category, Kohana::config('app.webshop.extra_items_icon')) AND (!$item->show_additional_supplement_label AND $item_item->category == 'shipping')): ?>
                            <?php $thumb_size = Kohana::config('app.webshop.orderitem_use_image_thumb_size'); ?>
                            <?php $image_width = (!empty($thumb_size)) ? $thumb_size : 24; ?>
                            <?php $image_height = (!empty($thumb_size)) ? $thumb_size : 24; ?>
                            <img src="<?php echo $info['site_url']; ?>/shared/icons/webshop_<?php echo $item_item->category; ?><?php echo ($item_item->category == 'shipping' AND $item_item->total == 0 AND !$item_is_manual) ? '_0' : ''; ?>.png"
                                 width="<?php echo $image_width; ?>" height="<?php echo $image_height; ?>" alt="">
                        <?php endif; ?>
                    </td>
                    <td valign="top"></td>
                    <?php if ($item_is_manual): ?>
                        <td valign="top"
                            colspan="<?php if (!$item->user_disabled_price): ?>4<?php else: ?>2<?php endif; ?>">
                            <?php if ($item->show_additional_supplement_label AND $item_item->category == 'shipping') : ?>
                                <strong><?php echo Arr::get($cmslabel, 'additional_supplement', 'Dodatak na način plaćanja');?></strong>
                            <?php else : ?>
                                <strong><?php if (!in_array($item_item->category, Kohana::config('app.webshop.extra_items_disable_label')) AND Arr::get($cmslabel, $item_item->category)): ?><?php echo Arr::get($cmslabel, $item_item->category); ?>: <?php endif; ?><?php echo $item_item->title; ?></strong>
                                <p class="product-details">
                                    <?php if (!in_array($item_item->category, Kohana::config('app.webshop.extra_items_disable_code'))): ?>
                                        <em><?php echo $item_item->code; ?></em><?php endif; ?>
                                    <?php if ($item->show_shipping_info()): ?>
                                        <?php echo nl2br($item->show_shipping_info()); ?>
                                    <?php endif; ?>
                                </p>
                            <?php endif ?>
                        </td>
                    <?php else: ?>
                        <td valign="top">
                            <?php if ($item->show_additional_supplement_label AND $item_item->category == 'shipping') : ?>
                                <strong><?php echo Arr::get($cmslabel, 'additional_supplement', 'Dodatak na način plaćanja');?></strong>
                            <?php else : ?>
                                <strong><?php if (!in_array($item_item->category, Kohana::config('app.webshop.extra_items_disable_label')) AND Arr::get($cmslabel, $item_item->category)): ?><?php echo Arr::get($cmslabel, $item_item->category); ?>: <?php endif; ?><?php echo $item_item->title; ?></strong>
                                <?php if (!in_array($item_item->category, Kohana::config('app.webshop.extra_items_disable_code')) AND (!in_array($item_item->category, Kohana::config('app.webshop.extra_items_disable_code_for_client')) OR !empty($user->staff))): ?>
                                    <br/><em><?php echo $item_item->code; ?></em><?php endif; ?>
                                <?php if ($item_item->category == 'loyalty' AND $item->loyalty_point_used): ?><br/><em>
                                    Iskorišteno bodova: <?php echo $item->loyalty_point_used; ?></em><?php endif; ?>
                            <?php endif; ?>
                        </td>
                        <?php if (Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)): ?>
                            <td valign="top"></td>
                        <?php endif; ?>
                        <?php $shipping_tax_include_in_price = ((in_array(Kohana::config('app.sitename'), ['b2b.gumiimpex.hr', 'b2b.keindl-sport.hr', 'b2b.eurotrade.hr']) OR (Kohana::config('app.webshop.shipping_show_basic_instead_total') AND !empty($item->b2b))) AND $item_item->category == 'shipping') ? false : true; ?>
                        <?php if (!$item->user_disabled_price): ?>
                            <td align="right" valign="top">
                                <?php if (in_array($item_item->category, ['discount', 'shipping']) AND Kohana::config('app.webshop.discount_check_user')): ?>
                                    <?php echo Utils::currency_format( $item_item->total_basic * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->total_basic * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php else: ?>
                                    <?php echo Utils::currency_format((($tax_include_in_price AND $shipping_tax_include_in_price) ? $item_item->total : $item_item->total_basic) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string((($tax_include_in_price AND $shipping_tax_include_in_price) ? $item_item->total : $item_item->total_basic) * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php endif; ?>
                                <?php if (Kohana::config('app.catalog.multitax') AND  Kohana::config('app.webshop.order_mail_multitax_itemextra') AND $item_item->total AND ((!empty($show_tax_by_country) AND in_array($item->country->code, $show_tax_by_country)) OR empty($show_tax_by_country))): ?>
                                    <br>
                                    <?php if (!in_array($item_item->category, Kohana::config('app.webshop.order_view_itemsextra_without_tax'))): ?>
                                        <small><?php echo Arr::get($cmslabel, 'tax') . ' ' . $item_item->tax() * 100; ?>
                                            %
                                        </small>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </td>
                        <?php endif; ?>
                        <?php if ($order_covers_advanced): ?>
                            <td align="center" valign="top">
                                <?php echo $item_item->cover_qty; ?>
                            </td>
                        <?php endif; ?>
                        <td align="right" valign="top">
                                <span style="padding: 3px;">
                                    <strong>1</strong>&nbsp;
                                </span>
                        </td>
                        <?php if (!$item->user_disabled_price): ?>
                            <td align="right"
                            <?php if (in_array($item_item->category, ['discount', 'shipping']) AND Kohana::config('app.webshop.discount_check_user')): ?>
                                valign="top"><?php echo Utils::currency_format($item_item->total_basic * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item_item->total_basic, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></td>
                            <?php else: ?>
                                valign="top"><?php echo Utils::currency_format((($tax_include_in_price AND $shipping_tax_include_in_price) ? $item_item->total : $item_item->total_basic) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string((($tax_include_in_price AND $shipping_tax_include_in_price) ? $item_item->total : $item_item->total_basic) * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></td>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </tr>
                <?php $i++; ?>
            <?php endforeach; ?><?php endif; ?>

            <?php if (!$item->user_disabled_price): ?>
                <?php if (Kohana::config('app.webshop.order_view_total_layout') == '2'): ?>
                    <tr>
                        <td colspan="5" align="right" valign="top" class="highlight2 bold">
                            <big><?php echo Arr::get($cmslabel, $item->total_label(), 'Ukupno'); ?></big></td>
                        <td colspan="2" align="right" class="highlight2 price">
                            <strong><big data-total="all"><?php echo Utils::currency_format($item->total * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item->total * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></big></strong>
                            <?php if ($item->exchange != 1 AND $item->price_currency): ?>
                                <br/><?php echo Utils::currency_format($item->total, $item->price_currency->display) . Utils::get_second_pricetag_string($item->total * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endif; ?>

                <?php if (($item->tax AND Kohana::config('app.webshop.order_mail_show_tax') AND Kohana::config('app.webshop.order_mail_show_tax_included') AND ((!empty($show_tax_by_country) AND in_array($item->country->code, $show_tax_by_country)) OR empty($show_tax_by_country))) OR Kohana::config('app.catalog.ino_tax_custom_calculation')): ?>
                    <tr <?php if ($item->multitax() AND Kohana::config('app.webshop.order_mail_multitax_total')): ?> class="noborder"<?php endif; ?>>
                        <td colspan="<?php echo ((Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)) ? 6 : 5); ?>" align="right" valign="top"
                            class="bold"><?php echo Arr::get($cmslabel, 'total_minus_tax'); ?></td>
                        <?php if ($order_covers_advanced): ?>
                            <td align="center" valign="top" class="bold">
                                <?php echo $item_item->cover_qty; ?>
                            </td>
                        <?php endif; ?>
                        <td colspan="2" align="right" class="price" data-total_basic="all">
                            <?php if ($content_category == 'order'): ?>
                                <?php if (Kohana::config('app.catalog.product_use_margin_amount') AND $item->total_items_margin_basic >= 0): ?>
                                    <?php echo Utils::currency_format($item->total_items_margin_basic * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item->total_items_margin_basic * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php else: ?>
                                    <?php $total_minus_tax_field = (!empty($item->b2b)) ? Kohana::config('app.webshop.order_total_without_tax_field') : 'total_basic'; ?>
                                    <?php echo Utils::currency_format($item->$total_minus_tax_field * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item->$total_minus_tax_field * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php endif; ?>
                            <?php elseif ($content_category == 'additpayment'): ?>
                                <?php echo Utils::currency_format(($item->total / ($item->tax + 1)) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string(($item->total / ($item->tax + 1)) * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php $itemsextra_hide_tax_percentage = array_intersect(Kohana::config('app.webshop.order_view_itemsextra_hide_multitax_percentage'), $itemsextra_code); ?>
                    <?php if ($item->multitax() AND Kohana::config('app.webshop.order_mail_multitax_total')): ?>
                        <tr class="small">
                            <td colspan="7" align="right" valign="top" class="price">
                                <small><em><?php echo ($itemsextra_hide_tax_percentage) ? preg_replace('/\([^\)]+\)(\R|$)/', '', $item->total_basic_taxranks_description) : $item->total_basic_taxranks_description; ?></em></small>
                            </td>
                        </tr>
                    <?php endif; ?>
                    <tr <?php if ($item->multitax() AND Kohana::config('app.webshop.order_mail_multitax_total')): ?> class="noborder"<?php endif; ?>>
                        <td colspan="<?php echo ((Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)) ? 6 : 5); ?>" align="right" valign="top" class="bold"><?php echo Arr::get($cmslabel, 'tax'); ?> <?php if (($calculate_tax = $item->calculate_tax()) !== NULL): ?>(<?php echo $calculate_tax * 100 ?>%)<?php endif; ?></td>
                        <?php if ($order_covers_advanced): ?>
                            <td align="center" valign="top" class="bold">
                                <?php echo $item_item->cover_qty; ?>
                            </td>
                        <?php endif; ?>
                        <td colspan="2" align="right" class="price" data-total_tax="all">
                            <?php if ($content_category == 'order'): ?>
                                <?php if (Kohana::config('app.catalog.product_use_margin_amount') AND $item->total_items_margin_tax >= 0): ?>
                                    <?php echo Utils::currency_format($item->total_items_margin_tax * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item->total_items_margin_tax * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php else: ?>
                                    <?php echo Utils::currency_format($item->total_tax * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item->total_tax * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                                <?php endif; ?>
                            <?php elseif ($content_category == 'additpayment'): ?>
                                <?php echo Utils::currency_format(($item->total - ($item->total / ($item->tax + 1))) * $item->exchange, $currency_display) . Utils::get_second_pricetag_string(($item->total - ($item->total / ($item->tax + 1))) * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php if ($item->multitax() AND Kohana::config('app.webshop.order_mail_multitax_total')): ?>
                        <tr class="small">
                            <td colspan="7" align="right" valign="top" class="price">
                                <small><em><?php echo ($itemsextra_hide_tax_percentage) ? preg_replace('/\([^\)]+\)(\R|$)/', '', $item->total_tax_taxranks_description) : $item->total_tax_taxranks_description; ?></em></small>
                            </td>
                        </tr>
                    <?php endif; ?>
                <?php endif; ?>

                <?php if (Kohana::config('app.webshop.order_view_total_layout') == '1'): ?>
                    <tr>
                        <td colspan="<?php echo ((Kohana::config('app.webshop.show_supplier_code_for_admin_on_orders_view') AND !empty($user->staff)) ? 6 : 5); ?>" align="right" valign="top" class="highlight1 bold">
                            <big><?php echo Arr::get($cmslabel, ((!empty($show_tax_by_country) AND in_array($item->country->code, $show_tax_by_country)) OR empty($show_tax_by_country)) ? $item->total_label() : 'total_tax_in_price', 'Ukupno'); ?></big></td>
                        <?php if ($order_covers_advanced): ?>
                            <td align="center" valign="top" class="highlight1 bold">
                                <?php echo $item_item->cover_qty; ?>
                            </td>
                        <?php endif; ?>
                        <td colspan="2" align="right" class="highlight1 price">
                            <strong><big
                                    data-total="all"><?php echo Utils::currency_format($item->total * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($item->total * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?></big></strong>
                            <?php if ($item->exchange != 1 AND $item->price_currency): ?>
                                <br/><?php echo Utils::currency_format($item->total, $item->price_currency->display) . Utils::get_second_pricetag_string($item->total * $item->exchange, ['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]); ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endif; ?>
            <?php endif; ?>
        </table>
        <?php if (empty(Kohana::config('app.webshop.order_mail_show_tax_included'))) : ?>
            <div style="text-align: right;">
                <strong> <?php echo Arr::get($cmslabel, 'order_mail_show_tax_included', ''); ?></strong>
            </div>
        <?php endif; ?>
        <?php $this->endblock('order_items'); ?>

        <?php if (!empty($status_changed)): ?>
            <?php echo Arr::get($cmslabel, 'success_status_change', 'Uspješno ste promijenili status narudžbe u ' . Utf8::strtolower($item->status->title($item->lang))); ?>
        <?php endif; ?>

        <?php if ($redemption_can_change): ?>
            <div class="form-row">
                <button type="submit" name="confirm" value="1" class="btn"><strong><?php echo __('Potvrdi'); ?></strong>
                </button>
                <button type="submit" name="cancel" value="1"
                        class="btn-secondary"><?php echo __('Odustani'); ?></button>
            </div>
            </form>
        <?php endif; ?>

        <br class="clear"/>&nbsp;

        <?php if ($content_category == 'order' AND isset($item->personmessage) AND $item->personmessage): ?>
            <p><strong><?php echo Arr::get($cmslabel, 'personmessage', 'Personalizirane poruke (naziv proizvoda i poruke odvojene s znakom |)'); ?>:</strong><br/><?php echo nl2br($item->personmessage); ?></p>
        <?php endif; ?>

        <?php if ($item->get_file_url()): ?>
            <p>
                <strong><?php echo Arr::get($cmslabel, 'order_file', 'Datoteka'); ?></strong>: <a href="<?php echo $item->get_file_url(); ?>" target="_blank"><?php echo $item->get_file_url(); ?></a>
                <?php if (!empty($item->orderfile_message)): ?>
                    <br><strong><?php echo Arr::get($cmslabel, 'order_file_message', 'Poruka uz datoteku'); ?></strong>: <?php echo $item->orderfile_message; ?>
                <?php endif; ?>
            </p>
        <?php endif; ?>

        <?php
        if (Kohana::config('app.webshop.issuer_as_acquirer')) {
            $credit_card_options = Kohana::config('app.payment.creditcard_options');
            if (!empty($credit_card_options)) {
                $credit_card_name = $credit_card_options[strtolower($item->paid_cc_type)];
            }
        }
        ?>

        <?php if ($item->get_payment_display()): ?>
            <p>
                <strong><?php echo Arr::get($cmslabel, 'payment'); ?></strong>: <?php echo (!Kohana::config('app.webshop.order_view_payment_ignore_strtolower')) ?  Utf8::strtolower($item->payment->description($item->lang)) : $item->payment->description($item->lang); ?>
                <?php if ($item->status->code == Kohana::config('app.webshop.status.paid')): ?>
                    (<?php echo date('d.m.Y.', ((isset($item->date_payment_1) AND $item->date_payment_1) ? $item->date_payment_1 : (($item->paid_datetime) ? $item->paid_datetime : $item->datetime_created))); ?><?php if (Kohana::config('app.webshop.issuer_as_acquirer') AND !empty($credit_card_name)) : ?><?php if ($item->paid_cc_acquirer): ?>, <?php echo $item->paid_cc_acquirer; ?><?php endif; ?> <?php echo strtoupper($credit_card_name); ?><?php else: ?><?php if ($item->paid_cc_type): ?>, <?php echo $item->paid_cc_type; ?><?php endif; ?><?php endif; ?><?php if ($item->paid_cc_installments > 1): ?>, br. rata: <?php echo $item->paid_cc_installments; ?><?php endif; ?>)
                <?php endif; ?>
                <?php if ($item->show_payment_info() AND !$item->show_payment_transfer() AND !$item->payment->is_credit_card AND ($payment_info = $item->payment->payment_info($item->lang))): ?>
                    <br/><?php echo nl2br($payment_info); ?>
                    <?php if (!empty($item->payment->transfer_address) AND !empty($item->payment->transfer_bank_number) AND !empty($item->paid_reference_number)): ?>
                        <br/>
                        <br/><?php echo Arr::get($cmslabel, 'transfer_address', 'Primatelj uplate'); ?>:
                        <br/><?php echo nl2br($item->payment->transfer_address); ?>
                        <br/>
                        <br/><?php echo Arr::get($cmslabel, 'transfer_bank_number', 'IBAN primatelja'); ?>: <strong><?php echo $item->payment->transfer_bank_number; ?></strong>
                        <br/><?php echo Arr::get($cmslabel, 'transfer_model_paid_reference_number', 'Model i poziv na broj'); ?>: <strong><?php echo $item->payment->transfer_model; ?> <?php echo $item->paid_reference_number; ?></strong>                    <?php endif; ?>
                <?php endif; ?>
                <?php if (($payment_url = $item->get_payment_url())): ?>
                    <?php if (Kohana::config('app.webshop.order_clientmail_orderdetail_payment_link')): ?>
                        <br/><br/><?php echo Arr::get($cmslabel, 'payment_url'); ?>:<br/><a href="<?php echo $payment_url; ?>"><strong><?php echo $payment_url; ?></strong></a>
                    <?php else: ?>
                        <br/><?php echo str_replace('%URL%', $payment_url, Arr::get($cmslabel, 'payment_url_custom')); ?>
                    <?php endif; ?>
                <?php endif; ?>
            </p>
        <?php endif; ?>

        <?php if (Kohana::config('app.webshop.use_not_fiscalised')): ?>
            <p><?php echo Arr::get($cmslabel, 'not_fiscalised', 'OVO NIJE FISKALIZIRANI RAČUN'); ?></p>
        <?php endif; ?>

        <?php if ($content_category == 'order'): ?>
            <p>
                <?php if (Kohana::config('app.webshop.shipping_display') AND isset($item->shipping->id) AND $item->shipping->id): ?>
                    <?php if ($item->is_shipping_pickup()): ?>
                        <strong><?php echo $item->shipping->description($item->lang); ?></strong>
                    <?php else: ?>
                        <strong><?php echo Arr::get($cmslabel, 'shipping'); ?></strong>: <?php echo $item->shipping->description($item->lang); ?>
                    <?php endif; ?>

                    <?php if ($item->show_shipping_info()): ?>
                        <br/><?php echo nl2br($item->show_shipping_info()); ?>
                    <?php endif; ?>

                    <?php if ($item->show_location_info()): ?>
                        <?php if (($location_info = $item->shipping->location_info($item->lang))): ?>
                            <br/><br/><?php echo nl2br($location_info); ?>
                        <?php endif; ?>
                        <?php if (!empty($item->shipping_pickup_location->id)): ?>
                            <?php $pickup_location = Widget_Location::points(['lang' => $info['lang'], 'id' => $item->shipping_pickup_location->id, 'single' => true]); ?>
                            <?php if ($pickup_location): ?>
                                <br/><?php echo $pickup_location['title']; ?>
                                <?php if (!empty($pickup_location['address'])): ?><br/><?php echo nl2br(trim(strip_tags($pickup_location['address']))); ?><?php endif; ?>
                                <?php if (!empty($pickup_location['contact'])): ?><br/><?php echo nl2br(trim(strip_tags(str_replace("\n\n", "\n", $pickup_location['contact'])))); ?><?php endif; ?>
                                <?php if (!empty($pickup_location['business_hour'])): ?><br/><br/><?php echo nl2br(trim(strip_tags($pickup_location['business_hour']))); ?><?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                    <?php if ($item->shipping->api_class == 'woltdriveapi'): ?>
                        <br/>
                        <strong><?php echo Arr::get($cmslabel, 'estimated_delivery_time', 'Predviđeno vrijeme dostave: ');?></strong><?php echo Date::humanize($item->shipping_delivery_datetime, 'custom', 'd.m.Y - H:i'); ?>
                    <?php endif; ?>
                    <?php if (Kohana::config('app.webshop.shipping_use_parcel_locker') AND !empty($item->shipping->parcel_locker)): ?>
                        <br/><br/>
                        <strong><?php echo Arr::get($cmslabel, 'selected_locker', 'Odabrani paketomat: '); ?></strong><br/>
                        <?php echo $item->parcel_locker_id; ?><br/>
                        <?php echo $item->parcel_locker_title; ?><br/>
                        <?php echo $item->parcel_locker_address . ', ' . $item->parcel_locker_zipcode . ' ' . $item->parcel_locker_city; ?><br/>
                    <?php endif; ?>
                <?php endif; ?>
                <?php if (($tracking_url = $item->get_shipping_url())): ?>
                    <?php if (filter_var($tracking_url, FILTER_VALIDATE_URL)): ?>
                        <br/><a href="<?php echo $tracking_url; ?>" target="_blank"><strong><?php echo Arr::get($cmslabel, 'tracking_url', 'Prati pošiljku na'); ?> <?php echo $tracking_url; ?></strong></a>
                    <?php else: ?>
                        <br/><strong><?php echo Arr::get($cmslabel, 'tracking_code', 'Broj praćenja pošiljke'); ?>: <?php echo $tracking_url; ?></strong>
                    <?php endif; ?>
                <?php endif; ?>
            </p>
        <?php endif; ?>

        <?php if(Kohana::config('app.webshop.order_use_used_coupon_code') AND !empty($item->used_coupon_code)): ?>
            <strong><?php echo Arr::get($cmslabel, 'coupon'); ?></strong>: <?php echo $item->used_coupon_code; ?>
        <?php endif; ?>

        <?php if (!Kohana::config('app.webshop.shipping_display')): ?>
            <br/><strong><?php echo Arr::get($cmslabel, 'order_noshipping_nopayment'); ?></strong>
        <?php endif; ?>

        <?php if ($item->notes_for_client): ?>
            <br><strong><?php echo Arr::get($cmslabel, 'notes_for_client'); ?>:</strong><br/><?php echo $item->notes_for_client_html(); ?>
        <?php endif; ?>

        <?php if (Kohana::config('app.webshop.order_use_pg_parameters') AND !empty($item->pg_parameters)): ?>
            <br/><br/>
            <?php $pg_parameters = json_decode($item->pg_parameters, true); ?>
            <?php foreach ($pg_parameters as $parameter_name => $parameter_value): ?>
                <strong><?php echo $parameter_name; ?>:</strong> <?php echo $parameter_value; ?><br/>
            <?php endforeach; ?>
            <br/>
        <?php endif; ?>

        <?php if ($content_category == 'order'): ?>
            <?php if ($item->shipping_date): ?>
                <br/><br/><strong><?php echo Arr::get($cmslabel, 'shipping_date_time', __('Datum i vrijeme dostave')); ?></strong>: <?php echo nl2br(Date::humanize($item->shipping_date)); ?><?php if ($item->shipping_time): ?>, <?php echo nl2br($item->shipping_time); ?><?php endif; ?>
                <br/>
            <?php endif; ?>
            <?php if ($item->shipping_message): ?>
                <br/><strong><?php echo Arr::get($cmslabel, 'shipping_message', __('Napomena za dostavu')); ?></strong>: <?php echo nl2br($item->shipping_message); ?>
            <?php endif; ?>
            <?php if (!empty($item->giftwrapping_note)): ?>
                <br/><strong><?php echo Arr::get($cmslabel, 'giftwrapping_note', __('Napomena za zamatanje')); ?></strong>: <?php echo nl2br($item->giftwrapping_note); ?>
            <?php endif; ?>
            <?php if ($item->message): ?>
                <br/><strong><?php echo Arr::get($cmslabel, 'client_message', __('Poruka kupca')); ?></strong>: <?php echo nl2br($item->message); ?>
            <?php endif; ?>

            <?php if (isset($item->itemwishlist) AND count($item->itemwishlist)): ?>
                <h2><?php echo Arr::get($cmslabel, 'wishlist_shipping_address'); ?>:</h2>
                <?php foreach ($item->itemwishlist AS $wisthlistitem): ?>
                    <br/><?php echo Arr::get($cmslabel, 'products'); ?>: <?php echo $wisthlistitem->wishlistitem->product->admin_title; ?>
                    <br/><?php echo Arr::get($cmslabel, 'wishlist_shipping'); ?>: <?php echo $wisthlistitem->address; ?>
                    <br/><?php echo Arr::get($cmslabel, 'quantity'); ?>: <?php echo $wisthlistitem->qty; ?>
                <?php endforeach; ?>
            <?php endif; ?>

            <?php if (isset($item->itemcoupons) AND count($item->itemcoupons)): ?>
                <h2><?php echo Arr::get($cmslabel, 'gifts'); ?></h2>
                <?php foreach ($item->itemcoupons AS $coupon): ?>
                    <p>
                        <strong><?php echo Arr::get($cmslabel, 'coupon_code'); ?>: <?php echo $coupon->code; ?></strong>, vrijednost: <?php echo ($coupon->type == 'f') ? Utils::currency_format($coupon->coupon_value * $item->exchange, $currency_display) . Utils::get_second_pricetag_string($coupon->coupon_value * $item->exchange,['currency_code' => $currency_code, 'display_format' => 'admin', 'order_created_date' => $item->datetime_created]) : $coupon->coupon_value . '%'; ?>
                        <br/><?php echo Arr::get($cmslabel, 'recipient'); ?>: <?php echo $coupon->recipient(); ?>
                    </p>
                <?php endforeach; ?>
            <?php endif; ?>
        <?php endif; ?>

        <?php if (Kohana::config('app.webshop.shipping_display') AND $item->show_payment_transfer() AND empty($hide_payment)): ?>
            <div style="page-break-after:always;"></div>
            <?php echo View::factory('webshop/widget/payment_transfer', ['order' => $item]); ?>
            <br/>
            <?php if (Kohana::config('payment.transfer_banks.show_banks')) : ?>
                <select onchange="this.options[this.selectedIndex].value && (window.open(this.options[this.selectedIndex].value));" class="hideprint">
                    <option>Odaberi banku za plaćanje internet bankarstvom</option>
                    <optgroup label="Građani">
                        <?php $banks_1 = Kohana::config('payment.transfer_banks.citizens'); ?>
                        <?php foreach ($banks_1 AS $bank_title => $bank_url): ?>
                            <option value="<?php echo $bank_url; ?>"><?php echo $bank_title; ?></option>
                        <?php endforeach; ?>
                    </optgroup>
                    <optgroup label="Poslovni korisnici">
                        <?php $banks_2 = Kohana::config('payment.transfer_banks.business'); ?>
                        <?php foreach ($banks_2 AS $bank_title => $bank_url): ?>
                            <option value="<?php echo $bank_url; ?>"><?php echo $bank_title; ?></option>
                        <?php endforeach; ?>
                    </optgroup>
                </select>
            <?php endif; ?>
            &nbsp;&nbsp;&nbsp;<a href="<?php echo $item->get_absolute_url(); ?>?mode=payment" target="_blank" class="hideprint"><img src="<?php echo Utils::site_url(Kohana::config('app.language')); ?>shared/icons/print.png" width="16" height="16" style="vertical-align: middle; "/> <?php echo Arr::get($cmslabel, 'print_payment', 'Ispis naloga za uplatu'); ?>
            </a>
        <?php endif; ?>

        <?php if (Kohana::config('app.webshop.order_count_alert') AND !empty($item->orders_status) AND !empty($info["user_staff"])): ?><br>
            <br/>
            <?php echo "<strong>Narudžbe kupca po statusima:</strong>"?><br>
            <?php $decoded_status = json_decode($item->orders_status);?>
            <?php foreach($decoded_status as $status_title => $status_count): ?>
                <?php echo $status_title . ": " . $status_count . "<br>";   ?>
            <?php endforeach; ?>
        <?php endif; ?>

        <?php echo Arr::get($cmslabel, 'order_view_footer'); ?>

        <p style="text-align: right;">
            <a href="javascript:print();" class="print hideprint"><img src="<?php echo Utils::site_url(Kohana::config('app.language')); ?>shared/icons/print.png" width="16" height="16" style="vertical-align: middle; "/> <?php echo Arr::get($cmslabel, 'print_order', 'Ispiši narudžbu'); ?>
            </a>
        </p>
    <?php endif; ?>
<?php endif; ?>

<?php if (in_array($mode, ['', 'barcode'])): ?>
    <script>
        var loyalty_code_type = "<?php echo Kohana::config('app.auth.loyalty_code_type'); ?>";
        var currency_display = '<?php echo $currency_display; ?>';
    </script>
    <?php echo Html::media('jquery,barcode,/shared/jquery-cms/webshoporder.js', 'js'); ?>
<?php endif; ?>

<?php if (Arr::get($_GET, 'print')): ?>
    <script>print();</script>
<?php endif; ?>

<?php if (!empty($show_item_coupons)): ?>
    <style>
        .item_row td {border-bottom: none}
    </style>
<?php endif; ?>

<?php if (Kohana::config('app.webshop.order_document_use_footer_labels') AND !empty($info['user_staff'])) : ?>
    <?php $labels = Cms::labels($item->lang, 'like', 'view_order_label'); ?>
    <?php foreach ($labels as $label_code => $label_value): ?>
        <?php if($label_code=='view_order_label_1'): ?>
            <?php $now = date("d.m.Y \u\ H:i", time()); ?>
            <?php $user_and_time=str_replace(array('%CURRENT_USER%', '%CURRENT_DATETIME%'), array($info['user_fullname'], $now), $label_value); ?>
            <?php echo $user_and_time . "<br><br>"; ?>
        <?php else: ?>
            <?php echo $label_value . "<br><br>"; ?>
        <?php endif; ?>
    <?php endforeach; ?>
<?php endif; ?>
<?php $this->endblock('content'); ?>