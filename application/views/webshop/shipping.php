<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-checkout page-checkout-step2<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<?php echo Html::media('/media/cart.css', 'css'); ?>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('header'); ?>
	<div class="wc-header">
		<div class="wc-wrapper">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
		</div>
	</div>
<?php $this->endblock('header'); ?>

<?php $this->block('content_layout'); ?>
	<div class="wc-container">
		<div class="w-sidebar-container">
			<div class="w-sidebar-support">
				<p class="sidebar-help-title"><?php echo Arr::get($cmslabel, 'help_and_support_title'); ?></p>
				<?php echo Arr::get($cmslabel, 'help_and_support'); ?>
				<div class="w-sidebar-cards"><?php echo Arr::get($cmslabel, 'cards'); ?></div>
			</div>

			<div class="w-sidebar-note">
				<?php echo Arr::get($cmslabel, 'safe_purchase'); ?>
				<div class="w-sidebar-payment-note">
					<?php echo Arr::get($cmslabel, 'payment_note'); ?>
				</div>
			</div>
		</div>
		<?php if(!empty(Arr::get($cmslabel, 'webshop_disabled'))): ?>
			<div class="webshop-disabled-note"><?php echo Arr::get($cmslabel, 'webshop_disabled'); ?></div>
		<?php else: ?>
		<form class="step2 form-label ajax_siteform ajax_siteform_loading form-cart" action="#webshop_form" method="post" name="webshop" id="webshop_form" accept-charset="utf-8" data-tracking_gtm_checkout="2|<?php echo Utils::token_id(); ?>" data-webshop_autocomplete="zipcode_city_location">

			<!-- Steps -->
			<?php echo View::factory('webshop/widget/step', ['current_step' => 2]); ?>

			<!-- Column 1 -->
			<div class="wc-col wc-col1 wc-step2-col1">
				<div class="col-cont">
					<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'customer_details', 'Podaci za dostavu proizvoda'); ?></h2>
					<?php echo Arr::get($cms_page, 'content'); ?>
					<div class="global_error global-error"<?php if ( ! count($errors)): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<?php $i = 1; ?>
					<?php foreach ($customer_fields as $field): ?>
						<?php if (in_array($field, ['location', 'city'])) {continue;} ?>
						<div class="<?php if ($field != 'shipping'): ?>field<?php endif; ?><?php if($field == 'zipcode'): ?> field-autocomplete<?php endif; ?> field-<?php echo $field; ?><?php if($i % 2 == 0): ?> last<?php endif; ?><?php if(count($available_shippings) <= 1): ?> single<?php endif; ?>">
							<?php if ($field == 'zipcode'): ?>
								<?php $error = Valid::get_error($field, $errors); ?>
								<label for="field-location" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'zipcode_city', 'Poštanski broj / mjesto'); ?><?php if (in_array($field, $request_fields)): ?> <?php endif; ?></label>
								<input type="text" name="location" id="field-location" value="<?php echo Arr::get($customer_data, 'city') . (!empty($customer_data['zipcode']) ? ' (' . $customer_data['zipcode'] . ')' : ''); ?>"/>
								<input type="hidden" name="zipcode" id="field-zipcode" value="<?php echo Arr::get($customer_data, 'zipcode'); ?>"/>
								<input type="hidden" name="city" id="field-city" value="<?php echo Arr::get($customer_data, 'city'); ?>"/>
								<span class="phone-tooltip location-tooltip"><?php echo Arr::get($cmslabel, 'zipcode_city_tooltip'); ?></span>
								<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
							
								<div data-shipping_infos class="shipping-restriction-note"><?php echo (($data->shipping->id > 2) ? $data->shipping->shipping_infos[$info['lang']] : ''); ?></div>
							<?php else: ?>
								<?php $error = Valid::get_error($field, $errors); ?>
								<?php $label = (Arr::get($cmslabel, 'checkout_'.$field)) ? Arr::get($cmslabel, 'checkout_'.$field) : Arr::get($cmslabel, $field); ?>
								<?php if ($field != 'shipping'): ?>
									<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo $label; ?></label>
								<?php endif; ?>
								<?php if ($field == 'shipping'): ?>
									<?php /* 
									<div class="step2-shipping">
										<h2 class="wc-subtitle"><?php echo Arr::get($cmslabel, 'shipping_type', 'Način dostave'); ?></h2>
										<?php echo Form::select_as_radio2($field, $available_shippings, $data->shipping->id); ?>
									</div>
									*/ ?>
								<?php else: ?>
									<?php echo $data->input($field, 'form'); ?>
								<?php endif; ?>
								<?php if($field == 'phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'Samo za potrebe dostavne službe'); ?></span><?php endif; ?>
								<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
							<?php endif; ?>
						</div>
						<?php $i++; ?>
					<?php endforeach; ?>
				</div>
			</div>

			<!-- Column 3 -->
			<div class="wc-col wc-col3 wc-step2-col3">
				<div class="col-cont">
					<?php if (Kohana::config('app.webshop.bill_on_shipping_step')): ?>
						<?php if (sizeof($customer_bill_fields)): ?>
							<?php $b = 0; ?>
							<?php foreach ($customer_bill_fields as $field): ?>
								<?php if(in_array($field, ['b_location', 'b_city'])) {continue;} ?>
								<?php $error = Valid::get_error($field, $errors); ?>
								<?php if($field == 'b_same_as_shipping' OR $field == 'b_r1'): ?>
									<p class="field field-<?php echo $field; ?>">
										<?php echo $data->input($field, 'form'); ?>
										<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?></label>
										<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
									</p>
								<?php else: ?>
									<?php if ($field == 'b_zipcode'): ?>
										<p class="field field-autocomplete field-<?php echo $field; ?>">
											<label for="field-b_location" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'zipcode_city', 'Poštanski broj / mjesto'); ?><?php if (in_array($field, $request_fields)): ?> <?php endif; ?></label>
											<input type="text" name="b_location" id="field-b_location" value="<?php echo Arr::get($customer_data, 'b_city') . (!empty($customer_data['b_zipcode']) ? ' (' . $customer_data['b_zipcode'] . ')' : ''); ?>" />
											<input type="hidden" name="b_zipcode" id="field-b_zipcode" value="<?php echo Arr::get($customer_data, 'b_zipcode'); ?>" />
											<input type="hidden" name="b_city" id="field-b_city" value="<?php echo Arr::get($customer_data, 'b_city'); ?>" />
											<span class="phone-tooltip location-tooltip"><?php echo Arr::get($cmslabel, 'zipcode_city_tooltip'); ?></span>
										</p>
									<?php else: ?>
										<p class="field field-<?php echo $field; ?><?php if($b % 2 == 0): ?> last<?php endif; ?>">
											<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, substr($field, 2, strlen($field))); ?></label>
											<?php echo $data->input($field, 'form'); ?>
											<?php if($field == 'b_phone'): ?><span class="phone-tooltip"><?php echo Arr::get($cmslabel, 'phone_tooltip', 'Samo za potrebe dostavne službe'); ?></span><?php endif; ?>
											<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
										</p>
									<?php endif; ?>
								<?php endif; ?>
								<?php $b++; ?>
							<?php endforeach; ?>
						<?php else: ?>
							<?php echo Arr::get($cmslabel, 'same_as_shipping', 'Podaci za dostavu računa jednaki su podacima za dostavu proizvoda'); ?>
						<?php endif; ?>
					<?php endif; ?>
				</div>

				<div class="clear wc-btns">
					<button class="btn btn-checkout" type="submit"><span><?php echo Arr::get($cmslabel, 'goto_step3_button'); ?></span></button>
					<div class="wc-step-note"><?php echo Arr::get($cmslabel, 'step2_note', 'Odabir načina plaćanja i potvrda narudžbe'); ?></div>
				</div>
				<div class="clear"></div>
			</div>
		</form>
		<?php endif; ?>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('footer'); ?> <?php $this->endblock('footer'); ?>
<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>