<style>
	.gdpr-container-wrapper{background: #fff; box-shadow: 0px 0px 20px rgba(0,0,0,.2); position: fixed; max-height: 100vh; overflow: auto; left: 0; bottom: 0; right: 0; z-index: 100;}
	.cookie-warning-wrapper{padding: 20px 30px;}
	.gdpr-popup-content{padding: 0 30px 20px; font-size: 13px; line-height: 18px; max-width: 60vw;}
	.no-cookie .gdpr-popup-content{padding-top: 30px;}
	#gdpr_configurator{display: none;}
	#gdpr_configurator label{width: 100%; display: block; float: none;}
	.gdpr-popup-object-cnt{padding-top: 10px;}
	.gdpr-popup-field{padding-bottom: 20px;}
	.gdpr-popup-field:last-child{padding-bottom: 0;}
	.gdpr-popup-field p:last-child{padding-bottom: 0;}
	.gdpr-popup-cnt{overflow: auto; padding: 20px 25px; max-height: 40vh; background: #f7f7f7; border: 1px solid #ededed;}
	.gdpr-popup-close{width: 40px; height: 40px; box-shadow: none; position: absolute; top: 15px; right: 20px; font-size: 0; background: #fff;}
	.gdpr-popup-close span{width: 20px; height: 20px; display: block; position: absolute; top: 10px; left: 10px; transform: rotate(45deg); -webkit-transform: rotate(45deg); -moz-transform: rotate(45deg);}
	.gdpr-popup-close span:before, .gdpr-popup-close span:after{content:""; background: #000; display: block; width: 100%; height: 1px; position: absolute; top: 10px; left: 0;}
	.gdpr-popup-close span:after{width: 1px; height: 100%; left: 10px; top: 0;}
	.gdpr-popup-btn{height: 50px; padding: 0 30px; margin-top: 20px; display: inline-flex; justify-content: center; align-items: center; font-size: 16px;}
	@media screen and (max-width: 1200px) {
		.gdpr-popup-content{max-width: 93vw; padding: 0px 15px 15px;}
		.no-cookie .gdpr-popup-content{padding-top: 20px;}
		.gdpr-popup-close{top: 10px; right: 5px;}
		.cookie-warning-wrapper{padding: 20px 60px 20px 15px;}
	}
	@media screen and (max-width: 700px) {
		.gdpr-popup-content{max-width: 100vw;}
		.cookie-warning-wrapper{padding: 15px 15px 10px 15px;}
		.cookie-warning-wrapper p:first-child{padding-right: 40px;}
		.cookie-warning-wrapper .btn{display: block; width: 100%; margin: 2px 0;}
		.gdpr-popup-cnt{padding: 15px; max-height: 25vh;}
		.gdpr-popup-btn{width: 100%;}
	}
</style>

<!-- GDPR konfigurator -->
<div id="gdpr_configurator">
	<div class="gdpr-popup-content">
		<a class="gdpr_configurator_button gdpr-popup-close" href="javascript:void(0);"><span><?php echo Arr::get($cmslabel, 'gdpr_popup_close', 'Zatvori'); ?></span></a>
		<?php if(Arr::get($cmslabel, 'gdpr_popup_header')): ?>
			<div class="gdpr-popup-header">
				<?php echo Arr::get($cmslabel, 'gdpr_popup_header'); ?>
			</div>
		<?php endif; ?>
		<form id="gdpr_configurator_form" data-gdpr_ajax="1" data-gdpr_google4="1" data-gdpr_google4_no_refresh="1" action="<?php echo Utils::app_absolute_url($info['lang'], 'gdpr', 'user', false); ?>?redirect=<?php echo $this->info['redirect_url']; ?>" method="POST">
			<div class="gdpr-popup-cnt">
				<?php echo str_replace('{OBJECTS}', $gdpr_objects_html, $gdpr_template['content']); ?>
			</div>

			<div class="gdpr-popup-btns">
				<button class="btn gdpr-popup-btn gdpr-btn-submit" type="submit" name="submit" value="1"><?php echo Arr::get($cmslabel, 'gdpr_popup_save', 'Spremi'); ?></button>
				<?php /*if (!empty($gdpr_approved_total)): ?>
					<a href="/gdpr/download/?lang=<?php echo $info['lang']; ?>&redirect=<?php echo $this->info['redirect_url']; ?>" class="btn gdpr-popup-btn gdpr-btn-download"><?php echo Arr::get($cmslabel, 'gdpr_popup_download', 'Preuzmi podatke'); ?></a>
					<a href="/gdpr/delete/?lang=<?php echo $info['lang']; ?>&redirect=<?php echo $this->info['redirect_url']; ?>" class="btn gdpr-popup-btn gdpr-btn-delete"><?php echo Arr::get($cmslabel, 'gdpr_popup_delete', 'Obriši podatke'); ?></a>
				<?php endif;*/ ?>
			</div>
		</form>
	</div>
</div>
