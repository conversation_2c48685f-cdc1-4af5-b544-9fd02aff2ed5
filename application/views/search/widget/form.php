<div class="sw">
	<a href="javascript:;" class="sw-toggle"></a>
	<form class="sw-form" action="<?php echo Utils::app_absolute_url($info['lang'], 'catalog'); ?>" method="get">
		<input class="sw-input" name="search_q" type="text" placeholder="<?php echo Arr::get($cmslabel, 'enter_search_term', 'Pretraživanje...'); ?>" />
		
		<?php $search_menu = Widget_Cms::menu(['lang' => $info['lang'], 'code' => 'search_list']); ?>
		<?php if(!empty($search_menu)): ?>
			<div class="sw-list-container">
				<div class="sw-list-title"><?php echo Arr::get($cmslabel, 'search_list_title'); ?></div>
				<ul class="sw-list">
					<?php foreach($search_menu as $search_menu_item): ?>
						<li><a href="<?php echo $search_menu_item['url']; ?>"><span><?php echo $search_menu_item['title']; ?></span></a></li>
					<?php endforeach; ?>
				</ul>
			</div>
		<?php endif; ?>

		<div id="field-search_q-autocomplete_position" class="autocomplete-container sw-autocomplete-container" style="display: none">
			<div class="autocomplete-wrapper">
				<div class="autocomplete-col1">
					<div class="autocomplete-cnt catalogproduct">
						<!-- <div class="autocomplete-title"><?php //echo Arr::get($cmslabel, 'autocomplete_products'); ?></div> -->
						<ul class="ui-autocomplete ui-autocomplete1" data-autocomplete_contenttype="catalogproduct"></ul>
					</div>
				</div>
				<div class="autocomplete-col2">
					<div class="autocomplete-col-row autocomplete-col-category catalogcategory" data-autocomplete_contenttype_box="catalogcategory">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_categories'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogcategory"></ul>
					</div>
					<div class="autocomplete-col-row autocomplete-col-brands catalogmanufacturer" data-autocomplete_contenttype_box="catalogmanufacturer">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_manufacturers'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="catalogmanufacturer"></ul>	
					</div>
					<div class="autocomplete-col-row autocomplete-col-publish publish" data-autocomplete_contenttype_box="publish">
						<div class="autocomplete-title"><?php echo Arr::get($cmslabel, 'autocomplete_publish'); ?></div>
						<ul class="ui-autocomplete ui-menu ui-widget ui-widget-content ui-corner-all" data-autocomplete_contenttype="publish"></ul>	
					</div>
				</div>
			</div>
		</div>
		
		<button class="sw-btn" type="submit"><?php echo Arr::get($cmslabel, 'search_button', 'Traži'); ?></button>
	</form>
</div>