<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-search page-search-<?php echo $search_content; ?><?php $this->endblock('page_class'); ?>

<?php $catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog'); ?>
<?php $search_url = Utils::app_absolute_url($info['lang'], 'search'); ?>
<?php $search_content = Arr::get($_GET, 'search_content'); ?>
<?php $search_totals = Widget_Search::totals($info['lang'], $query, $search_content); ?>
<?php $other_results = array_sum($search_totals); ?>

<?php $this->block('before_main'); ?>
	<div class="c-header s-header">
		<div class="pos-r wrapper s-header-wrapper">
			<h1 class="s-h1">
				<span class="s-headline"><?php echo Arr::get($cms_page, 'seo_h1'); ?>:</span>
				<span class="s-keyword"><?php echo $query; ?></span>
			</h1>

			<ul class="s-nav">
				<li><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $query; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
				<li<?php if($search_content == 'publish'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $query; ?>&search_content=publish"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish', (($items AND $search_content == 'publish') ? count($items['publish']) : 0)); ?>)</span></a></li>
				<li<?php if($search_content == 'cms'): ?> class="selected"<?php endif; ?>><a href="<?php echo $search_url; ?>?search_q=<?php echo $query; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', (($items AND $search_content == 'cms') ? count($items['cms']) : 0)); ?>)</span></a></li>
			</ul>
		</div>
	</div>
<?php $this->endblock('before_main'); ?>

<?php $this->block('main_layout'); ?>
	<div class="wrapper">
		<?php if (sizeof($items) > 0 ): ?>
			<?php foreach ($items as $module => $results): ?>
				<?php if ($module == 'publish'): ?>
					<div class="p-items">
						<?php echo View::factory('publish/index_entry', array('items' => $results, 'mode' => 'search')); ?>
					</div>
				<?php else: ?>
					<div class="s-items">
						<?php foreach ($results as $item): ?>
							<article class="s-item">
								<h2 class="s-item-title"><a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a></h2>
								<?php if (isset($item['content']) AND $item['content']): ?><div class="s-item-cnt"><?php echo Text::limit_words(strip_tags($item['content']), 50, '...'); ?></div><?php endif; ?>
							</article>
						<?php endforeach; ?>
					</div>
				<?php endif; ?>
			<?php endforeach; ?>
		<?php else: ?>
			<div class="s-no-results"><?php echo Arr::get($cmslabel, 'nothing_found', 'Nema rezultata za traženi pojam'); ?></div>
		<?php endif; ?>
	</div>
<?php $this->endblock('main_layout'); ?>