<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<title><?php echo $item['seo_title']; ?></title>
	<?php echo Html::media('fancybox,standard', 'css'); ?>
	<?php echo Html::media('modernizr', 'js'); ?>
	<meta name="format-detection" content="telephone=no" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>
<body class="gallery">

<div class="slider" id="slider" data-elem="slider">
	<div class="gallery-caption"><?php echo Arr::get($cmslabel, 'scroll_to_zoom'); ?></div>
	<div class="slides" data-elem="slides" data-options="preloaderUrl:/media/images/loader.gif;loop:false;maxZoom:3"></div>
	
	<div class="gsZoom gsZoomIn" data-elem="zoomIn" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0.5; cursor:default"> </div>
	<div class="gsZoom gsZoomOut" data-elem="zoomOut" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0.5; cursor:default"> </div>

	<div class="gsControl gsPrev midLeft" data-elem="prev" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0; cursor:default"> </div>
	<div class="gsControl gsNext midRight" data-elem="next" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0; cursor:default"> </div>

	<?php /*
	<div class="gsThumbToggle topRight" data-elem="thumbsToggle" data-on="autoAlpha:1" data-off="autoAlpha:0.5"></div>
	<div class="gsThumbsHolder" data-elem="thumbsHolder">
		<div class="gsThumbs blackBgAlpha60" data-elem="thumbs" data-options="initShow:true; space:5; preloaderUrl:/media/images/loader.svg; borderColor:#4d4d4d; borderThickness:1; scaleMode:proportionalInside;" data-show="bottom:0px;" data-hide="bottom:-100%;" ></div>
	</div>
	*/ ?>
	
	<?php $images = Utils::get_files('catalogproduct', $item['id'], 'image', $info['lang']); ?>
	<?php $images_360 = Utils::get_files_360('catalogproduct', $item['id'], $info['lang']); ?>

	<ul data-elem="items">
		<!-- 360 images -->
		<?php if(!empty($images_360)): ?>
			<?php foreach($images_360 as $item_360): ?>
				<li>
					<?php $first_image = (!empty($item_360['main_image'])) ? Utils::file_url($item_360['main_image']) : '/media/images/no-image-870.jpg'; ?>
					<img src="<?php echo $first_image; ?>">
				</li>
			<?php endforeach; ?>
		<?php endif; ?>

		<?php foreach($images as $image): ?>
			<li><img src="<?php echo Utils::file_url($image['file']); ?>" alt="<?php echo $image['title']; ?>"></li>
		<?php endforeach; ?>
	</ul>
</div>

<?php echo Html::media('js_gallery'); ?>
<script>
$(window).load(function() {
	var gallery = TouchNSwipe.getSlider("slider");
	var slideIndex = <?php echo (!empty($_GET['index'])) ? $_GET['index'] : 0; ?>;
	gallery.index(slideIndex);
});
</script>
<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
</body>
</html>