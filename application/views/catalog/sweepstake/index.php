<?php $this->extend('catalog/index'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(($kind) ? $kind['seo_title'] . ((isset($kind['parents']) AND $kind['parents']) ? ' - ' . implode(' - ', array_map(function ($element) {
			return $element['title'];
		}, $kind['parents'])) : '') : Arr::get($cms_page, 'seo_title')); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - ' . Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($pagination->current_page > 1): ?><?php echo sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages); ?><?php endif; ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => isset($cms_page) ? $cms_page : [], 'kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-catalog-sweepstake<?php $this->endblock('page_class'); ?>

<?php $this->block('before_main'); ?>
<div class="c-sweepstake-header">
	<div class="wrapper c-sweepstake-wrapper">
		<div class="c-sweepstake-heading-left">
			<?php if ($items): ?>
				<h1><?php echo Arr::get($cmslabel, 'sweepstake_thankyou'); ?></h1>
			<?php else: ?>
				<h1><?php echo Arr::get($cmslabel, 'sweepstake_no_results', 'Nije pronađen ni jedan proizvod koji odgovara kriterijima.'); ?></h1>
			<?php endif; ?>
		</div>
		<div class="c-sweepstake-heading-right">
			<div class="c-sweepstake-label"><?php echo Arr::get($cmslabel, 'sweepstake_not_found'); ?></div>
		</div>
	</div>
</div>
<?php $this->endblock('before_main'); ?>