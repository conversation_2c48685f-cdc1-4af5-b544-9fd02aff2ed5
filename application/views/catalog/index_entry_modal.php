<?php $product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); ?>
<?php $mode = (isset($mode))? $mode : '';  ?>
<?php $i = 1; ?>
<?php foreach ($items as $i => $item): ?>
	<article class="cp<?php if (!$item['is_available']): ?> cp-not-available<?php endif; ?><?php if (!empty($compare_page)): ?> cp-compare<?php endif; ?>" <?php if ($mode == 'wishlist' AND !empty($item['wishlist_widget']['content'])): ?> data-wishlistitem_details="<?php echo $item['wishlist_widget']['content']; ?>"<?php endif; ?>>
		<?php if(!empty($item['lists_info'])): ?>
			<div class="cp-badges">
				<?php foreach($item['lists_info'] as $list_info): ?>
					<?php if(!empty($list_info['image'])): ?>
						<a class="cp-list-badge cp-list-badge-list cp-special-list-badge" href="<?php echo $list_info['url']; ?>">
							<img loading="lazy" <?php echo Thumb::generate($list_info['image'], array('width' => 60, 'height' => 200, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" />
						</a>
					<?php endif; ?>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>

		<?php if (!empty($compare_page)): ?>
			<div class="cp-compare-header">
				<div class="cp-compare-title"><?php echo Arr::get($cmslabel, 'compare_choose_product', 'Odaberite proizvod za usporedbu'); ?></div>
				<form class="cp-compare-form" action="?special_view=compare" method="GET">
					<input class="cp-compare-input" name="compare_autocomplete-<?php echo $item['id']; ?>" placeholder="<?php echo Arr::get($cmslabel, 'compare_add_title', 'Upišite naziv proizvoda'); ?>" type="text" data-compare_content="<?php echo $item['compare_widget']['content']; ?>">
					<button class="cp-compare-btn btn" type="submit"></button>
				</form>
			</div>
		<?php endif ?>

		<a class="cp-header-cnt" href="<?php echo $item['url']; ?>">			
			<div class="cp-image">
				<?php if(!empty($item['attributes_special'])): ?>
					<?php $pk = 1; ?>
					<?php foreach($item['attributes_special'] as $attr): ?>
						<?php if(in_array($attr['attribute_code'], ['poklon'])): ?>
							<div class="cp-badge cp-badge-gift" title="<?php echo $attr['title']; ?>"></div>
							<?php if($pk == 1) break; ?>
							<?php $pk++; ?>
						<?php endif; ?>
					<?php endforeach; ?>
				<?php endif; ?>

				<?php if ($item['discount_percent'] > 0): ?>
					<?php /*<span class="cp-badge cp-badge-discount">-<?php echo $item['discount_percent']; ?>%</span>*/ ?>
				<?php else: ?>
					<?php $priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : '')); ?>
					<?php if ($priority): ?>
						<span class="cp-badge <?php echo Arr::get($priority, 'code'); ?>"><?php echo Arr::get($priority, 'title'); ?></span>
					<?php endif; ?>
				<?php endif; ?>
				<figure>
					<img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => 265, 'height' => 265, 'default_image' => '/media/images/no-image-265.jpg', 'html_tag' => true)); ?> alt="<?php echo Text::meta($item['main_image_description']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" />
				</figure>
			</div>
		</a>
		
		<div class="clear cp-cnt" data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>|<?php echo !empty($list) ? $list : 'Proizvodi';?>">
			<a href="<?php echo $item['url']; ?>">
				<div class="cp-rating-container">
					<?php if(!empty($item['feedback_rate_widget']['rates_status']) AND $item['feedback_rate_widget']['rates_status'] > 1 AND $item['feedback_rate_widget']['rates_votes'] > 0): ?>
						<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
					<?php endif; ?>
				</div>

				<div class="cp-cnt-header">
					<?php if (!$item['is_available']): ?>
						<span class="cp-unavailable">
							<span class="cp-unavailable-btn"><span><?php echo Arr::get($cmslabel, 'not_available_2'); ?></span></span>
						</span>
					<?php endif; ?>

					<?php if(!empty($item['manufacturer_main_image'])): ?>
						<div class="cp-brand<?php if(!empty($item['manufacturer_code'])): ?> cp-brand-<?php echo $item['manufacturer_code']; ?><?php endif; ?>">
							<img <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 95, 'height' => 30, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="<?php echo $item['manufacturer_title']; ?>" />
						</div>
					<?php endif; ?>
					<h2 class="cp-title" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title']; ?></h2>
					<div class="cp-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></div>

					<span style="display: none;" data-product_id="<?php echo $item['shopping_cart_code'] ?>"><?php echo $item['id'] ?></span>
					<span style="display: none;" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title'] ?></span>
					<span style="display: none;" data-extra_product_title="<?php echo $item['shopping_cart_code']; ?>"></span>
					<span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
					<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'category_title'); ?></span>
					<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title', '')); ?></span>
					<span style="display: none;" data-collected_price_warranty="<?php echo $item['shopping_cart_code']; ?>"></span>
					<span style="display: none;" data-extra_service_price="<?php echo $item['shopping_cart_code']; ?>"></span>

					<?php if(!empty($item['attributes_special'])): ?>
						<ul class="cp-attrs">
							<?php $a = 1; ?>
							<?php foreach($item['attributes_special'] as $attr): ?>
								<?php if(!in_array($attr['attribute_code'], ['posebne_oznake', 'poklon'])): ?>
									<li class="cp-attr"><?php echo $attr['title']; ?></li>
									<?php if($a == 4) break; ?>
									<?php $a++; ?>
								<?php endif; ?>
							<?php endforeach; ?>
						</ul>
					<?php endif; ?>
				</div>

				<?php $payment_installments_array = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array'), ['universal_return' => true]); ?>
				<?php
					$payment_installments_info = $payment_installments_array['info'];
					$payment_installments = $payment_installments_array['items'];
					$installments_max_price = (!empty($payment_installments_info['max_total'])) ? round($payment_installments_info['max_total'], 2) : Arr::get($item, 'basic_price_base');
				?>

				<!-- Modal prices -->
				<?php $payment_installments_modal = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array')); ?>
				<?php $basic_price_base = (!empty($payment_installments_modal)) ? round(max(array_column($payment_installments_modal, 'total')), 2) : $item['basic_price_base']; ?>

				<!-- Modal prices -->
				<div class="cp-modal-price" data-collect_price="<?php echo $item['shopping_cart_code']; ?>">
                    <?php if (Arr::get($item, 'discount_percent_base', 0) > 0 OR $item['price_custom'] < $basic_price_base): ?>
						<div class="modal-price-main">
							<div class="modal-old-price"><?php echo Utils::currency_format($basic_price_base * $currency['exchange'], $currency['display']); ?></div>
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
						</div>
						<div class="modal-price-second">
							<div class="modal-old-price"><?php echo Utils::get_second_pricetag_string($basic_price_base, ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></div>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?>						
						</div>
					<?php else: ?>
						<span><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
						<span><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
					<?php endif; ?>
				</div>

				<?php if($item['price_custom'] > 0): ?>
					<?php if(!empty($payment_installments) OR $item['price_custom'] != $installments_max_price): ?>
						<div class="cp-card-prices">
							<?php if (Arr::get($item, 'discount_percent_base', 0) > 0 OR $item['price_custom'] < $installments_max_price): ?>
								<div class="cp-card-price">
									<span class="cp-card-discount">-<?php echo (int) round(Arr::get($item, 'discount_percent_base', 0), 0); ?>%</span>
									<span><?php echo Arr::get($cmslabel, 'cash_price2'); ?></span>
								</div>
							<?php endif; ?>

							<?php if(count($payment_installments) > 1): ?>
								<?php $p = 1; ?>
								<?php foreach($payment_installments as $installment): ?>
									<?php if($p != count($payment_installments)): ?>
									<?php $installment_info = Arr::get($installment, 'price_diff_by_highest_installment'); ?>
										<div class="cp-card-price">
											<span class="cp-card-discount">-<?php echo (int) round(Arr::get($installment_info, 'percent'), 0); ?>%</span>
											<span><?php echo str_replace(['%MIN%', '%MAX%'], [$installment['min'], $installment['max']], Arr::get($cmslabel, 'installments_price')); ?></span>
										</div>
									<?php endif; ?>
									<?php $p++; ?>
								<?php endforeach; ?>
							<?php endif; ?>
						</div>
					<?php endif; ?>
				<?php endif; ?>
			</a>

			<div class="cp-price-container">
				<?php echo View::factory('catalog/widget/energy_badge', ['item' => $item]); ?>
				<div class="cp-price"> 
					<?php if($item['price_custom'] > 0): ?>
						<?php if(empty($payment_installments) OR $item['price_custom'] == $installments_max_price): ?>
							<div class="cp-price-discount">
								<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
									<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
									<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
								</div>
								<div class="cp-price-note"><?php echo Arr::get($cmslabel, 'our_price'); ?></div>
							</div>
						<?php else: ?>
							<div class="cp-price-discount">
								<?php $card_price = end($payment_installments); ?>
								<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
									<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($card_price['total'] * $currency['exchange'], $currency['display']); ?></span>
									<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($card_price['total'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
								</div>
								<div class="cp-price-note"><?php echo str_replace(['%MIN%', '%MAX%'], [$card_price['min'], $card_price['max']], Arr::get($cmslabel, 'installments_price2')); ?></div>
							</div>
						<?php endif; ?>
					<?php endif; ?>
				</div>
			</div>

			<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
				<span class="cp-extra-price-lowest">
					<?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
					<strong>
						<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
						<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
					</strong>
				</span>
			<?php endif; ?>
			
			<div class="cp-btns">
				<?php if (!empty($item['wishlist_widget'])): ?>
					<div class="cp-wishlist<?php if ($item['wishlist_widget']['active']): ?> active<?php endif; ?>" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>">
						<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '+', '', 2);" class="cp-wishlist-btn cp-wishlist-add wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>" title="<?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?>"><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></a>
						<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', 'remove', '', 2);" class="cp-wishlist-btn cp-wishlist-remove wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>" title="<?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?>"><?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?></a>
						<span class="product-in-wishlist wishlist_message wishlist_message_<?php echo $item['wishlist_widget']['content']; ?> wishlist-message wishlist-message-<?php echo $item['id']; ?>" style="display: none;"></span>
					</div>
				<?php endif; ?>

				<?php if (isset($item['compare_widget'])): ?>
					<?php if (!empty($compare_page)): ?>
						<span class="cp-compare-container">
							<a class="cp-btn-compare compare_set_<?php echo $item['compare_widget']['content']; ?> compare_active" href="javascript:cmscompare.set('<?php echo $item['compare_widget']['content']; ?>', 'remove_reload');"><span><?php echo Arr::get($cmslabel, 'compare_remove', 'Ukloni'); ?></span></a>
						</span>
					<?php else: ?>
						<?php echo View::factory('catalog/widget/set_compare', $item['compare_widget']); ?>
					<?php endif ?>
				<?php endif; ?>

				<?php if($item['is_available'] > 0 AND $item['available_qty'] > 0): ?>
                    <a href="javascript:cmswebshop.shopping_cart.add_warranty('<?php echo $item['shopping_cart_code'];?>', '<?php echo $item['price_custom'];?>', '<?php echo $item['category_id'];?>', '<?php echo $item['manufacturer_id'];?>', '<?php echo $item['id'];?>');" class="btn btn-cp-add-detail cp-btn-addtocart" title="<?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?>" data-gtm-product="<?php echo $item['title']; ?>" data-gtm="add-to-cart"><span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span></a>
				<?php else: ?>
					<a class="btn btn-cp-add-detail cp-btn-details" title="<?php echo Arr::get($cmslabel, 'more_info'); ?>" href="<?php echo $item['url']; ?>"><?php echo Arr::get($cmslabel, 'more_info'); ?></a>
				<?php endif; ?>
			</div>		
		</div>
		<?php if (!empty($compare_page)): ?>
			<div class="c-compare-m-btns">
				<a href="javascript:void(0);" data-compare_mode="diff" class="c-compare-btn c-compare-btn-diff"><?php echo Arr::get($cmslabel, 'compare_difference'); ?><span></span></a>
				<a href="javascript:void(0);" data-compare_mode="all" class="c-compare-btn c-compare-btn-all active"><?php echo Arr::get($cmslabel, 'compare_all_details'); ?><span></span></a>
			</div>			
			<div class="cp-attributes">
				<table class="table-cp-attributes">
					<?php 
					$item_attributes = array_flip(array_keys($attributes));
					foreach ($item['attributes'] AS $attribute) {
						if (isset($item_attributes[$attribute['attribute_code']])) {
							$item_attributes[$attribute['attribute_code']] = [$attribute['attribute_title'], $attribute['title']];
						}
					}
					?>

					<?php $j = 1; ?>
					<?php foreach ($item_attributes AS $attribute_code => $attribute_data): ?>
						<?php if (is_array($attribute_data)): ?>
							<tr class="attr-row active attr-row<?php echo $j; ?>" data-row="<?php echo $j; ?>" data-compare_attribute="<?php echo $attribute_code; ?>">
								<td class="col-attribute-title"><?php echo $attribute_data[0]; ?></td>
								<td class="col-title"><?php echo $attribute_data[1]; ?></td>
							</tr>
						<?php else: ?>
							<tr class="attr-row active attr-row<?php echo $j; ?> attr-row-empty" data-row="<?php echo $j; ?>" data-compare_attribute="<?php echo $attribute_code; ?>">
								<td class="col-attribute-title">&nbsp;</td>
								<td class="col-title">&nbsp;</td>
							</tr>
						<?php endif; ?>
						<?php $j++; ?>
					<?php endforeach; ?>
				</table>
			</div>
		<?php endif ?>		
	</article>

	<?php if (!empty($compare_page) AND $i == 3): ?>
		<?php break; ?>
	<?php endif ?>
	<?php $i++; ?>		
<?php endforeach; ?>