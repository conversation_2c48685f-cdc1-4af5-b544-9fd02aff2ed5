<?php $this->extend('default_quick'); ?>

<?php $this->block('content_layout'); ?>
<div id="quick-wishlist-container">
	<div class="quick-wishlist-wrapper">
		<div class="global-error wishlist-message wishlist_message_<?php echo $shopping_cart_code; ?>" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>

		<?php if (count($wishlists)): ?>
			<div class="clear wishlist-addtolist">
				<?php /*<h1 class="wishlist-widget-title">Dodaj "<?php echo $shopping_cart_title; ?>" na popis poklona za...</h1>*/ ?>
				<?php $wishlists_list = array_map(function($element){return $element['full_name'].' - '.$element['event_after_year'].'. rođendan ('.date('d.m.', $element['event_date']).')';}, $wishlists); ?>
				<?php echo Form::select('wishlist_id', $wishlists_list, $default_wishlist_id); ?>
				<a class="btn btn-add-to-wishlist" href="javascript:cmswishlist.set('<?php echo $shopping_cart_code; ?>', '+', 'select', 4);"><?php echo Arr::get($cmslabel, 'add_to_list'); ?></a>
				<div class="wishlist-add-new-list">
					<a href="javascript:toggleBox('#quick-wishlist-container')" class="btn-quick-new-list"><?php echo Arr::get($cmslabel, 'quick_new_list'); ?></a>
				</div>
			</div>
		<?php endif; ?>
		
		<form method="post" action="" id="wishlist" name="wishlist" class="form-label wishlist-form quick-wishlist-form<?php if (!count($wishlists)): ?> active<?php endif; ?>">
			<?php foreach ($customer_fields as $field): ?>
				<?php $error = Valid::get_error($field, $message); ?>
				<p class="field field-<?php echo $field; ?>">
					<?php $label = (Arr::get($cmslabel, 'wishlist_'.$field)) ? Arr::get($cmslabel, 'wishlist_'.$field) : Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?>
					<?php if($field == 'is_private'): ?>
						<?php echo $item->input($field, 'form'); ?>
						<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo $label; ?></label>
					<?php else: ?>
						<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo $label; ?></label>
						<?php echo $item->input($field, 'form'); ?>						
					<?php endif; ?>
					<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
				</p>
				<?php /*if($field == 'full_name'): ?>
					<a class="btn-toggle-wishlist" href="javascript:toggleBox(['.btn-toggle-wishlist, .wishlist-form-otherfields']);"><?php echo Arr::get($cmslabel, 'wishlist_reminder_fields'); ?></a>
					<div class="wishlist-form-otherfields"></div>
				<?php endif;*/ ?>
			<?php endforeach; ?>
			
			<div class="clear wishlist-form-btns">
				<a class="btn btn-white btn-border btn-wishlist-close" href="javascript:void(0);" onClick="parent.$.fancybox.close();"><?php echo Arr::get($cmslabel, 'cancel'); ?></a>

				<a class="btn btn-wishlist-save" href="javascript:cmswishlist.set('<?php echo $shopping_cart_code; ?>', '+', 'new_form')">
					<?php echo Arr::get($cmslabel, 'save_to_reminder'); ?>
				</a>
			</div>
		</form>
	</div>
</div>
<?php $this->endblock('content_layout'); ?>