<?php $class = (isset($class)) ? ' '.$class : ''; ?>
<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.1', 'special' => 1, 'mode' => 'widget')); ?>
<?php if(!empty($categories)): ?>
	<span class="nc-m-cnt">
		<ul class="<?php if(!empty($class)): ?><?php echo $class; ?><?php else: ?>nav-categories<?php endif; ?><?php if($info['user_device'] == 'm' AND empty($hpcat)): ?> nc-m nc-m-lvl1<?php endif; ?>">
			<li class="nav-home"><a href="<?php echo Utils::homepage($info['lang']); ?>">Početna</a></li>
			<?php foreach($categories as $category): ?>
				<?php $category_code = $category['code']; ?>
				<?php $submenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => $category['code'], 'generate_tree' => true, 'layout_config' => ['item_open' => '<li class="%selected_class% %style_field% %haschlidren_class%">'])); ?>
				<li class="nav-category-<?php echo $category['code']; ?><?php if(!empty($submenu)): ?> has-children<?php endif; ?>">
					<a href="<?php echo $category['url']; ?>" data-category="<?php echo $category_code; ?>" data-menu_item_position="menu-item-position<?php echo $category['position_h']; ?>" data-menu_item_title="<?php echo $category['title']; ?>"><?php echo $category['title']; ?></a>
					<?php if($info['user_device'] != 'm'): ?>
						<?php if(!empty($submenu)): ?>
							<div class="submenu-container">
								<div class="submenu-col submenu-col1">
									<ul class="nav-submenu">
										<?php echo $submenu; ?>
									</ul>
								</div>
								<?php /* if($category['element_menu'] AND $info['user_device'] != 'm'): ?>
									<div class="submenu-col submenu-col2">
										<?php echo $category['element_menu']; ?>
									</div>
								<?php endif; */ ?>
								<?php //$nav_bestseller_list = Widget_Catalog::speciallist($info['lang'], 'bestsellers_'.$category_code, true); ?>
								<?php $nav_bestseller_code = 'bestsellers_'.$category_code ?>
								<?php $nav_bestseller_items = Widget_Catalog::products(array('lang' => $info['lang'], 'sort' => 'list_position', 'list_code' => $nav_bestseller_code, 'only_available' => true, 'limit' => 3)); ?>
								<?php if(!empty($nav_bestseller_items)): ?>
									<div class="submenu-col submenu-col2">
										<div class="nav-bestsellers-cnt">
											<div class="nav-bestsellers-items-title">
												<?php echo Arr::get($cmslabel, 'bestsellers'); ?>
												<a class="bs-show-more" href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
											</div>
											<div class="nav-bestsellers-items">
												<?php echo View::factory('catalog/index_entry_small', array('items' => $nav_bestseller_items)); ?>
											</div>
										</div>
									</div>
								<?php endif; ?>
							</div>
						<?php endif; ?>
					<?php endif; ?>
				</li>
			<?php endforeach; ?>
		</ul>
		<?php if(empty($hpcat)): ?>
			<?php foreach ($categories as $category): ?>
				<?php $category_code = $category['code']; ?>
				<?php $submenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => $category['code'], 'level_range' => 1.1, 'layout_config' => ['item_open' => '<li class="%selected_class% %style_field% %haschlidren_class%">'])); ?>
					<?php if (!empty($submenu)): ?>
						<ul class="nc-m nc-m-lvl2" data-category="<?php echo $category['code']; ?>">
							<?php foreach($submenu as $menuitem):?>
								<?php $subsubmenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => $category['code'], 'level_range' => 2.2, 'start_position' => $menuitem['position_h'], 'layout_config' => ['item_open' => '<li class="%selected_class% %style_field% %haschlidren_class%">'])); ?>
								<li class="nav-category-<?php echo $category['code']; ?><?php if($subsubmenu): ?> has-children<?php endif; ?>">
									<a href="<?php echo $menuitem['url']; ?>" data-subcategory="<?php echo $menuitem['code']; ?>" data-menu_item_position="menu-item-position<?php echo $menuitem['position_h']; ?>" data-menu_item_title="<?php echo $menuitem['title']; ?>"><?php echo $menuitem['title'] ?></a>
								</li>
							<?php endforeach; ?>

							<?php //$nav_bestseller_list = Widget_Catalog::speciallist($info['lang'], 'bestsellers_'.$category_code, true); ?>
							<?php $nav_bestseller_code = 'bestsellers_'.$category_code ?>
							<?php $nav_bestseller_items = Widget_Catalog::products(array('lang' => $info['lang'], 'sort' => 'list_position', 'list_code' => $nav_bestseller_code, 'only_available' => true, 'limit' => 3)); ?>
							<?php if(!empty($nav_bestseller_items)): ?>
								<div class="submenu-col submenu-col2">
									<div class="nav-bestsellers-cnt">
										<div class="nav-bestsellers-items-title">
											<?php echo Arr::get($cmslabel, 'bestsellers'); ?>
											<a class="bs-show-more" href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
										</div>
										<div class="nav-bestsellers-items">
											<?php echo View::factory('catalog/index_entry_small', array('items' => $nav_bestseller_items)); ?>
										</div>
									</div>
								</div>
							<?php endif; ?>
						</ul>
					<?php endif; ?>
			<?php endforeach; ?>

			<?php foreach ($categories as $category): ?>
			<?php $category_code = $category['code']; ?>
				<?php $submenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => $category['code'], 'level_range' => 1.1, 'layout_config' => ['item_open' => '<li class="%selected_class% %style_field% %haschlidren_class%">'])); ?>
				<?php if (!empty($submenu)): ?>
					<?php foreach($submenu as $menuitem):?>
						<?php $subsubmenu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => $category['code'], 'level_range' => 2.2, 'start_position' => $menuitem['position_h'], 'layout_config' => ['item_open' => '<li class="%selected_class% %style_field% %haschlidren_class%">'])); ?>
						<?php if(!empty($subsubmenu)): ?>
							<ul class="nc-m nc-m-lvl3" data-subcategory="<?php echo $menuitem['code']; ?>">
								<?php foreach($subsubmenu as $submenuitem):?>
									<li class="nav-category-<?php echo $menuitem['code']; ?> <?php echo $submenuitem['style'] ?>">
										<a href="<?php echo $submenuitem['url']; ?>" data-subsubcategory="<?php echo $submenuitem['code']; ?>" data-menu_item_position="menu-item-position<?php echo $submenuitem['position_h']; ?>" data-menu_item_title="<?php echo $submenuitem['title']; ?>"><?php echo $submenuitem['title'] ?></a>
									</li>
								<?php endforeach; ?>
							</ul>
						<?php endif; ?>
					<?php endforeach; ?>
				<?php endif; ?>
			<?php endforeach; ?>
		<?php endif; ?>
	</span>
<?php endif; ?>