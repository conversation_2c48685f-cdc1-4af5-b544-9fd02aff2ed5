<?php
$is_loyalty = (!empty($user->loyalty_code));
$installments_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['regular'])).Utils::get_second_pricetag_string((!empty(Arr::get($item, 'installments_calculation')['regular'])), ['currency_code' => $currency['code'], 'display_format' => 'standard']) ? reset($item['installments_calculation']['regular']) : 0 * $currency['exchange'], $currency['display']);
$installment_price = Arr::get($item, 'installments_calculation')['regular'];
$installment_loyalty_price = Arr::get($item, 'installments_calculation')['loyalty'];
if (is_array($installment_price)) {
	$installment_price = reset($installment_price);
}
if (is_array($installment_loyalty_price)) {
	$installment_loyalty_price = reset($installment_loyalty_price);
}
if (!empty($installment_loyalty_price) AND $is_loyalty) {
	$installment_price = $installment_loyalty_price;
}
?>
<?php if(!empty($installment_price) OR (!empty($installments_min_price) AND (!empty($item['installments_calculation']['regular']) OR !empty($item['installments_calculation']['regular'])))): ?>
	<?php if (!empty($installments_min_price) AND (!empty($item['installments_calculation']['regular']) OR !empty($item['installments_calculation']['regular']))): ?>
		<?php $installments_loop = $item['installments_calculation']['regular']; ?>
		<?php if($installments_loop): ?>
			<a class="btn-installments-calc btn-installments-calc-leanpay" href="javascript:toggleBox(['.btn-installments-calc-leanpay', '.cd-payment-installments-leanpay']);">
				<span><?php echo Arr::get($cmslabel, 'installments_calc', 'Izračun rata'); ?></span>
				<img src="/media/images/leanpay.png?v1" alt="Leanpay" width="65" height="19">
			</a>
			<div class="cd-payment-installments cd-payment-installments-leanpay">
				<a class="cd-payment-installments-close" href="javascript:toggleBox(['.btn-installments-calc-leanpay', '.cd-payment-installments-leanpay']);"></a>
				<div class="cd-payment-title"><?php echo Arr::get($cmslabel, 'leanpay_title'); ?> <img src="/media/images/leanpay.png?v1" width="65" height="19" alt=""></div>
				<ul>
					<?php foreach ($installments_loop as $rate => $rate_price): ?>
						<li><?php echo str_replace(['%RATE%', '%RATE_PRICE%'],[$rate, Utils::currency_format($rate_price * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($rate_price, ['currency_code' => $currency['code'], 'display_format' => 'standard'])], Arr::get($cmslabel, 'installment_single_rate')); ?></li>
					<?php endforeach; ?>
				</ul>
				<div class="leanpay-desc"><?php echo Arr::get($cmslabel, 'leanpay'); ?></div>	
			</div>
		<?php endif; ?>
	<?php endif; ?>
<?php endif; ?>