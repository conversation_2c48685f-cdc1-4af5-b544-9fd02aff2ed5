<style>
    .fancybox-overlay {
        z-index: 1001;
    }
</style>
<?php if (!empty($item)): ?>
	<?php $payment_installments_array = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array'), ['universal_return' => true]); ?>
	<?php
		$payment_installments_info = $payment_installments_array['info'];
		$payment_installments = $payment_installments_array['items'];
		$installments_max_price = (!empty($payment_installments_info['max_total'])) ? round($payment_installments_info['max_total'], 2) : Arr::get($item, 'basic_price_base');
	?>

	<!-- Modal prices -->
	<?php $payment_installments_modal = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array')); ?>
	<?php $basic_price_base = (!empty($payment_installments_modal)) ? round(max(array_column($payment_installments_modal, 'total')), 2) : $item['basic_price_base']; ?>
	<?php $related_items = Widget_Catalog::products(array('lang' => $lang, 'related_code' => 'related', 'related_item_id' => $item['id'], 'limit' => 3, 'always_to_limit_strict_rules' => true)); ?>
	<div class="warranty-modal" id="warranty">
		<?php if(!empty($items)): ?>
			<div class="warranty-modal-intro">
				<div class="warranty-intro-cnt">
					<div class="desc">
						<h4 class="message" data-add_to_cart_msg="<?php echo Arr::get($item, 'shopping_cart_code', ''); ?>"></h4>
						<span class="wp-message product_message product_message_<?php echo Arr::get($item, 'shopping_cart_code', ''); ?>" style="display: none"></span>
						<div class="modal-title"><?php echo Arr::get($cmslabel, 'need_extra_warranty', 'Trebate li dodatno jamstvo za proizvod?'); ?></div>
					</div>
					<?php if (!empty($items)): ?>
						<div class="modal-buttons">
							<?php /* TODO ostaviti integraciji
								<div class="cd-services">
									<div class="cd-services-title"><?php echo Arr::get($cmslabel, 'additional_services'); ?></div>
									<div class="cd-services-items">
										<?php  foreach ($warranty_products as $warranty_product): ?>
											<p>
												<input type="radio" name="select-product" id="service-<?php echo $warranty_product['id']; ?>" value="<?php echo $warranty_product['shopping_cart_code']; ?>" data-title="<?php echo Text::meta($warranty_product['title']); ?>" <?php if (!$warranty_product['is_available']): ?> disabled<?php endif; ?> data-service_product="<?php echo $item['shopping_cart_code']; ?>">
												<label for="service-<?php echo $warranty_product['id']; ?>"><?php echo $warranty_product['title']; ?>
													<?php if (!$warranty_product['is_available']): ?> - <strong><?php echo Arr::get($cmslabel, 'unavailable', 'nije dostupno'); ?></strong><?php endif; ?>
													- <span class="cd-services-price">
														<?php echo Utils::currency_format($warranty_product['price_custom'] * $currency['exchange'], $currency['display']); ?>
														<?php echo Utils::get_second_pricetag_string($warranty_product['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
													</span>
												</label>
											</p>
										<?php endforeach; ?>
										<p><input type="radio" name="select-product" checked id="service-none"><label for="service-none"><?php echo Arr::get($cmslabel, 'additional_services_reject'); ?></label></p>
									</div>
								</div>
							<?php */ ?>

							<?php foreach ($items as $item_id => $item_data) : ?>
								<?php if(!empty($item_data)): ?>
									<p>
										<input type="radio" name="select-product"
											id="modal-service-<?php echo $item_id; ?>"
											value="<?php echo $item_data['shopping_cart_code']; ?>"
											data-product_title="<?php echo Text::meta($item_data['title']); ?>"
											data-extra_service_price="<?php echo Text::meta($item_data['price']); ?>"
											data-title="<?php echo Text::meta($item_data['title']); ?>"
											data-title_separate="1"
											data-service_product="<?php echo $item['shopping_cart_code']; ?>">
										<label for="modal-service-<?php echo $item_id; ?>"><?php echo $item_data['title']; ?>
											<span class="cd-services-price">
												<?php echo Utils::currency_format($item_data['price'] * $currency['exchange'], $currency['display']); ?>
											</span>
										</label>
									</p>
								<?php endif; ?>
							<?php endforeach; ?>
							<p><input type="radio" name="select-product" id="modal-service-none" checked value=""><label for="modal-service-none"><?php echo Arr::get($cmslabel, 'additional_services_reject'); ?></label></p>
						</div>
					<?php endif; ?>
				</div>
				<div class="modal-image">
					<img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => 124, 'height' => 124, 'default_image' => '/media/images/no-image-124.jpg', 'html_tag' => true)); ?> alt="" />
				</div>
			</div>
		<?php elseif($related_items): ?>
			<div class="warranty-modal-intro">
				<div class="warranty-intro-cnt">
					<div class="desc">
						<h4 class="message" data-add_to_cart_msg="<?php echo Arr::get($item, 'shopping_cart_code', ''); ?>"></h4>
						<span class="wp-message product_message product_message_<?php echo Arr::get($item, 'shopping_cart_code', ''); ?>" style="display: none"></span>
						<div class="modal-item-title"><?php echo $item['title'] ?></div>

						<div class="modal-item-price"> 
							<?php if($item['price_custom'] > 0): ?>
								<?php if(empty($payment_installments) OR $item['price_custom'] == $installments_max_price): ?>
									<div class="cp-price-discount">
										<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
											<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
										</div>
									</div>
								<?php else: ?>
									<div class="cp-price-discount">
										<?php $card_price = end($payment_installments); ?>
										<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
											<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($card_price['total'] * $currency['exchange'], $currency['display']); ?></span>
										</div>
									</div>
								<?php endif; ?>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<div class="modal-image">
					<img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => 124, 'height' => 124, 'default_image' => '/media/images/no-image-124.jpg', 'html_tag' => true)); ?> alt="" />
				</div>
			</div>
		<?php else: ?>
			<div class="warranty-modal-intro warranty-modal-intro-basic">
				<div class="warranty-intro-cnt">
					<h4 class="message" data-add_to_cart_msg="<?php echo Arr::get($item, 'shopping_cart_code', ''); ?>"></h4>
					<div class="modal-image">
						<img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => 200, 'height' => 200, 'default_image' => '/media/images/no-image-124.jpg', 'html_tag' => true)); ?> alt="" />
					</div>
					<span class="wp-message product_message product_message_<?php echo Arr::get($item, 'shopping_cart_code', ''); ?>" style="display: none"></span>
					<div class="modal-item-title"><?php echo $item['title'] ?></div>
					<div class="modal-item-price"> 
						<?php if($item['price_custom'] > 0): ?>
							<?php if(empty($payment_installments) OR $item['price_custom'] == $installments_max_price): ?>
								<div class="cp-price-discount">
									<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
									</div>
								</div>
							<?php else: ?>
								<div class="cp-price-discount">
									<?php $card_price = end($payment_installments); ?>
									<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($card_price['total'] * $currency['exchange'], $currency['display']); ?></span>
									</div>
								</div>
							<?php endif; ?>
						<?php endif; ?>
					</div>
				</div>
			</div>
		<?php endif; ?>

		<?php if ($related_items): ?>
			<div class="cd-row">
				<!-- Related products -->
				<?php $mode = 'warranty'; ?>
				<div class="cd-related-products">
					<div class="cd-related-title"><?php echo Arr::get($cmslabel, 'warranty_related_products_title', 'Drugi kupci su također naručili...'); ?></div>
					<div class="cd-related-slider">
						<?php echo View::factory('catalog/index_entry', ['items' => $related_items, 'mode' => $mode, 'list' => strip_tags(Arr::get($cmslabel, 'product_related_products', 'Povezani proizvodi')), 'from_modal' => true]); ?>
					</div>
				</div>
			</div>
		<?php endif; ?>

		<?php /*
			<div style="float: left;width:50%">
				<a class="btn btn-white modal-view-cart"
				href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'input', 3)">
					<span><?php echo strtoupper(Arr::get($cmslabel, 'no_extra_warranty', 'Ne trebam')); ?></span>
				</a>
			</div>
		*/ ?>
		<div class="modal-continue<?php if(empty($items) AND empty($related_items)): ?> modal-continue-basic<?php endif; ?>">
			<a href="javascript:void(0);" class="continue-shopping" title="Close" data-close_modal><?php echo Arr::get($cmslabel, 'continue_shopping', 'Nastavi kupovati');?></a>
			<a data-shoppingcart class="btn btn-green modal-view-cart"
			href="<?php echo Utils::app_absolute_url($lang, 'webshop', 'index');?>">
				<span><?php echo strtoupper(Arr::get($cmslabel, 'view_shopping_cart', 'Pregled košarice')); ?></span>
			</a>
		</div>
	</div>
<?php endif; ?>
<style>
	.modal-continue{display: flex; margin-top: 15px; overflow-x: hidden;}
	.modal-continue-basic{flex-flow: column-reverse; row-gap: 15px;}
	/*.continue-shopping{flex-shrink: 0; padding: 0 60px; display: flex; align-items: center; justify-content: center; font-size: 14px; line-height: 18px; text-decoration: underline; text-decoration-color: #FCD002 !important; text-underline-offset: 4px;}*/
</style>
<script>
	if ($('a[data-close_modal]').size()) {
		$('a[data-close_modal]').on('click', function (e) {
			e.preventDefault();
			let selected_warranty = $('[name=select-product][type=radio]:checked').val();
			if (typeof selected_warranty != 'undefined' && selected_warranty) {
				cmswebshop.shopping_cart.add(selected_warranty, '_tracking:index', 'simple_loader', 'input', 1);
			}
			if ($("#warranty").size() && $.fancybox.isActive) {
				$.fancybox.close();
			}
		});
	}

	if ($('a[data-shoppingcart]').size()) {
		$('a[data-shoppingcart]').on('click', function (e) {
			e.preventDefault();
			let href = $(this).attr('href') || '';
			let selected_warranty = $('[name=select-product][type=radio]:checked').val();
			if (typeof selected_warranty != 'undefined' && selected_warranty) {
				cmswebshop.shopping_cart.add(selected_warranty, '_tracking:index', 'simple_loader', 'input', 4);
			} else {
				if ($("#warranty").size() && $.fancybox.isActive) {
					$.fancybox.close();
				}
				setTimeout(function(){
					window.location.href = href;
				}, 1000);

			}
			return false;
		});
	}
</script>