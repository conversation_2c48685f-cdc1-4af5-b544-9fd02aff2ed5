<?php $class = (isset($class)) ? $class : 'cp-btn-compare'; ?>
<?php if($class != 'cd-btn-compare'): ?><span class="cp-compare-container"><?php endif; ?>
	<a class="<?php echo $class; ?> compare_set_<?php echo $content; ?><?php if ($active): ?> compare_active<?php endif; ?>" href="javascript:cmscompare.set('<?php echo $content; ?>', '<?php if ($active): ?>remove<?php else: ?>+<?php endif; ?>');" data-compare_operation="+|remove">
		<span>
			<small class="l1"><?php echo Arr::get($cmslabel, 'add_compare_product', 'Usporedi'); ?></small>
			<small class="l2"><?php echo Arr::get($cmslabel, 'remove_compare_product', 'Ukloni iz usporedilice'); ?></small>
		</span>
	</a>
	<span class="cp-compare-info compare_message compare_message_<?php echo $content; ?>" style="display:none;"></span>
<?php if($class != 'cd-btn-compare'): ?></span><?php endif; ?>