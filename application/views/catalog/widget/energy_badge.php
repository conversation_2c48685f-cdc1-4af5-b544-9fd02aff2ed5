<?php $class = (!empty($class)) ? ' '.$class : ''; ?>
<?php $mode = (!empty($mode)) ? $mode : ''; ?>
<?php $index = (!empty($index)) ? $index : ''; ?>
<?php if(!empty($item['attributes_special'])): ?>
	<?php foreach($item['attributes_special'] as $attr): ?>
		<?php if($attr['attribute_code'] == 'energetski-razred'): ?>
			<div class="cp-attr-energy<?php echo $class; ?>">
				<?php if($mode != 'detail'): ?><a href="<?php echo $item['url']; ?>?mode=energy"><?php endif; ?>
					<img <?php if($mode == 'detail'): ?>class="btn-attr-energy" data-slide-index="<?php echo $index; ?>" style="cursor: pointer;"<?php endif; ?> loading="lazy" src="<?php if($attr['image']): ?><?php echo Utils::file_url($attr['image']); ?><?php else: ?>/media/images/no-image-50.jpg<?php endif; ?>" alt="" width="75" height="40">
				<?php if($mode != 'detail'): ?></a><?php endif; ?>
				<?php $documents = Utils::get_files('catalogproduct', $item['id'], '-image', $info['lang']); ?>
				<?php $energy_doc = ''; ?>
				<?php if($documents): ?>
					<?php 
						foreach($documents as $energy_document) {
							if($energy_document['title'] == 'Informacijski list') {
								$energy_doc = $energy_document;
							}
						}
					?>
				<?php endif; ?>
				<?php if($energy_doc): ?>
					<a class="cp-attr-energy-link" target="_blank" href="<?php echo $energy_doc['url']; ?>">
						<?php echo $energy_doc['title']; ?>
					</a>
				<?php endif; ?>
			</div>
		<?php endif; ?>
	<?php endforeach; ?>
<?php endif; ?>