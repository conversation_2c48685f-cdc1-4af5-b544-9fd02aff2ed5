<!-- Catalog toolbar -->
<div class="clear toolbar<?php if($q): ?> toolbar-search<?php endif; ?>">
	<div class="toolbar-col toolbar-col1">
		<!-- Total items counter -->
		<?php if ($items_total > 1): ?>
			<div class="c-counter">
				<?php echo $items_total ?> proizvod/a
			</div>
		<?php endif; ?>
	</div>

	<div class="toolbar-col toolbar-col2">
		<?php if($items_total > 1 OR $items_total <= 1 AND $active_filters): ?>
			<a class="btn-toggle-filter<?php if($items_total <= 1): ?> wide<?php endif; ?>" href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'filter'); ?></a>
		<?php endif; ?>

		<?php if($items_total > 0 OR !empty($_GET['discount_base']) OR !empty($_GET['with_qty'])): ?>
			<?php if($items_total > 0 OR !empty($_GET['with_qty'])): ?>
				<?php $with_qty_base_url = Url::query($_GET, FALSE, 'page,searchcode,searchid,with_qty'); ?>
				<?php $with_qty_base_url .= ($with_qty_base_url)  ? '&' : '?'; ?>
				<?php if ((int) Arr::get($_GET, 'with_qty') == 1): ?>
					<a href="<?php echo $with_qty_base_url; ?>searchcode=basic&searchid=1" class="toolbar-filter toolbar-filter-qty active"><?php echo Arr::get($cmslabel, 'with_qty'); ?></a>
				<?php else: ?>
					<a href="<?php echo $with_qty_base_url; ?>searchcode=basic&searchid=1&with_qty=1" class="toolbar-filter toolbar-filter-qty"><?php echo Arr::get($cmslabel, 'with_qty'); ?></a>
				<?php endif; ?>
			<?php endif; ?>
			<?php $discount_base_url = Url::query($_GET, FALSE, 'page,searchcode,searchid,discount_base'); ?>
			<?php $discount_base_url .= ($discount_base_url)  ? '&' : '?'; ?>
			<?php if ((int) Arr::get($_GET, 'discount_base') == 1): ?>
				<a class="toolbar-filter toolbar-filter-discount active" href="<?php echo $discount_base_url; ?>searchcode=basic&searchid=1"><?php echo Arr::get($cmslabel, 'discounts'); ?></a>
			<?php else: ?>
				<a class="toolbar-filter toolbar-filter-discount" href="<?php echo $discount_base_url; ?>searchcode=basic&searchid=1&discount_base=1"><?php echo Arr::get($cmslabel, 'discounts'); ?></a>
			<?php endif; ?>
		<?php endif; ?>

		<?php if($items_total > 0): ?>
			<!-- Product layout options -->
			<div class="c-layout">
				<div class="c-layout-label"><?php echo Arr::get($cmslabel, 'layout'); ?></div>
				<?php $items_layout_base_url = Url::query($_GET, FALSE, 'items_layout'); ?>
				<?php $items_layout_base_url .= ($items_layout_base_url)  ? '&' : '?'; ?>
				<a class="c-layout-grid<?php if (!$items_layout_sufix): ?> active<?php endif; ?>" href="<?php echo $items_layout_base_url; ?>items_layout=default">Mreža</a>
				<a class="c-layout-list<?php if ($items_layout_sufix == '_list'): ?> active<?php endif; ?>" href="<?php echo $items_layout_base_url; ?>items_layout=list">Lista</a>
			</div>
		<?php endif; ?>
		
		<!-- Sort options -->
		<?php if ($items_total > 1): ?>
			<div class="sort c-sort">
				<?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort'); ?>
				<?php $sort_base_url .= ($sort_base_url)  ? '&' : '?'; ?>
				<?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
				<select onchange="window.location.href=this.options[this.selectedIndex].value">
					<option value="<?php echo $sort_base_url; ?>sort=new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent', 'Najnovije'); ?></option> 
					<option value="<?php echo $sort_base_url; ?>sort=old"<?php if ($selected_sort == 'old'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_older', 'Najstarije'); ?></option> 
					<option value="<?php echo $sort_base_url; ?>sort=expensive"<?php if ($selected_sort == 'expensive'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_expensive', 'Najskuplje'); ?></option>
					<option value="<?php echo $sort_base_url; ?>sort=cheaper"<?php if ($selected_sort == 'cheaper'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_cheaper', 'Najjeftinije'); ?></option>
					<option value="<?php echo $sort_base_url; ?>sort=az"<?php if ($selected_sort == 'az'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_az', 'A-Z'); ?></option>
					<option value="<?php echo $sort_base_url; ?>sort=za"<?php if ($selected_sort == 'za'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_za', 'Z-A'); ?></option>
				</select>
			</div>
		<?php endif; ?>
	</div>
</div>