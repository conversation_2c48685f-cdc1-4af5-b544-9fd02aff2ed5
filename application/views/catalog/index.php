<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title'])) ? $kind['seo_title'] : Arr::get($cms_page, 'seo_title'))); ?><?php $this->endblock('title'); ?>
<?php //$breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $kind['breadcrumbs']); ?>
<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, Arr::get($kind, 'breadcrumbs')); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind, 'extra_kind' => (!empty($extra_kind) ? $extra_kind : []), 'pagination' => $pagination, 'breadcrumb' => $breadcrumb]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?><?php if(!empty($kind)): ?> category-level<?php echo $kind['level']; ?><?php endif; ?><?php if($kind_content == 'manufacturer'): ?> page-brand<?php endif; ?><?php if($q): ?> page-search<?php endif; ?><?php $this->endblock('page_class'); ?>

<?php $search_fields = Widget_Catalog::search_filters(array('filters' => $filters, 'hide_total_0' => TRUE, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1))); ?>
<?php $active_filters = (isset($search_fields['_basic']['selected']) AND $search_fields['_basic']['selected']) ? $search_fields['_basic']['selected'] : array(); ?>

<?php if(isset($_GET['special_view']) AND $_GET['special_view'] == 'wishlist'): ?>
	<?php $this->block('main_layout'); ?>
		<div class="wrapper">		
			<div class="wishlist-header">
				<h1 class="wishlists-title"><?php echo Arr::get($cmslabel, 'wishlists'); ?></h1>
			</div>
			<div class="c-empty wishlist-empty"><?php echo Arr::get($cmslabel, 'wishlist_no_products_title'); ?></div>			
		</div>
	<?php $this->endblock('main_layout'); ?>
<?php else: ?>
	<?php $this->block('before_main'); ?>
		<div class="wrapper c-header<?php if($q): ?> s-header<?php endif; ?>">
			<!-- If is search page -->
			<?php if($q): ?>
				<div class="pos-r wrapper s-header-wrapper">
					<h1 class="s-h1">
						<span class="s-headline"><?php echo Arr::get($cms_page, 'seo_h1'); ?>:</span>
						<span class="s-keyword"><?php echo $q; ?></span>
					</h1>

					<?php $catalog_search_url = Utils::app_absolute_url($info['lang'], 'catalog'); ?>
					<?php $search_url = Utils::app_absolute_url($info['lang'], 'search'); ?>
					<?php $search_totals = Widget_Search::totals($info['lang'], $q); ?>
					<?php 
					if ((int) Arr::get($search_totals, 'catalog', 0) === 0) {
						if ((int) Arr::get($search_totals, 'publish', 0) > 0) {
							Request::current()->redirect($search_url . '?search_q=' . $q . '&search_content=publish');
						} elseif ((int) Arr::get($search_totals, 'cms', 0) > 0) {
							Request::current()->redirect($search_url . '?search_q=' . $q . '&search_content=cms');
						}
					}
					?>
					<ul class="s-nav">
						<li class="selected"><a href="<?php echo $catalog_search_url; ?>?search_q=<?php echo $q; ?>"><?php echo Arr::get($cmslabel, "search_catalog"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'catalog', 0); ?>)</span></a></li>
						<li><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=publish"><?php echo Arr::get($cmslabel, "search_publish"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'publish', 0); ?>)</span></a></li>
						<li><a href="<?php echo $search_url; ?>?search_q=<?php echo $q; ?>&search_content=cms"><?php echo Arr::get($cmslabel, "search_cms"); ?> <span class="s-counter">(<?php echo (int) Arr::get($search_totals, 'cms', 0); ?>)</span></a></li>
					</ul>
				</div>
			<?php else: ?>
				<div class="bc">
					<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, ($kind ? $kind['breadcrumbs'] : [])); ?>
					<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
				</div>

				<?php if(!empty($kind_content) AND $kind_content == 'manufacturer' AND !empty($kind['main_image'])): ?>
					<div class="cm-logo">
						<img <?php echo Thumb::generate($kind['main_image'], array('width' => 350, 'height' => 60, 'default_image' => '/media/images/no-image-100.jpg', 'html_tag' => true)); ?> alt="<?php echo $kind['seo_h1']; ?>" />
					</div>
				<?php else: ?>
					<h1 class="c-title"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($q): ?>: <?php echo $q; ?><?php endif; ?></h1>
				<?php endif; ?>

				<!-- Category content -->
				<?php if (($kind AND $kind['content']) OR Arr::get($cms_page, 'content')): ?>
					<div class="c-desc">
						<?php echo ($kind) ? $kind['content'] : Arr::get($cms_page, 'content'); ?>
					</div>
				<?php endif; ?>
			<?php endif; ?>		
		</div>

		<?php if(!empty($kind_content) AND $kind_content != 'list' AND $kind_content != 'manufacturer'): ?>
			<?php if(!empty($kind)): ?>
				<div class="wrapper wrapper-promo">
					<?php echo View::factory('cms/widget/promo', ['category_id' => $kind['id'], 'class' => 'c-promo']); ?>
				</div>
			<?php else: ?>
				<?php if(!empty($cms_page['id'])): ?>
					<div class="wrapper wrapper-promo">
						<?php echo View::factory('cms/widget/promo', ['page_id' => $cms_page['id'], 'class' => 'c-promo']); ?>
					</div>
				<?php endif; ?>
			<?php endif; ?>
		<?php endif; ?>
	<?php $this->endblock('before_main'); ?>

	<?php $this->block('main_layout'); ?>
		<div class="wrapper wrapper-toolbar">
			<?php echo View::factory('catalog/widget/toolbar', ['q' => $q, 'items_total' => $items_total, 'items_layout_sufix' => $items_layout_sufix, 'active_filters' => $active_filters]); ?>
		</div>
		
		<div class="display-f wrapper c-wrapper">
			<aside class="sidebar c-sidebar">
				<div class="sidebar-advisor c-sidebar-advisor">
					<?php echo Arr::get($cmslabel, 'advisor'); ?>
				</div>

				<!-- Selected filters -->
				<?php if ($active_filters): ?>
					<div class="fz0 cf-item cf-active<?php if($q): ?> cf-active-search<?php endif; ?>">
						<div class="cf-title cf-active-title"><?php echo Arr::get($cmslabel, 'selected_filters'); ?></div>
						<?php foreach ($active_filters AS $active_filter_code => $active_filter): ?>
							<a class="cf-active-item" href="<?php echo $active_filter['remove_url']; ?>">
								<?php echo $active_filter['title']; ?>
							</a>
						<?php endforeach; ?>
						<a href="<?php if($q): ?>?search_q=<?php echo $q ?><?php else: ?>?<?php endif; ?>" class="btn-cf-active-clear"><span><?php echo Arr::get($cmslabel, 'clear_filtering'); ?></span></a>
					</div>
				<?php endif; ?>

				<!-- Filter -->
				<?php if ($search_fields): ?>
					<div class="cf">
						<div class="cf-sidebar-title">
							<?php echo Arr::get($cmslabel, 'filter_title'); ?>
							<a class="btn-close-filter" href="javascript:;">Zatvori</a>
						</div>	
						<form action="" method="get" id="attribute_filters_select">
							<?php echo $search_fields['_basic']['field']; ?>
							<div class="cf-items-wrapper">
								<?php foreach ($search_fields AS $search_filter_field => $search_filter): ?>
									<?php $options = Arr::get($search_filter, 'options_details'); ?>
									<?php $template = Arr::get($search_filter, 'template'); ?>
									<?php if (!$options OR (isset($search_filter['options_total_available']) AND $search_filter['options_total_available'] == 0)) {continue;} ?>
									<div class="cf-item cf-item-<?php echo $search_filter_field; ?><?php if($search_filter['options_show']): ?> active<?php endif; ?><?php if($kind_content == 'manufacturer' OR $q): ?> cf-item-notitle<?php endif; ?>">
										
										<!-- Filter title  -->
										<div class="cf-title cf-title" onClick="toggleBox('.cf-item-<?php echo $search_filter_field; ?>');">
											<?php $filter_title = Arr::get($cmslabel, 'f_filter_'.$search_filter_field); ?>
											<?php if($filter_title): ?>
												<?php echo $filter_title; ?>
											<?php else: ?>
												<?php echo Arr::get($search_filter, 'label', Arr::get($cmslabel, $search_filter_field, $search_filter_field)); ?>
											<?php endif; ?>
											<span class="nav-sidebar-arrow"></span>
										</div>

										<div class="cf-item-wrapper scrollable">
											<?php $options_total = count($options); ?>
											<?php $i = 1; ?>
											<?php foreach ($options AS $option_id => $option): ?>
												<?php if ( ! $option_id) {$options_total--; continue;} ?>
												<div class="cf-row<?php if(!empty($option['level'])): ?> cf-row-level<?php echo $option['level']; ?><?php endif; ?><?php if($option['total_available'] <= 0): ?> cf-row-not-available<?php endif; ?>">					
													<input type="checkbox" id="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>" name="<?php echo $search_filter_field; ?>" value="<?php echo $option_id; ?>" <?php if ($option['selected']): ?> checked<?php endif; ?><?php if (Arr::get($search_filter, 'element_multivalue')): ?> data-element_multivalue="1"<?php endif; ?><?php if ($search_filter_field == 'categories'): ?> data-filter_hierarhy_position="<?php echo $option['position']; ?>"<?php endif; ?>>
													<label for="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>"><?php echo $option['title']; ?> <span class="cf-counter">(<?php echo $option['total_available']; ?>)</span></label>
													<?php if($option['description']): ?>
														<span class="cf-row-detail"></span>
														<div class="cf-tooltip">
															<span class="btn-close-tooltip btn-close-cf-tooltip"></span>
															<?php if($option['image']): ?>
																<img loading="lazy" <?php echo Thumb::generate($option['image'], array('width' => 240, 'height' => 170, 'crop' => true, 'default_image' => '/media/images/no-image-100.jpg', 'html_tag' => true)); ?> alt="<?php echo $option['title']; ?>" />
															<?php endif; ?>
															<div class="cf-tooltip-title"><?php echo $option['title']; ?></div>
															<div class="cf-tooltip-desc"><?php echo $option['description']; ?></div>
															<div class="cf-tooltip-footer"><?php echo Arr::get($cmslabel, 'filter_tooltip_footer'); ?></div>
														</div>
													<?php endif; ?>
												</div>
												<?php $i++; ?>
											<?php endforeach; ?>
										</div>
									</div>
								<?php endforeach; ?>
							</div>
							<input type="hidden" name="search_q" value="<?php echo Arr::get($_GET, 'search_q', ''); ?>" />
							
							<?php if (!empty($search_fields['discount']['field'])): ?>
							<div class="cf-item cf-item-sale">
								<?php echo $search_fields['discount']['field']; ?> <label for="searchfield-discount"><?php echo Arr::get($cmslabel, 'sale_item'); ?></label>
							</div>
							<?php endif; ?>

							<div class="cf-btns<?php if ($active_filters): ?> has-active-filters<?php endif; ?>">
								<?php if($active_filters): ?>
									<a href="<?php if($q): ?>?search_q=<?php echo $q ?><?php else: ?>?<?php endif; ?>" class="btn btn-border btn-cf-m-active-clear"><?php echo Arr::get($cmslabel, 'clear_filtering'); ?></a>
								<?php endif; ?>
								<button class="btn btn-confirm-filters" data-cmsfilter_manual_submit="1" data-cmsfilter_element="1" <?php if (empty($search_data)): ?> data-cmsfilter_empty="1" <?php endif; ?> type="submit"><?php echo Arr::get($cmslabel, 'confirm_filters'); ?></button>
							</div>							
						</form>
					</div>
				<?php endif; ?>
			</aside>

			<div class="main-content">
				<div id="items_catalog_layout">
					<?php echo View::factory('catalog/index_layout', [
						'cms_page' => $cms_page,
						'kind' => $kind,
						'extra_kind' => $extra_kind,
						'q' => $q,
						'items' => $items,
						'items_per_page' => $items_per_page,
						'items_all' => $items_all,
						'items_layout_sufix' => $items_layout_sufix,
						'items_total' => $items_total,
						'pagination' => $pagination,
						'selected_sort' => $selected_sort,
					]); ?>
				</div>
			</div>
		</div>
	<?php $this->endblock('main_layout'); ?>
<?php endif; ?>