<?php $this->extend('auth/default'); ?>

<?php $this->block('title'); ?><?php echo $item['title']; ?> od <?php echo trim($item['user_first_name'].' '.$item['user_last_name']); ?><?php $this->endblock('title'); ?>
<?php $this->block('page_class'); ?> page-wishlist<?php $this->endblock('page_class'); ?>

<?php $this->block('main_layout'); ?>
	<div class="wrapper">
		<div class="wishlist-header">
			<h1 class="wishlists-title"><?php echo Arr::get($cmslabel, 'wishlists'); ?><?php if($item['total_items'] > 0): ?> <span class="wishlist-title-counter wishlist_count"><?php echo $item['total_items']; ?></span><?php endif; ?></h1>
		</div>

		<?php if ($item['total_items'] > 0): ?>
			<div id="view_wishlist">
				<?php if ($item['total_items'] > 0): ?>
					<a class="btn btn-border btn-wishslit-delete" href="<?php echo $wishlist['url_delete']; ?>"><span><?php echo Arr::get($cmslabel, 'wishlist_delete'); ?></span></a>
				<?php endif; ?>

				<div class="c-items wishlist-items">
					<?php echo View::factory('catalog/index_entry', ['items' => $item['items'], 'mode' => 'wishlist']); ?>
				</div>
			</div>

			<div class="c-empty wishlist-empty" id="empty_wishlist" style="display: none;">
				<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products_title'); ?></div>
			</div>
		<?php else: ?>
			<div class="c-empty wishlist-empty">
				<div class="no-wishlist-title"><?php echo Arr::get($cmslabel, 'wishlist_no_products_title'); ?></div>
			</div>
		<?php endif; ?>
	</div>

	<?php echo View::factory('catalog/widget/brands'); ?>
<?php $this->endblock('main_layout'); ?>