<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($item,'seo_title', $item['title']  . ' - ' . Arr::get($item, 'category_title'))); ?><?php $this->endblock('title'); ?>
<?php// $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, Arr::get($item, 'breadcrumbs')); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item, 'schema_org' => true, 'breadcrumb' => $breadcrumb]); ?><?php $this->endblock('seo'); ?>

<?php $comment_status = (!empty($item['feedback_comment_widget'])) ? $item['feedback_comment_widget']['comments_status'] : 0; ?>
<?php $rates_status = (!empty($item['feedback_rate_widget'])) ? $item['feedback_rate_widget']['rates_status'] : 0; ?>
<?php $documents = Utils::get_files('catalogproduct', $item['id'], '-image', $info['lang']); ?>

<?php $this->block('main_layout'); ?>
	<div class="wrapper cd-wrapper">
		<div class="cd-row cd-row1">
			<div class="cd-header-placeholder"></div>
			<div class="cd-col1">
				<?php if ($item['manufacturer_main_image']): ?>
					<div class="cd-brand">
						<a href="<?php echo $item['manufacturer_url']; ?>"><img <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 95, 'height' => 30, 'default_image' => '/media/images/no-image-100.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo Text::meta($item['manufacturer_title']); ?>" /></a>
					</div>
				<?php endif; ?>

				<div class="cd-badges">
					<?php if(!empty($item['lists_info'])): ?>
						<?php foreach($item['lists_info'] as $list_info): ?>
							<?php if(!empty($list_info['image'])): ?>
								<a class="cp-list-badge cd-list-badge" href="<?php echo $list_info['url']; ?>">
									<img loading="lazy" <?php echo Thumb::generate($list_info['image'], array('width' => 60, 'height' => 100, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" />
								</a>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php endif; ?>

					<!-- Priority badge (new products) -->
					<?php $product_priorities = Kohana::config('app.catalog.product_priorities'); ?>
					<?php $priority = Arr::get($product_priorities, $item['priority_2']); ?>
					<?php if ($priority): ?>
						<span class="cp-badge cd-badge <?php echo Arr::get($priority, 'code'); ?>"><?php echo Arr::get($priority, 'title'); ?></span>	
					<?php endif; ?>

					<?php if(!empty($item['attributes_special'])): ?>
						<?php $pk = 1; ?>
						<?php foreach($item['attributes_special'] as $attr): ?>
							<?php if(in_array($attr['attribute_code'], ['poklon'])): ?>
								<div class="cp-badge cd-badge cp-badge-gift cd-badge-gift"><span><?php echo $attr['title']; ?></span></div>
								<?php if($pk == 1) break; ?>
								<?php $pk++; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>

				<div id="product_images" class="cd-images" data-bxslider_var_name="catalog_details_bxlider">
					<?php $images = Utils::get_files('catalogproduct', $item['id'], 'image', $info['lang']); ?>
								<?php $cert = false; ?>
					<?php if ($images OR !empty($item['element_certificate'])): ?>		
						<div id="zoom-container"></div>
						<div class="cd-hero-image">
							<div class="cd-hero-slider<?php if(count($images) > 1): ?> has-slider<?php endif; ?>">
								<?php $i = 0; ?>
								<?php foreach ($images as $file): ?>
									<?php if($file['position'] == 30): ?>
										<a class="cd-hero-slide cd-hero-slide-cert fancybox" href="<?php echo $file['url']; ?>" title="<?php echo Text::meta($file['title']); ?>">
										<?php $cert = true; ?>
									<?php else: ?>
										<a class="cd-hero-slide product-gallery<?php if($i == 0): ?> cd-hero-slide-first<?php endif; ?>" href="<?php echo $item['url']; ?>?mode=gallery&index=<?php echo $i; ?>" title="<?php echo Text::meta($file['title']); ?>">
									<?php endif; ?>
										<span><img <?php echo Thumb::generate($file['file'], array('width' => 650, 'height' => 650, 'default_image' => '/media/images/no-image-650.jpg', 'html_tag' => TRUE)); ?> title="<?php echo Text::meta($file['title']); ?>" alt="<?php echo Text::meta($file['description']); ?>" data-zoom-image="<?php echo $file['url']; ?>" <?php if ($i == 0): ?>data-product_main_image="1"<?php endif; ?>/></span>
									</a>
									<?php $i++; ?>
								<?php endforeach; ?>
								<?php if(!empty($item['element_certificate'])): ?>
									<a class="cd-hero-slide cd-hero-slide-cert fancybox" href="<?php echo Html::image_from_string($item['element_certificate'], 'no-results.jpg', 1); ?>">
										<span><img src="<?php echo Html::image_from_string($item['element_certificate'], 'no-results.jpg', 1); ?>" alt=""></span>
									</a>
								<?php endif; ?>
							</div>
						</div>

						<?php if(count($images) > 1 OR !empty($item['element_certificate'])): ?>
							<div class="fz0 cd-thumbs">
								<?php $t = 0; ?>
								<div class="cd-thumbs-slider<?php if(count($images) > 1): ?> has-slider<?php endif; ?>">
									<?php foreach ($images as $file): ?>
										<a class="cd-thumb<?php if($t == 0): ?> active<?php endif; ?>" data-slide-index="<?php echo $t; ?>" href="javascript:void(0);" data-variationids="<?php echo (isset($file['variations_ids']) ? $file['variations_ids'] : ''); ?>">
											<span><img <?php echo Thumb::generate($file['file'], array('width' => 100, 'height' => 100, 'default_image' => '/media/images/no-image-100.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo Text::meta($file['description']); ?>" /></span>
										</a>
										<?php $t++; ?>
									<?php endforeach; ?>
									<?php if(!empty($item['element_certificate'])): ?>
										<a class="cd-thumb" data-slide-index="<?php echo $t++; ?>" href="javascript:void(0);">
											<span><img src="<?php echo Html::image_from_string($item['element_certificate'], 'no-results.jpg', 1); ?>" alt=""></span>
										</a>
									<?php endif; ?>
								</div>
							</div>
						<?php endif; ?>		
					<?php else: ?>
						<img src="/media/images/no-image-435.jpg" alt="" data-product_main_image="1">
					<?php endif; ?>
				</div>
			</div>
			<div class="cd-col2" data-tracking_gtm_impression="1|<?php echo $item['code']; ?>">
				<div class="cd-header">
					<div class="bc cd-bc">
						<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
						<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
					</div>

					<!-- Product code number -->
					<div class="cd-code" data-variation_box="description" data-variations="1" data-variation_active_id="<?php echo Arr::get($active_variation, 'id'); ?>">
						<div data-variation_item_description="<?php echo $item['shopping_cart_code']; ?>"><span data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span></div>
					</div>	

					<h1 class="cd-title" data-product_title="1"><?php echo $item['seo_h1'] ?></h1>

					<span style="display: none;" class="code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
					<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'category_title'); ?></span>
					<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'manufacturer_title'); ?></span>

					<!-- Product rating -->
					<?php if (isset($item['feedback_rate_widget'])): ?>
						<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
					<?php endif; ?>

					<!-- Product comments info -->
					<?php if($comment_status > 1): ?>
						<span class="cd-comments-info">
							<a class="btn-comments" href="#comments">
								<span class="label"><?php echo Arr::get($cmslabel, 'comments_num'); ?></span>
								(<?php echo Arr::get($item['feedback_comment_widget'], 'comments', 0); ?>)
							</a>
						</span>
					<?php endif; ?>
				</div>
				<?php if($item['price_custom'] > 0): ?>
					<?php $payment_installments = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array')); ?>
					<?php $basic_price_base = (!empty($payment_installments)) ? round(max(array_column($payment_installments, 'total')), 2) : $item['basic_price_base']; ?>

					<!-- Modal prices -->
					<div class="cd-modal-price" data-collect_price="<?php echo $item['shopping_cart_code']; ?>">
						<?php if ($item['discount_percent_base'] > 0 OR $item['price_custom'] < $basic_price_base): ?>
							<div class="modal-price-main">
								<div class="modal-old-price"><?php echo Utils::currency_format($basic_price_base * $currency['exchange'], $currency['display']); ?></div>
								<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							</div>
							<div class="modal-price-second">
								<div class="modal-old-price"><?php echo Utils::get_second_pricetag_string($basic_price_base, ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></div>
								<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?>						
							</div>
						<?php else: ?>
							<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
						<?php endif; ?>
					</div>

					<!-- Product prices -->
					<div class="cd-price-container">
						<?php 
							$has_energy_bagde = false; 
							if(!empty($item['attributes_special'])) {
								foreach($item['attributes_special'] as $attr) {
									if($attr['attribute_code'] == 'energetski-razred') {
										$has_energy_bagde = true;
									}
								}
							}
						?>
						<div class="cd-price-container-row<?php if(!$has_energy_bagde): ?> no-energy-badge<?php endif; ?>">
							<div class="cd-price">
								<!-- Regular price -->
								<?php if ($item['discount_percent_base'] > 0 OR $item['price_custom'] < $basic_price_base): ?>
									<div class="cd-current-price" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($basic_price_base * $currency['exchange'], $currency['display']); ?></span>
										<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($basic_price_base, ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
									</div>
								<?php else: ?>
									<div class="cd-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
										<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
									</div>
								<?php endif; ?>
							</div>

							<?php echo View::factory('catalog/widget/energy_badge', ['item' => $item, 'class' => 'cd-attr-energy', 'mode' => 'detail', 'index' => (isset($t) ? $t : 0)]); ?>
							<?php if (!empty($payment_installments)): ?>
								<div class="cd-payment">
									<a class="btn-installments-calc btn-installments-calc1" href="javascript:toggleBox(['.btn-installments-calc1', '.cd-payment-installments1']);"><span><?php echo Arr::get($cmslabel, 'installments_calc', 'Izračun rata'); ?></span></a>
									<div class="cd-payment-installments cd-payment-installments1">
										<a class="cd-payment-installments-close" href="javascript:toggleBox(['.btn-installments-calc1', '.cd-payment-installments1']);"></a>
										<?php if(!empty(Arr::get($cmslabel, 'monthly_payment_cards'))): ?>
											<div class="cd-payment-installments-cards"><?php echo Arr::get($cmslabel, 'monthly_payment_cards'); ?></div>
										<?php endif; ?>
										<?php $monthly_price = end($payment_installments); ?>
										<?php if (!empty($monthly_price['per_month'])): ?>
											<?php if ($payment_installments): ?>							
												<?php foreach ($payment_installments AS $payment_installment): ?>
													<div class="cd-installment-row">
														<span class="label">Kartice <?php if ($payment_installment['max'] > 1): ?> <?php echo $payment_installment['min'].($payment_installment['min'] < $payment_installment['max'] ? '-'.$payment_installment['max'] : ''); ?> rate:<?php else: ?>jednokratno<?php endif; ?></span>
														<span class="value">
															<?php echo Utils::currency_format($payment_installment['total'] * $currency['exchange'], $currency['display']); ?>
															<?php echo Utils::get_second_pricetag_string($payment_installment['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
															<?php if ($payment_installment['max'] > 1): ?> - već od <?php echo Utils::currency_format($payment_installment['per_month'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($payment_installment['per_month'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>/mj<?php endif; ?>
														</span>
													</div>
												<?php endforeach; ?>
											<?php endif; ?>
										<?php else: ?>
											<?php foreach ($payment_installments AS $cc_type => $cc_payment_installments): ?>
												<div class="cd-installment-card">
													<?php foreach ($cc_payment_installments AS $payment_installment): ?>
														<div class="cd-installment-row">
															<span class="label"><span class="img"></span>
															<?php echo Arr::get($cmslabel, 'cc_' . trim($cc_type), 'Kartice'); ?> <?php if ($payment_installment['max'] > 1): ?> <?php echo $payment_installment['min'].($payment_installment['min'] < $payment_installment['max'] ? '-'.$payment_installment['max'] : ''); ?> rate<?php else: ?>jednokratno<?php endif; ?></span>
															<span class="value">
																<?php echo Utils::currency_format($payment_installment['total'] * $currency['exchange'], $currency['display']); ?>
																<?php echo Utils::get_second_pricetag_string($payment_installment['total'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
																<?php if ($payment_installment['max'] > 1): ?> - već od <?php echo Utils::currency_format($payment_installment['per_month'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($payment_installment['per_month'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>/mj<?php endif; ?>
															</span>
														</div>
													<?php endforeach; ?>
												</div>
											<?php endforeach; ?>
										<?php endif; ?>
									</div>
								</div>

								<div class="cd-payment">
									<?php echo View::factory('catalog/widget/box_price_installments', array('item' => $item)); ?>
								</div>
							<?php endif; ?>
						</div>

						<?php if ($item['discount_percent_base'] > 0 OR $item['price_custom'] < $basic_price_base): ?>
							<!-- Cash price -->
							<div class="cd-lowest-price-cnt">
								<span class="cd-badge-discount">-<?php echo (int) round(Arr::get($item, 'discount_percent_base', 0), 0); ?>%</span>
								<div class="cd-lowest-price-price-group">					
									<div class="cd-price-value cd-lowest-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
										<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
									</div>
									<div class="cd-lowest-price-note">
										<span><?php echo Arr::get($cmslabel, 'cash_price'); ?></span>
										<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
											<span class="cd-extra-price-lowest"><?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
												<strong>
													<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
													<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</strong>
											</span>
										<?php endif; ?>
									</div>

									<?php if(!empty($item['extra_price_lowest_data']['price_data']) AND !empty($item['extra_price_lowest_data']['set_by']) AND $item['extra_price_lowest_data']['set_by'] == 'coupons_used'): ?>
										<div class="cd-lowest-coupon-price">
											<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['extra_price_lowest_data']['price'] * $currency['exchange'], $currency['display']); ?></span>
											<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item['extra_price_lowest_data']['price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
										</div>
										<div class="cd-lowest-coupon-price-desc"><?php echo str_replace(['%DISCOUNT%', '%COUPON%'], [round(($item['price_custom'] - $item['extra_price_lowest_data']['price']) / $item['price_custom'] * 100), $item['extra_price_lowest_data']['price_data']['coupon_code']], Arr::get($cmslabel, 'coupon_discount_price')); ?></div>
									<?php endif; ?>
								</div>
							</div>
						<?php else: ?>
							<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
								<div class="cd-lowest-price-note no-discount">
									<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
										<span class="cd-extra-price-lowest"><?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
											<strong>
												<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</strong>
										</span>
									<?php endif; ?>
								</div>
							<?php endif; ?>

							<?php if(!empty($item['extra_price_lowest_data']['price_data']) AND !empty($item['extra_price_lowest_data']['set_by']) AND $item['extra_price_lowest_data']['set_by'] == 'coupons_used'): ?>
								<div class="cd-lowest-coupon-price-cnt">
									<div class="cd-lowest-coupon-price">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['extra_price_lowest_data']['price'] * $currency['exchange'], $currency['display']); ?></span>
										<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item['extra_price_lowest_data']['price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
									</div>
									<div class="cd-lowest-coupon-price-desc"><?php echo str_replace(['%DISCOUNT%', '%COUPON%'], [round(($item['price_custom'] - $item['extra_price_lowest_data']['price']) / $item['price_custom'] * 100), $item['extra_price_lowest_data']['price_data']['coupon_code']], Arr::get($cmslabel, 'coupon_discount_price')); ?></div>
								</div>
							<?php endif; ?>
						<?php endif; ?>

						<div class="cd-qty-shipping-price">
							<?php if(!empty($item['qty_visible']) AND $item['qty_visible'] == 1 AND $item['available_qty'] > 0): ?>
								<div class="cd-available-qty"><?php echo Arr::get($cmslabel, 'available_qty', 'Na zalihi:'); ?> <span><?php echo round($item['available_qty']); ?> <?php echo Arr::get($cmslabel, 'unit', 'kom'); ?></span></div>
							<?php endif; ?>
							
							<?php if (!empty($item['exclude_delivery'])): ?>
								<div class="cd-shipping-price cd-shipping-price-free">
									<?php echo Arr::get($cmslabel, 'product_free_shipping', 'Besplatna dostava'); ?>
								</div>
							<?php else: ?>
								<div class="cd-shipping-price cd-shipping-price-paid">
									<?php echo str_replace('%PRICE%', Utils::currency_format($item['shipping_price'] * $currency['exchange'], $currency['display']).Utils::get_second_pricetag_string($item['shipping_price'], ['currency_code' => $currency['code'], 'display_format' => 'standard']), Arr::get($cmslabel, 'product_shipping_price')) ?>
								</div>
							<?php endif; ?>
						</div>
					</div>
				
					<?php if ($item['is_available']): ?>
						<?php //$warranty_products = Widget_Catalog::products(['lang' => $info['lang'], 'limit' => 10, 'advanced_filters' => ['id' => $item['id'], 'category_id' => $item['category_id'], 'manufacturer_id' => $item['manufacturer_id'], 'price' => $item['price_custom']], 'warranty_main_item_price' => ['basic_price' => $item['basic_price_custom'], 'price' => $item['price_custom']], 'warranty_product_id' => $item['id'], 'only_available' => TRUE, 'sort' => 'older']); ?>
						<?php $warranty_products = Widget_Catalog::products(['lang' => $info['lang'], 'limit' => 10, 'advanced_filters' => ['id' => $item['id'], 'category_id' => $item['category_id'], 'manufacturer_id' => $item['manufacturer_id'], 'attributes_ids' => $item['attributes_ids'], 'price' => $item['price_custom']], 'warranty_main_item_price' => ['basic_price' => $item['basic_price_custom'], 'price' => $item['price_custom']], 'warranty_product_id' => $item['id'], 'only_available' => FALSE, 'sort' => 'older']); ?>
						<?php if (!empty($warranty_products)): ?>
							<div class="cd-services">
								<div class="cd-services-title"><?php echo Arr::get($cmslabel, 'additional_services'); ?></div>
								<div class="cd-services-items">
									<?php foreach ($warranty_products as $warranty_product): ?>
										<p>
											<input type="radio" name="select-product" id="service-<?php echo $warranty_product['id']; ?>" value="<?php echo $warranty_product['shopping_cart_code']; ?>" data-title="<?php echo Text::meta($warranty_product['title']); ?>" <?php if (!$warranty_product['is_available']): ?> disabled<?php endif; ?> data-service_product="<?php echo $item['shopping_cart_code']; ?>">
											<label for="service-<?php echo $warranty_product['id']; ?>"><?php echo $warranty_product['title']; ?>
												<?php if (!$warranty_product['is_available']): ?> - <strong><?php echo Arr::get($cmslabel, 'unavailable', 'nije dostupno'); ?></strong><?php endif; ?>
												- <span class="cd-services-price">
													<?php echo Utils::currency_format($warranty_product['price_custom'] * $currency['exchange'], $currency['display']); ?>
													<?php echo Utils::get_second_pricetag_string($warranty_product['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</span>
											</label>
										</p>
									<?php endforeach; ?>
									<p><input type="radio" name="select-product" checked id="service-none"><label for="service-none"><?php echo Arr::get($cmslabel, 'additional_services_reject'); ?></label></p>
								</div>
							</div>
						<?php endif; ?>
					<?php endif; ?>
				<?php endif; ?>

				<?php if (!$item['is_available'] AND !$variations): ?>
					<?php echo View::factory('siteforms/widget/inquiry_form', array('subject' => Text::meta(Arr::get($cmslabel, 'inquiry_form_title', 'Upit o proizvodu').' '.$item['title'].', url: '.$info['url']))); ?>
				<?php endif; ?>

				<div class="cd-btns">
					<!-- Wishlist -->
					<?php if (!empty($item['wishlist_widget'])): ?>
						<div class="cp-list-wishlist cd-wishlist<?php if ($item['wishlist_widget']['active']): ?> active<?php endif; ?>" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>">
							<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '+', '', 2);" class="cp-list-wishlist-btn cp-wishlist-add wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></span></a>
							<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '-', '', 2);" class="cp-list-wishlist-btn cp-wishlist-remove wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?></span></a>
							<span class="product-in-wishlist wishlist_message wishlist_message_<?php echo $item['wishlist_widget']['content']; ?> wishlist-message wishlist-message-<?php echo $item['id']; ?>" style="display: none;"><span class="wishlist-message-title"></span>
						</div>
					<?php endif; ?>

					<?php if (isset($item['compare_widget'])): ?>
						<div class="cp-list-compare cd-compare">
							<?php echo View::factory('catalog/widget/set_compare', ['content' => $item['compare_widget']['content'], 'active' => $item['compare_widget']['active'], 'class' => 'cp-list-btn-compare cd-btn-compare']); ?>
						</div>
					<?php endif; ?>

					<!-- Add to cart section (if no variations) -->
					<?php if ($item['is_available'] AND !$variations): ?>
						<div class="add-to-cart-container<?php if((int) $item['available_qty'] == 1): ?> cd-qty-limit<?php endif; ?>">
							<?php /*if(!empty($warranty_products)): ?>
								<a class="btn cp-list-btn-addtocart cd-btn-add" href="javascript:cmswebshop.shopping_cart.add_warranty('<?php echo $item['shopping_cart_code'];?>', '<?php echo $item['price_custom'];?>', '<?php echo $item['category_id'];?>', '<?php echo $item['manufacturer_id'];?>', '<?php echo $item['title'];?>', '<?php echo $item['id'];?>');">
									<span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span>
								</a>
							<?php else: ?>
								<a class="btn cp-list-btn-addtocart cd-btn-add" href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code'];?>', '_tracking_detail', 'simple_loader', 'input', 3);">
									<span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span>
								</a>
							<?php endif;*/ ?>
                            <a class="btn cp-list-btn-addtocart cd-btn-add" href="javascript:cmswebshop.shopping_cart.add_warranty('<?php echo $item['shopping_cart_code'];?>', '<?php echo $item['price_custom'];?>', '<?php echo $item['category_id'];?>', '<?php echo $item['manufacturer_id'];?>', '<?php echo $item['id'];?>');">
                                <span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span>
                            </a>
						</div>
                    <?php endif; ?>
				</div>

				<?php if(!empty($item['attributes_special'])): ?>
					<ul class="cp-list-attrs cd-attrs">
						<?php $a = 1; ?>
						<?php foreach($item['attributes_special'] as $attr): ?>
							<?php if(!in_array($attr['attribute_code'], ['posebne_oznake', 'poklon'])): ?>
								<li class="cp-list-attr cp-list-attr-<?php echo $attr['attribute_code']; ?>">
									<div>
										<div class="cp-list-attr-image">
											<?php if($attr['attribute_code'] == 'energetski-razred'): ?>
												<?php if(!empty($item['element_certificate'])): ?><a class="btn-attr-energy" data-slide-index="<?php echo $t++; ?>" href="javascript:void(0);"><?php endif; ?>
												<img loading="lazy" src="<?php if($attr['image']): ?><?php echo Utils::file_url($attr['image']); ?><?php else: ?>/media/images/no-image-50.jpg<?php endif; ?>" alt="" width="60" height="60">
												<?php if(!empty($item['element_certificate'])): ?></a><?php endif; ?>
											<?php else: ?>
												<img loading="lazy" src="<?php if($attr['attribute_image']): ?><?php echo Utils::file_url($attr['attribute_image']); ?><?php else: ?>/media/images/no-image-50.jpg<?php endif; ?>" alt="" width="50" height="50">
											<?php endif; ?>
										</div>
										<?php if($attr['attribute_code'] == 'energetski-razred'): ?>
											<?php $energy_doc = ''; ?>
											<?php if($documents): ?>
												<?php 
													foreach($documents as $energy_document) {
														if($energy_document['title'] == 'Informacijski list') {
															$energy_doc = $energy_document;
														}
													}
												?>
											<?php endif; ?>

											<?php if($energy_doc): ?>
												<div class="cp-list-attr-title">
													<a style="cursor: pointer;" class="cp-list-attr-link" target="_blank" onclick="window.open('<?php echo $energy_doc['url']; ?>', '_blank')">
														<?php echo $energy_doc['title']; ?>
													</a>
												</div>
											<?php endif; ?>
										<?php else: ?>
											<div class="cp-list-attr-title"><?php echo $attr['attribute_title']; ?></div>
											<div class="cp-list-attr-value"><?php echo $attr['title']; ?></div>
										<?php endif; ?>
									</div>
								</li>
								<?php if($a == 4) break; ?>
								<?php $a++; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>

				<?php if(!empty($item['short_description'])): ?>
					<div class="cd-short-description">
						<?php echo $item['short_description']; ?>
					</div>
				<?php endif; ?>

				<?php $related_color_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related_color', 'related_item_id' => $item['id'], 'limit' => 0)); ?>
				<?php if ($related_color_items): ?>
					<div class="cd-related-container">
						<div class="cd-related-color-title"><?php echo Arr::get($cmslabel, 'related_products', 'Proizvod je dostupan i u ovim bojama'); ?>:</div>
						<div class="cd-related-color-products">
							<?php foreach($related_color_items as $related_color_item): ?>
								<a class="cd-related-color-item" href="<?php echo $related_color_item['url'] ?>">
									<?php if (!empty($related_color_item['main_image_2'])): ?>
										<span><img loading="lazy" <?php echo Thumb::generate($related_color_item['main_image_2'], array('width' => 80, 'height' => 80, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> title="<?php echo Text::meta($related_color_item['title']); ?>" alt="<?php echo Text::meta(($related_color_item['main_image_2_description']) ? $related_color_item['main_image_2_description'] : $related_color_item['title']); ?>" /></span>
									<?php else: ?>
										<span><img loading="lazy" <?php echo Thumb::generate($related_color_item['main_image'], array('width' => 80, 'height' => 80, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> title="<?php echo Text::meta($related_color_item['title']); ?>" alt="<?php echo Text::meta(($related_color_item['main_image_description']) ? $related_color_item['main_image_description'] : $related_color_item['title']); ?>" /></span>
									<?php endif; ?>
								</a>
							<?php endforeach; ?>
						</div>
					</div>
				<?php endif; ?>

				<?php $related_size_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related_size', 'related_item_id' => $item['id'], 'limit' => 0)); ?>
				<?php if ($related_size_items): ?>
					<div class="cd-related-container">
						<div class="cd-related-color-title"><?php echo Arr::get($cmslabel, 'related_size_products', 'Proizvod je dostupan i u ovim dijagonalama'); ?>:</div>
						<div class="cd-related-color-products">						
							<?php foreach($related_size_items as $related_size_item): ?>								
								<a class="cd-related-color-item cd-related-size-item" href="<?php echo $related_size_item['url'] ?>">
									<span>
										<img loading="lazy" <?php echo Thumb::generate($related_size_item['main_image'], array('width' => 80, 'height' => 80, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> title="<?php echo Text::meta($related_size_item['title']); ?>" alt="<?php echo Text::meta(($related_size_item['main_image_description']) ? $related_size_item['main_image_description'] : $related_size_item['title']); ?>" />
										<?php if(!empty($related_size_item['attributes_special'])): ?>										
											<?php foreach($related_size_item['attributes_special'] as $related_size_item_attr): ?>
												<?php if($related_size_item_attr['attribute_code'] == 'energetski-razred'): ?>											
													<?php continue; ?>
												<?php endif; ?>
												<span class="cd-related-size-title"><?php echo $related_size_item_attr['title']; ?></span>
												<?php break; ?>
											<?php endforeach; ?>
										<?php endif; ?>
									</span>
								</a>
							<?php endforeach; ?>
						</div>
					</div>
				<?php endif; ?>
			</div>
		</div>
		<div class="cd-row cd-row2">
			<div class="cd-col3">
				<?php if(!empty($item['content']) OR !empty($documents) OR !empty($item['attributes'] OR !empty($item['element_attributes'])) OR $comment_status > 1): ?>
					<div class="tabs-container" id="c-tabs">
						<ul class="tabs">
							<li class="tab-nav-desc<?php if(empty($item['content']) AND empty($documents) AND empty($item['element_attributes'])): ?> no-desc<?php endif; ?>">
								<a href="#tab1"><?php echo Arr::get($cmslabel, 'product_description'); ?></a>
							</li>
							<?php if(!empty($item['attributes'])): ?>
								<li class="tab-nav-specs"><a href="#tab2"><?php echo Arr::get($cmslabel, 'product_specs'); ?></a></li>
							<?php endif; ?>
							<?php if($comment_status > 1): ?>
								<li class="tab-nav-comments" id="comments">
									<a href="#tab3">
										<?php echo Arr::get($cmslabel, 'comments'); ?>
										<?php if($item['feedback_comment_widget']['comments'] > 0): ?> <span class="yellow tab-counter">(<?php echo $item['feedback_comment_widget']['comments']; ?>)</span><?php endif; ?>
									</a>
								</li>
							<?php endif; ?>
							<?php if (!empty($documents)): ?>
								<li class="tab-nav-download"><a href="#tab4"><?php echo Arr::get($cmslabel, 'product_download'); ?></a></li>
							<?php endif; ?>
						</ul>
						<div class="tabs-content">
							<div class="tab tab-desc" id="tab1">
								<a class="btn-tab-toggle" href="javascript:toggleBox(['#tab1']);"><span class="toggle-icon"></span><?php echo Arr::get($cmslabel, 'product_description'); ?></a>
								<div class="tab-content">
									<!-- Product description -->
									<div class="clear cd-desc">
										<?php echo $item['content']; ?>
										<?php echo $item['element_attributes']; ?>
									</div>

									<div class="loadbeeTabContent">
										<div class="loadbeeTab"
											 data-loadbee-manufacturer="EAN"
											 data-loadbee-product="<?php echo $item['code']; ?>"
											 data-loadbee-language="hr_HR"
											 data-loadbee-css="default"
											 data-loadbee-button="default"
											 data-loadbee-template="default">
										</div>
									</div>									
								</div>
							</div>

							<?php if(!empty($item['attributes'])): ?>
								<div class="tab tab-specs" id="tab2">
									<a class="btn-tab-toggle" href="javascript:toggleBox(['#tab2']);"><span class="toggle-icon"></span><?php echo Arr::get($cmslabel, 'product_specs'); ?></a>
									<div class="tab-content">
										<div class="cd-attributes">
											<?php 
											$attributes2 = [];
											foreach($item['attributes'] as $attr) {
												if(in_array($attr['attribute_code'], ['posebne_oznake', 'poklon'])) {
													continue;
												}
												$attributes2[$attr['category_title']][$attr['attribute_title']][] = $attr['title'];
											}
											?>
											<?php foreach ($attributes2 AS $attribute_category => $attribute_items): ?>
												<?php $first = true; ?>
												<?php foreach ($attribute_items AS $attribute_title => $title): ?>
													<div class="cd-attribute">
														<?php if ($first): ?>
															<div class="cd-attribute-group"><?php echo $attribute_category; ?></div>
															<?php $first = false; ?>
														<?php else: ?> 
															<div class="cd-attribute-group empty"></div>
														<?php endif; ?>
														<div class="cd-attribute-title"><?php echo $attribute_title; ?></div>
														<div class="cd-attribute-value"><?php echo implode(', ', $title); ?></div>
													</div>
												<?php endforeach; ?>
											<?php endforeach; ?>
											<?php if(!empty($item['warranty']) AND (float) $item['warranty'] > 0): ?>
												<div class="cd-attribute">
													<div class="cd-attribute-group empty"></div>
													<div class="cd-attribute-title"><?php echo Arr::get($cmslabel, 'warranty'); ?></div>
													<div class="cd-attribute-value"><?php echo (int) $item['warranty']; ?> mj.</div>
												</div>
											<?php endif; ?>									
										</div>
									</div>
								</div>
							<?php endif; ?>
							
							<?php if($comment_status > 1): ?>
								<div class="tab tab-comments" id="tab3">
									<a class="btn-tab-toggle" href="javascript:toggleBox(['#tab3']);"><span class="toggle-icon"></span><?php echo Arr::get($cmslabel, 'comments'); ?></a>
									<div class="tab-content">
										<!-- Comments -->
										<?php if (isset($item['feedback_comment_widget'])): ?>
											<?php echo View::factory('feedback/comments', [ 'item' => $item['feedback_comment_widget'], 'items' => !empty($item['feedback_comment_widget']['items']) ? $item['feedback_comment_widget']['items'] : array(), 'content' => $item['feedback_comment_widget']['content'], 'status' => $comment_status]); ?>
										<?php endif; ?>
									</div>
								</div>
							<?php endif; ?>
	
							<?php if (!empty($documents)): ?>
								<div class="tab tab-specs" id="tab4">					
									<a class="btn-tab-toggle" href="javascript:toggleBox(['#tab4']);"><span class="toggle-icon"></span><?php echo Arr::get($cmslabel, 'product_download'); ?></a>
									<div class="tab-content">
										<table class="cd-documents">
											<thead>
												<tr>
													<td class="cd-documents-title"><?php echo Arr::get($cmslabel, 'file_description', 'Opis datoteke'); ?></td>
													<td class="cd-documents-format"><?php echo Arr::get($cmslabel, 'file_format', 'Format'); ?></td>
													<td class="cd-documents-dl"><?php echo Arr::get($cmslabel, 'file_download', 'Preuzimanje'); ?></td>
												</tr>
											</thead>
											<tbody>
												<?php foreach ($documents as $file): ?>
													<?php 
													if (Text::starts_with($file['file'], 'http')) {
														$file['url'] = $file['file'];
													}
													?>
													<tr>
														<td class="cd-documents-title"><?php echo Text::meta($file['title']); ?></td>
														<td class="cd-documents-format"><span class="table-row-title"><?php echo Arr::get($cmslabel, 'file_format', 'Format'); ?>: </span><?php echo pathinfo($file['file'], PATHINFO_EXTENSION); ?></td>
														<td class="cd-documents-dl"><a class="btn-documents-dld" href="<?php echo $file['url']; ?>" target="_blank" title="<?php echo Text::meta($file['description']); ?>"><span><?php echo Arr::get($cmslabel, 'download_file', 'Preuzmi datoteku'); ?></span></a></td>			
													</tr>
												<?php endforeach; ?>
											</tbody>
										</table>
									</div>
								</div>
							<?php endif; ?>
						</div>
					</div>
				<?php endif; ?>

				<!-- Share -->
				<?php echo View::factory('cms/widget/share', array('item' => isset($item) ? $item : array(), 'class' => 'cd-share')); ?>		
			</div>	
			<div class="cd-col4">
				<?php $benefit_items = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'benefits', 'limit' => 2)); ?>
				<?php if ($benefit_items): ?>			
					<div class="cd-benefits-wrapper">
						<div class="cd-benefits">
							<?php foreach ($benefit_items as $benefit_item): ?>
								<div class="cd-benefit-item">
									<a class="cd-benefit<?php if(!empty($benefit_item['link2'])): ?> <?php echo $benefit_item['link2']; ?><?php endif; ?>" href="<?php echo $benefit_item['link']; ?>"><?php echo $benefit_item['title']; ?><?php if(!empty($benefit_item['title2'])): ?><span><?php echo $benefit_item['title2']; ?></span><?php endif; ?></a>
								</div>
							<?php endforeach; ?>
						</div>
					</div>
				<?php endif; ?>

				<!-- Related products -->
				<?php $related_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'related', 'related_item_id' => $item['id'], 'limit' => 10, 'always_to_limit_strict_rules' => true)); ?>
				<?php if ($related_items): ?>
					<div class="cd-related-products">
						<div class="cd-related-title"><?php echo Arr::get($cmslabel, 'related_products', 'Povezani proizvodi'); ?></div>
						<div class="cd-related-slider">
							<?php echo View::factory('catalog/index_entry', ['items' => $related_items, 'list' => strip_tags(Arr::get($cmslabel, 'product_related_products', 'Povezani proizvodi'))]); ?>
						</div>
					</div>
				<?php endif; ?>
			</div>
		</div>
	</div>	

	<?php $upsale_items = Widget_Catalog::products(array('lang' => $info['lang'], 'related_code' => 'upsale', 'related_item_id' => $item['id'], 'limit' => 10)); ?>
	<?php if ($upsale_items): ?>
		<div class="cd-upsale">
			<div class="wrapper cd-upsale-wrapper">
				<div class="cd-upsale-title"><?php echo Arr::get($cmslabel, 'upsale_title'); ?></div>
				<div class="slick-carousel cd-upsale-slider cw-slider">
					<?php echo View::factory('catalog/index_entry', array('items' => $upsale_items)); ?>
				</div>
			</div>
		</div>
	<?php endif; ?>

	<?php echo View::factory('catalog/widget/brands'); ?>
<?php $this->endblock('main_layout'); ?>

<?php $this->block('extrabody'); ?>
	<script>
		loadbeeApiKey="5GKgxrTEtEkX2592UAbuHDMeReCFPR7J";
		var lb = 0;
		var loadbeeProductNotFoundCallback = function() {
			lb = 1;

			if($('.tab-nav-desc').hasClass('no-desc')) {
				$('.tab-nav-desc, .tab-desc').remove();
			}
			setTimeout(function() {
				$('.tabs-container').CmsUtilsTabs({
					animationType: 'css'
				});
			},200);
		}
		var loadbeeProductFoundCallback = function() {
			lb = 1;

			$('.tabs-container').CmsUtilsTabs({
				animationType: 'css'
			});
		}

		if(!lb) {
			$('.tabs-container').CmsUtilsTabs({
				animationType: 'css'
			});
		}
	</script>
	<script async src="//button.loadbee.com/js/v2/loadbee.js"></script>	
<?php $this->endblock('extrabody'); ?>	