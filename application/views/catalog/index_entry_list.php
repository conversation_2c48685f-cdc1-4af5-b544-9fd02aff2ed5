<?php $product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); ?>
<?php $mode = (isset($mode))? $mode : '';  ?>
<?php foreach ($items as $i => $item): ?>
	<article class="cp-list<?php if (!$item['is_available']): ?> cp-not-available<?php endif; ?>"<?php if ($mode == 'wishlist' AND !empty($item['wishlist_widget']['content'])): ?> data-wishlistitem_details="<?php echo $item['wishlist_widget']['content']; ?>"<?php endif; ?> data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>">
		<div class="cp-list-col cp-list-col1">
			<?php if(!empty($item['lists_info'])): ?>
				<div class="cp-badges cp-list-badges">
					<?php foreach($item['lists_info'] as $list_info): ?>
						<?php if(!empty($list_info['image'])): ?>
							<a class="cp-special-list-badge" href="<?php echo $list_info['url']; ?>">
								<img loading="lazy" <?php echo Thumb::generate($list_info['image'], array('width' => 60, 'height' => 200, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" />
							</a>
						<?php endif; ?>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>

			<?php if($item['manufacturer_main_image']): ?>
				<div class="cp-list-brand cp-brand-<?php echo $item['manufacturer_code']; ?>" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>">
					<img <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 95, 'height' => 30, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="<?php echo $item['manufacturer_title']; ?>" />
				</div>
			<?php endif; ?>

			<?php if(!empty($item['attributes_special'])): ?>
				<?php $po = 1; ?>
				<?php foreach($item['attributes_special'] as $attr): ?>
					<?php if(in_array($attr['attribute_code'], ['posebne_oznake'])): ?>
						<div class="cp-list-badge-special"><?php echo $attr['title']; ?></div>
						<?php if($po == 1) break; ?>
						<?php $po++; ?>
					<?php endif; ?>
				<?php endforeach; ?>
			<?php endif; ?>

			<?php if(!empty($item['attributes_special'])): ?>
				<?php $pk = 1; ?>
				<?php foreach($item['attributes_special'] as $attr): ?>
					<?php if(in_array($attr['attribute_code'], ['poklon'])): ?>
						<div class="cp-badge cp-badge-gift cp-list-badge-gift" title="<?php echo $attr['title']; ?>"></div>
						<?php if($pk == 1) break; ?>
						<?php $pk++; ?>
					<?php endif; ?>
				<?php endforeach; ?>
			<?php endif; ?>
	
			<?php if ($item['discount_percent'] > 0): ?>
				<?php /*<span class="cp-badge cp-list-badge cp-badge-discount">-<?php echo $item['discount_percent']; ?>%</span>*/ ?>
			<?php else: ?>
				<?php $priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : '')); ?>
				<?php if ($priority): ?>
					<span class="cp-badge cp-list-badge cp-list-badge-list <?php echo Arr::get($priority, 'code'); ?>"><?php echo Arr::get($priority, 'title'); ?></span>
				<?php endif; ?>
			<?php endif; ?>
			
			<figure class="cp-list-image">
				<a href="<?php echo $item['url']; ?>">
					<span><img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => 220, 'height' => 220, 'default_image' => '/media/images/no-image-265.jpg', 'html_tag' => true)); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" /></span>
				</a>
			</figure>
		</div>

		<div class="cp-list-col cp-list-col2">
			<?php if (!$item['is_available']): ?>
				<div>
					<span class="cp-list-unavailable"><span><?php echo Arr::get($cmslabel, 'not_available_2'); ?></span></span>
				</div>
			<?php endif; ?>
			<div class="cp-list-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></div>
			<h2 class="cp-list-title"><a href="<?php echo $item['url']; ?>" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title']; ?></a></h2>

            <span style="display: none;" data-product_id="<?php echo $item['id'] ?>"><?php echo $item['id'] ?></span>
            <span style="display: none;" data-product_title="<?php echo $item['title'] ?>"><?php echo $item['title'] ?></span>
            <span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
            <span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'category_title', ''); ?></span>
            <span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title', '')); ?></span>

			<div class="cp-list-rating">
				<?php if(!empty($item['feedback_rate_widget']['rates_status']) AND $item['feedback_rate_widget']['rates_status'] > 1 AND $item['feedback_rate_widget']['rates_votes'] > 0): ?>
					<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
				<?php endif; ?>

				<?php if(!empty($item['feedback_comment_widget']) AND $item['feedback_comment_widget']['comments_status'] > 1 AND Arr::get($item['feedback_comment_widget'], 'comments', 0) > 0): ?>
					<a class="cp-list-comments" href="<?php echo $item['url']; ?>#comments_data_<?php echo Arr::get($item['feedback_comment_widget'], 'content'); ?>">
						<span class="label"><?php echo Arr::get($cmslabel, 'comments_num'); ?></span> (<?php echo Arr::get($item['feedback_comment_widget'], 'comments', 0); ?>)
					</a>
				<?php endif; ?>
			</div>

			<?php if(!empty($item['attributes_special'])): ?>
				<ul class="cp-list-attrs">
					<?php $a = 1; ?>
					<?php foreach($item['attributes_special'] as $attr): ?>
						<?php if(!in_array($attr['attribute_code'], ['posebne_oznake', 'poklon'])): ?>
							<li class="cp-list-attr">
								<?php if($attr['attribute_code'] == 'energetski-razred'): ?><a class="btn-attr-energy" data-slide-index="<?php echo $a++; ?>" href="<?php echo $item['url']; ?>"><?php endif; ?>
								<div>
									<div class="cp-list-attr-image">
										<?php if($attr['attribute_code'] == 'energetski-razred'): ?>
											<img loading="lazy" src="<?php if($attr['image']): ?><?php echo Utils::file_url($attr['image']); ?><?php else: ?>/media/images/no-image-50.jpg<?php endif; ?>" alt="" width="60" height="60">
										<?php else: ?>
											<img loading="lazy" src="<?php if($attr['attribute_image']): ?><?php echo Utils::file_url($attr['attribute_image']); ?><?php else: ?>/media/images/no-image-50.jpg<?php endif; ?>" alt="" width="50" height="50">
										<?php endif; ?>	
									</div>
									<div class="cp-list-attr-title"><?php echo $attr['attribute_title']; ?></div>
									<div class="cp-list-attr-value"><?php echo $attr['title']; ?></div>
								</div>
								<?php if($attr['attribute_code'] == 'energetski-razred'): ?></a><?php endif; ?>
							</li>
							<?php if($a == 4) break; ?>
							<?php $a++; ?>
						<?php endif; ?>
					<?php endforeach; ?>
				</ul>
			<?php endif; ?>
		</div>

		<?php $payment_installments_array = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array'), ['universal_return' => true]); ?>
		<?php
			$payment_installments_info = $payment_installments_array['info'];
			$payment_installments = $payment_installments_array['items'];
			$installments_max_price = (!empty($payment_installments_info['max_total'])) ? round($payment_installments_info['max_total'], 2) : Arr::get($item, 'basic_price_base');
		?>

		<div class="cp-list-col cp-list-col3">
			<div class="cp-list-col3-top">
				<?php if($item['price_custom'] > 0): ?>
					<div class="cp-price cp-list-price">
						<?php if($item['price_custom'] > 0): ?>
							<?php if(empty($payment_installments) OR $item['price_custom'] == $installments_max_price): ?>
								<div class="cp-price-discount">
									<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
										<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
									</div>
									<div class="cp-price-note"><?php echo Arr::get($cmslabel, 'our_price'); ?></div>
									<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
										<span class="cp-extra-price-lowest">
											<?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
											<strong>
												<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</strong>
										</span>
									<?php endif; ?>
								</div>
							<?php else: ?>
								<div class="cp-price-discount">
									<?php $card_price = end($payment_installments); ?>
									<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
										<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($card_price['total'] * $currency['exchange'], $currency['display']); ?></span>
										<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($card_price['total'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
									</div>
									<div class="cp-price-note"><?php echo str_replace(['%MIN%', '%MAX%'], [$card_price['min'], $card_price['max']], Arr::get($cmslabel, 'installments_price2')); ?></div>
									<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
										<span class="cp-extra-price-lowest">
											<?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
											<strong>
												<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
												<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
											</strong>
										</span>
									<?php endif; ?>
								</div>
							
								<div class="cp-card-prices cp-list-card-prices">
									<?php if ($item['discount_percent_base'] > 0 OR $item['price_custom'] < $installments_max_price): ?>
										<div class="cp-card-price">
											<span class="cp-card-discount">-<?php echo (int) round($item['discount_percent_base'], 0); ?>%</span>
											<span><?php echo Arr::get($cmslabel, 'cash_price2'); ?></span>
										</div>
									<?php endif; ?>

									<?php if(count($payment_installments) > 1): ?>
										<?php $p = 1; ?>
										<?php foreach($payment_installments as $installment): ?>
											<?php if($p != count($payment_installments)): ?>
											<?php $installment_info = Arr::get($installment, 'price_diff_by_highest_installment'); ?>
												<div class="cp-card-price">
													<span class="cp-card-discount">-<?php echo (int) round(Arr::get($installment_info, 'percent'), 0); ?>%</span>
													<span><?php echo str_replace(['%MIN%', '%MAX%'], [$installment['min'], $installment['max']], Arr::get($cmslabel, 'installments_price')); ?></span>
												</div>
											<?php endif; ?>
											<?php $p++; ?>
										<?php endforeach; ?>
									<?php endif; ?>
								</div>
							<?php endif; ?>
						<?php endif; ?>
					</div>
				<?php endif; ?>
				<?php echo View::factory('catalog/widget/energy_badge', ['item' => $item, 'class' => 'cp-list-attr-energy']); ?>
			</div>
			
			<div class="cp-list-col3-bottom">
				<?php if (isset($item['compare_widget'])): ?>
					<div class="cp-list-compare cp-list-compare-list">
						<?php echo View::factory('catalog/widget/set_compare', ['content' => $item['compare_widget']['content'], 'active' => $item['compare_widget']['active'], 'class' => 'cp-list-btn-compare']); ?>
					</div>
				<?php endif; ?>

				<?php if (!empty($item['wishlist_widget'])): ?>
					<div class="cp-list-wishlist cp-list-wishlist-list<?php if ($item['wishlist_widget']['active']): ?> active<?php endif; ?>" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>">
						<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '+', '', 2);" class="cp-list-wishlist-btn cp-wishlist-add wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></span></a>
						<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '-', '', 2);" class="cp-list-wishlist-btn cp-wishlist-remove wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?></span></a>
						<span class="product-in-wishlist wishlist_message wishlist_message_<?php echo $item['wishlist_widget']['content']; ?> wishlist-message wishlist-message-<?php echo $item['id']; ?>" style="display: none;"></span>
					</div>
				<?php endif; ?>
			</div>
		</div>
	</article>
<?php endforeach; ?>