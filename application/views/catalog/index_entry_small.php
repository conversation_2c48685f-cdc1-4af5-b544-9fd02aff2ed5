<?php $product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities'); ?>
<?php $mode = (isset($mode))? $mode : '';  ?>
<?php foreach ($items as $i => $item): ?>
	<?php $warranty_products = Widget_Catalog::products(['lang' => $info['lang'], 'limit' => 10, 'advanced_filters' => ['id' => $item['id'], 'category_id' => $item['category_id'], 'manufacturer_id' => $item['manufacturer_id'], 'attributes_ids' => $item['attributes_ids'], 'price' => $item['price_custom']], 'warranty_main_item_price' => ['basic_price' => $item['basic_price_custom'], 'price' => $item['price_custom']], 'warranty_product_id' => $item['id'], 'only_available' => TRUE, 'sort' => 'older']); ?>
	<article class="cp-small<?php if (!$item['is_available']): ?> cp-not-available<?php endif; ?>"<?php if ($mode == 'wishlist' AND !empty($item['wishlist_widget']['content'])): ?> data-wishlistitem_details="<?php echo $item['wishlist_widget']['content']; ?>"<?php endif; ?> data-tracking_gtm_impression="<?php echo $i; ?>|<?php echo $item['code']; ?>">
		<div class="cp-small-col cp-small-col1">
			<?php if(!empty($item['lists_info'])): ?>
				<div class="cp-badges cp-small-badges">
					<?php foreach($item['lists_info'] as $list_info): ?>
						<?php if(!empty($list_info['image'])): ?>
							<a class="cp-special-small-badge" href="<?php echo $list_info['url']; ?>">
								<img loading="lazy" <?php echo Thumb::generate($list_info['image'], array('width' => 30, 'height' => 30, 'default_image' => '/media/images/no-image-30.jpg', 'html_tag' => true)); ?> alt="" />
							</a>
						<?php endif; ?>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
			
			<figure class="cp-small-image">
				<a href="<?php echo $item['url']; ?>">
					<img loading="lazy" <?php echo Thumb::generate($item['main_image'], array('width' => 144, 'height' => 144, 'default_image' => '/media/images/no-image-144.jpg', 'html_tag' => true)); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>" />
				</a>
			</figure>
		</div>

		<div class="cp-small-col cp-small-col2">
			<div class="cp-small-header">
				<?php if($item['manufacturer_main_image']): ?>
					<div class="cp-small-brand cp-brand-<?php echo $item['manufacturer_code']; ?>" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>">
						<img <?php echo Thumb::generate($item['manufacturer_main_image'], array('width' => 95, 'height' => 30, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="<?php echo $item['manufacturer_title']; ?>" />
					</div>
				<?php endif; ?>

				<div class="cp-small-title"><a href="<?php echo $item['url']; ?>" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title']; ?></a></div>
				<div class="cp-small-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></div>

				<span style="display: none;" data-product_id="<?php echo $item['id'] ?>"><?php echo $item['id'] ?></span>
				<span style="display: none;" data-product_title="<?php echo $item['title'] ?>"><?php echo $item['title'] ?></span>
				<span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
				<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'category_title', ''); ?></span>
				<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title', '')); ?></span>

				<?php if(!empty($item['attributes_special'])): ?>
					<ul class="cp-attrs cp-small-attrs">
						<?php $a = 1; ?>
						<?php foreach($item['attributes_special'] as $attr): ?>
							<?php if(!in_array($attr['attribute_code'], ['posebne_oznake', 'poklon'])): ?>
								<li class="cp-attr"><?php echo $attr['title']; ?></li>
								<?php if($a == 4) break; ?>
								<?php $a++; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
			</div>

			<?php $payment_installments_array = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array'), ['universal_return' => true]); ?>
			<?php
				$payment_installments_info = $payment_installments_array['info'];
				$payment_installments = $payment_installments_array['items'];
				$installments_max_price = (!empty($payment_installments_info['max_total'])) ? round($payment_installments_info['max_total'], 2) : Arr::get($item, 'basic_price_base');
			?>

			<!-- Modal prices -->
			<?php $payment_installments_modal = Widget_Webshop::payments_installments($item['price_custom'], 'kreditna_kartica', $info['lang'], '', Arr::get($item, 'installments_supplement_array')); ?>
			<?php $basic_price_base = (!empty($payment_installments_modal)) ? round(max(array_column($payment_installments_modal, 'total')), 2) : $item['basic_price_base']; ?>

			<!-- Modal prices -->
			<div class="cp-modal-price" data-collect_price="<?php echo $item['shopping_cart_code']; ?>">
				<?php if ($item['discount_percent_base'] > 0 OR $item['price_custom'] < $basic_price_base): ?>
					<div class="modal-price-main">
						<div class="modal-old-price"><?php echo Utils::currency_format($basic_price_base * $currency['exchange'], $currency['display']); ?></div>
						<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
					</div>
					<div class="modal-price-second">
						<div class="modal-old-price"><?php echo Utils::get_second_pricetag_string($basic_price_base, ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></div>
						<?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?>						
					</div>
				<?php else: ?>
					<span><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
					<span><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?></span>
				<?php endif; ?>
			</div>

			<?php if($item['price_custom'] > 0): ?>
				<div class="cp-small-col2-bottom">
					<div class="cp-price cp-small-price">
						<?php if(empty($payment_installments) OR $item['price_custom'] == $installments_max_price): ?>
							<div class="cp-price-discount">
								<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
									<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
									<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item['price_custom'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
								</div>
								<?php echo View::factory('catalog/widget/energy_badge', ['item' => $item, 'class' => 'cp-small-attr-energy']); ?>
								<div class="cp-price-note"><?php echo Arr::get($cmslabel, 'our_price'); ?></div>
								<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
									<span class="cp-extra-price-lowest">
										<?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
										<strong>
											<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
											<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
										</strong>
									</span>
								<?php endif; ?>
							</div>
						<?php else: ?>
							<div class="cp-card-prices cp-small-card-prices">
								<?php if ($item['discount_percent_base'] > 0 OR $item['price_custom'] < $installments_max_price): ?>
									<div class="cp-card-price">
										<span class="cp-card-discount">-<?php echo (int) round($item['discount_percent_base'], 0); ?>%</span>
										<span><?php echo Arr::get($cmslabel, 'cash_price2'); ?></span>
									</div>
								<?php endif; ?>

								<?php if(count($payment_installments) > 1): ?>
									<?php $p = 1; ?>
									<?php foreach($payment_installments as $installment): ?>
										<?php if($p != count($payment_installments)): ?>
										<?php $installment_info = Arr::get($installment, 'price_diff_by_highest_installment'); ?>
											<div class="cp-card-price">
												<span class="cp-card-discount">-<?php echo (int) round(Arr::get($installment_info, 'percent'), 0); ?>%</span>
												<span><?php echo str_replace(['%MIN%', '%MAX%'], [$installment['min'], $installment['max']], Arr::get($cmslabel, 'installments_price')); ?></span>
											</div>
										<?php endif; ?>
										<?php $p++; ?>
									<?php endforeach; ?>
								<?php endif; ?>
							</div>

							<div class="cp-price-discount">
								<?php $card_price = end($payment_installments); ?>
								<div class="cp-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>">
									<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($card_price['total'] * $currency['exchange'], $currency['display']); ?></span>
									<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($card_price['total'], ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
								</div>
								<?php echo View::factory('catalog/widget/energy_badge', ['item' => $item, 'class' => 'cp-small-attr-energy']); ?>
								<div class="cp-price-note"><?php echo str_replace(['%MIN%', '%MAX%'], [$card_price['min'], $card_price['max']], Arr::get($cmslabel, 'installments_price')); ?></div>
								<?php if(!empty($item['extra_price_lowest']) AND $item['extra_price_lowest'] > 0): ?>
									<span class="cp-extra-price-lowest">
										<?php echo Arr::get($cmslabel, 'extra_price_lowest'); ?>: 
										<strong>
											<?php echo Utils::currency_format($item['extra_price_lowest'] * $currency['exchange'], $currency['display']); ?>
											<?php echo Utils::get_second_pricetag_string($item['extra_price_lowest'], ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
										</strong>
									</span>
								<?php endif; ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			<?php endif; ?>
		</div>
			
		<div class="cp-small-col cp-small-col3">
			<?php if (!empty($item['wishlist_widget'])): ?>
				<div class="cp-wishlist cp-small-wishlist<?php if ($item['wishlist_widget']['active']): ?> active<?php endif; ?>" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>">
					<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '+', '', 2);" class="cp-small-wishlist-btn cp-wishlist-add wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'add_to_wishlist'); ?></span></a>
					<a href="javascript:cmswishlist.set('<?php echo $item['wishlist_widget']['content']; ?>', '-', '', 2);" class="cp-small-wishlist-btn cp-wishlist-remove wishlist_set_<?php echo $item['wishlist_widget']['content']; ?>" data-fancybox_w="560" data-wishlist_item_active="<?php echo $item['wishlist_widget']['content']; ?>"><span><?php echo Arr::get($cmslabel, 'remove_from_wishlist'); ?></span></a>
					<span class="product-in-wishlist wishlist_message wishlist_message_<?php echo $item['wishlist_widget']['content']; ?> wishlist-message wishlist-message-<?php echo $item['id']; ?>" style="display: none;"></span>
				</div>
			<?php endif; ?>

			<?php if (isset($item['compare_widget'])): ?>
				<div class="cp-small-compare">
					<?php echo View::factory('catalog/widget/set_compare', ['content' => $item['compare_widget']['content'], 'active' => $item['compare_widget']['active'], 'class' => 'cp-small-btn-compare']); ?>
				</div>
			<?php endif; ?>
			
			<?php if($item['is_available'] > 0 AND $item['available_qty'] > 0): ?>
				<?php /*if (empty($warranty_products)) : ?>
					<a href="javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code'];?>', '_service2_product,_tracking:index', 'simple_loader', 'input', 3, 0, 0, 0, 0, 0);" class="btn btn-cp-add-detail cp-btn-addtocart" title="<?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?>" data-gtm-product="<?php echo $item['title']; ?>" data-gtm="add-to-cart"><span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span></a>
				<?php else: ?>
					<a href="javascript:cmswebshop.shopping_cart.add_warranty('<?php echo $item['shopping_cart_code'];?>', '<?php echo $item['price_custom'];?>', '<?php echo $item['category_id'];?>', '<?php echo $item['manufacturer_id'];?>', '<?php echo $item['title'];?>');" class="btn btn-cp-add-detail cp-small-add cp-btn-addtocart" title="<?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?>" data-gtm-product="<?php echo $item['title']; ?>" data-gtm="add-to-cart"><span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span></a>
				<?php endif;*/ ?>
                <a href="javascript:cmswebshop.shopping_cart.add_warranty('<?php echo $item['shopping_cart_code'];?>', '<?php echo $item['price_custom'];?>', '<?php echo $item['category_id'];?>', '<?php echo $item['manufacturer_id'];?>', '<?php echo $item['id'];?>');" class="btn btn-cp-add-detail cp-small-add cp-btn-addtocart" title="<?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?>" data-gtm-product="<?php echo $item['title']; ?>" data-gtm="add-to-cart"><span><?php echo Arr::get($cmslabel, 'add_to_shopping_cart'); ?></span></a>
			<?php endif; ?>
		</div>
	</article>
<?php endforeach; ?>