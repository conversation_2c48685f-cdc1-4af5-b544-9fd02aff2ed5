<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-brands<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="m-wrapper">
		<div class="m-header">
			<div class="bc">
				<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
			</div>
			<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
			<?php echo Arr::get($cms_page, 'content'); ?>
		</div>

		<?php $manufacturers = Widget_Catalog::manufacturers(array('lang' => $info['lang'], 'limit' => 12, 'special' => 1)); ?>
		<?php if(!empty($manufacturers)): ?>
			<div class="m-special">
				<?php foreach ($manufacturers as $manufacturer):?>      
					<a class="m-special-item" href="<?php echo $manufacturer['url']; ?>">
						<span><img <?php echo Thumb::generate($manufacturer['main_image'], array('width' => 100, 'height' => 30, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="<?php echo $manufacturer['title']; ?>" /></span>
					</a>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>

		<div class="m-items">
			<?php if (sizeof($items)): ?>
				<?php $last_alphabet = ''; ?>
				<?php $i = 1; ?>
				<?php foreach ($items as $item): ?>
					<?php $current_alphabet = Utf8::strtoupper(substr($item['title'], 0, 1)); ?>
					<?php if ($current_alphabet != $last_alphabet): ?>
						<?php if ($i != 1): ?></ul><div class="clear"></div></div><?php endif; ?>
						<div class="m-column">
						<h3 class="m-letter"><?php echo $current_alphabet; ?></h3>
						<ul class="m-list">
					<?php endif; ?>
					<li>
						<div class="m-title"><a href="<?php echo $item['url']; ?>"><?php echo $item['title']; ?></a></div>
					</li>

					<?php if($i == count($items)): ?></ul><div class="clear"></div></div><?php endif; ?>
					<?php $last_alphabet = $current_alphabet; ?>
					<?php $i++; ?>
				<?php endforeach; ?>
				<div class="clear"></div>
			<?php else: ?>
				<?php echo Arr::get($cmslabel, 'no_manufacturers'); ?>
			<?php endif; ?>
		</div>
	</div>
<?php $this->endblock('content_layout'); ?>