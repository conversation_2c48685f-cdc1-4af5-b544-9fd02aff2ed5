<div class="cd-unavailable">
	<div class="cd-unavailable-header"><?php echo Arr::get($cmslabel, 'not_available', 'Oba<PERSON><PERSON><PERSON>i me kada postane dostupno'); ?></div>

	<div class="cd-notify-form">
		<div id="notifyme-<?php echo $form_content; ?>">
			<form action="#notifyme_form" data-main_action="notifyme_form" method="post" name="notifyme_form" id="notifyme_add_form_<?php echo $form_content; ?>">
				<div class="cd-notify-input">
					<input type="hidden" name="id" value="<?php echo $form_content; ?>" />
					<input type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'enter_email'); ?>" <?php if ($info['user_id'] AND $info['user_email']): ?>value="<?php echo $info['user_email']; ?>"<?php endif; ?>>
					<button type="submit" class="btn btn-red btn-notify g-recaptcha" data-sitekey="<?php echo Utils::get_recaptcha_key('site_key'); ?>" data-callback="onSubmit" data-action="submit" data-before_submit><?php echo Arr::get($cmslabel, 'notifyme'); ?></button>
				</div>
				<span id="field-error-email" class="field_error error" style="display: none"><?php echo Arr::get($cmslabel, 'error_email', 'error_email'); ?></span>
			</form>
			<div class="notifyme_success message" style="display: none">
				<?php echo Arr::get($cmslabel, 'notifyme_catalog_ty'); ?>
			</div>
		</div>
	</div>
</div>