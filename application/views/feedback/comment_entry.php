<article class="clear comment<?php if ($item['manager']): ?> comment-manager<?php endif; ?><?php if ($item['parent_id']): ?> comment-child<?php endif; ?>" id="comment-<?php echo $item['id']; ?>">
	<div class="comment-col comment-content">
    	<div class="comment-header">
			<?php if (!empty($item['rate'])): ?>
				<span class="comment-rate">
					<?php for ($rate = 1; $rate <=5; $rate++): ?>
						<span class="icon-star-empty<?php if ($rate <= $item['rate']): ?> icon-star<?php endif ?>"></span>		
					<?php endfor; ?>
				</span>
			<?php endif; ?>
    		<span class="comment-username"><?php echo $item['display_name']; ?></span> - <span class="comment-date"><?php echo Date::humanize($item['datetime_created'], 'custom', ' F Y'); ?></span>
    	</div>
		<div class="comment-message" id="comment-message-<?php echo $item['id']; ?>"><?php echo $item['message']; ?></div>

		<?php /*if ($item['can_reviews']): ?>
			<div class="comment-review">
				<span id="review_links_<?php echo $item['id']; ?>" class="review_links">
					<span class="comment-review-label"><?php echo Arr::get($cmslabel, 'rate'); ?></span>
					<a class="review-icon review_up" href="javascript:cmsfeedback.reviewcomment(<?php echo $item['id']; ?>, +1);">
						<span class="label"><?php echo Arr::get($cmslabel, 'commentreview_up', 'Da'); ?></span>
						<span id="review_comment_positive_<?php echo $item['id']; ?>" class="review_comment review-positive"><?php echo $item['review_positive']; ?></span>
					</a>
				</span>
				<span id="review_comment_success_<?php echo $item['id']; ?>" style="display: none" class="comment-review-success"><?php echo Arr::get($cmslabel, 'commentreview_success'); ?></span>
			</div>
		<?php else: ?>
			<div class="comment-review">
				<span id="review_links_<?php echo $item['id']; ?>" class="review_links">
					<span class="comment-review-label"><?php echo Arr::get($cmslabel, 'rate'); ?></span>
					<span class="review-icon review_up">
						<span class="label"><?php echo Arr::get($cmslabel, 'commentreview_up', ''); ?></span>
						<span id="review_comment_positive_<?php echo $item['id']; ?>" class="review_comment review-positive"><?php echo $item['review_positive']; ?></span>
					</span>
				</span>
			</div>
		<?php endif;*/ ?>		

		<?php if ($item['can_edit']): ?>
			<a class="comment_edit" href="javascript:cmsfeedback.editcomment(<?php echo $item['id']; ?>);">edit</a>
			<span id="comment_edit_success_<?php echo $item['id']; ?>" style="display: none"><?php echo Arr::get($cmslabel, 'comment_edit_success'); ?></span>
			<span id="comment_edit_error_<?php echo $item['id']; ?>" style="display: none"><?php echo Arr::get($cmslabel, 'comment_edit_error'); ?></span>
		<?php endif; ?>
		<?php if ($item['can_delete']): ?>
			<a class="comment_delete" href="javascript:cmsfeedback.deletecomment(<?php echo $item['id']; ?>);"><?php echo Arr::get($cmslabel, 'comment_delete'); ?></a>
			<span id="comment_delete_error_<?php echo $item['id']; ?>" style="display: none"><?php echo Arr::get($cmslabel, 'comment_delete_error'); ?></span>
		<?php endif; ?>
		<?php /*if (!$item['parent_id']): ?>
			<a class="btn-comment-replay" href="javascript:cmsfeedback.replaycomment(<?php echo $item['id']; ?>);replayForm();"><?php echo Arr::get($cmslabel, 'comment_replay', 'comment_replay'); ?></a>
		<?php endif;*/ ?>
	</div>
</article>
<span id="comment_delete_success_<?php echo $item['id']; ?>" style="display: none"><?php echo Arr::get($cmslabel, 'comment_delete_success'); ?></span>
<div class="clear"></div>