<?php if ($items_total): ?>
	<div id="comment-list-items">
		<?php foreach ($items as $item): ?>
			<?php echo View::factory('feedback/comment_entry', ['item' => $item, 'items_reviews' => $items_reviews]); ?>
		<?php endforeach; ?>
	</div>
	<input type="hidden" name="comments-list-lastcheck" id="lastcheck" value="<?php echo time(); ?>" />
	<input type="hidden" name="comments-list-page" id="comments-list-page" value="1" />
	<div class="comments-load-more">
		<a class="btn btn-load-more-comments" id="comment-list-more" href="javascript:cmsfeedback.getonlycomments('<?php echo $content; ?>', 'append', '<?php echo $sort; ?>')" style="display: none"><?php echo Arr::get($cmslabel, 'comment_load_more'); ?></a>
	</div>
<?php else: ?>
	<div class="no-comments"><?php echo Arr::get($cmslabel, 'no_comments'); ?></div>
<?php endif; ?>