<div id="comment_form" class="clear comment-form-container">
	<?php $form = Widget_Feedback::comment_form($_POST, $info); ?>
	<?php if ($form['request_login'] AND ! $user): ?>
		<div class="comment-form-login"><?php echo Arr::get($cmslabel, 'comment_request_login'); ?></div>
	<?php else: ?>
		<form class="clear form-label comment-form" action="#comment_form" method="post" name="comment_form" id="comment_add_form_<?php echo $content; ?>">
			<input type="hidden" name="id" value="<?php echo $content; ?>" />
			<input type="hidden" name="comment_id" value="" />
			<input type="hidden" name="parent_id" value="0" />
			<input type="hidden" name="lang" value="<?php echo $lang; ?>" />
			<?php foreach ($form['customer_fields'] as $field): ?>
				<?php if ($field[0] == ':'): ?>
					<div class="clear"></div>
				<?php else: ?>
					<p class="field comment-field comment-field-<?php echo $field; ?>">
						<?php $label = (Arr::get($cmslabel, 'form_comments_'.$field)) ? Arr::get($cmslabel, 'form_comments_'.$field) : Arr::get($cmslabel, $field); ?>
						<label for="field-<?php echo $field; ?>"><?php echo $label; ?></label>
						<?php echo $form['item']->input($field, 'form'); ?>
						<span id="field-error-<?php echo $field; ?>" class="error" style="display: none"></span>
					</p>
					<?php if($field == 'email'): ?>
						<div class="field comment-field comment-field-rate">
							<div class="label"><?php echo Arr::get($cmslabel, 'your_rate'); ?></div>
							<?php for ($rate = 1; $rate <= 5; $rate++): ?>
								<span class="comment-rate-item comment-rate-<?php echo $rate; ?> add_comment_rate" data-comment_rate="<?php echo $rate; ?>">
									<input type="radio" id="comment-rate<?php echo $rate; ?>" name="rate" value="<?php echo $rate ?>">
									<label for="comment-rate<?php echo $rate; ?>"><?php echo $rate ?></label>
								</span>
							<?php endfor; ?>
						</div>
					<?php endif; ?>
				<?php endif; ?>
			<?php endforeach; ?>
			<div class="buttons comment-buttons">
				<button class="btn btn-yellow btn-send-comment" type="submit"><?php echo Arr::get($cmslabel, 'send_comment', 'Pošalji komentar'); ?></button>
				<a id="cancel-edit" class="btn-link" href="javascript:cmsfeedback.replaycomment(0);" style="display: none"><?php echo Arr::get($cmslabel, 'comment_edit_cancel', 'Odustani'); ?></a>
				<a id="cancel-replay-to" class="btn-comment-cancel" href="javascript:cmsfeedback.replaycomment(0);cancelReplayForm();" style="display: none"><?php echo Arr::get($cmslabel, 'comment_replay_cancel', 'Zatvori'); ?></a>
			</div>
			<div class="comment-form-note"><?php echo Arr::get($cmslabel, 'comment_form_note'); ?></div>
		</form>
	<?php endif; ?>
	<div class="comment_success" style="display: none" data-feedback_ignore_scroll="1">
		<div class="comment-success-message"><?php echo Arr::get($cmslabel, 'comment_success'); ?></div>
		<a class="btn btn-yellow" id="comment_add_new" href="javascript:cmsfeedback.replaycomment(0);"><?php echo Arr::get($cmslabel, 'comment_add_new'); ?></a>
	</div>
	<div class="clear"></div>
</div>
