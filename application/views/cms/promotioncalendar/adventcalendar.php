<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-calendar<?php $this->endblock('page_class'); ?>

<?php $selected_calendar = $cms_page['selected_calendar_code'] ?>
<?php $promotion_calendar = $cms_page['promotion_calendar'] ?>

<?php $promotion_calendar_from = $cms_page['promotion_calendar']['datetime_active_from']; ?>
<?php $promotion_calendar_to = $cms_page['promotion_calendar']['datetime_active_to']; ?>

<?php /*$promotion_calendar_color = $promotion_calendar['color']; ?>
<?php $promotion_calendar_header = $promotion_calendar['bg_image']; ?>
<?php $promotion_calendar_footer = $promotion_calendar['bg_image_2']; */?>

<?php $this->block('extrahead'); ?>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
	<link href="https://fonts.googleapis.com/css2?family=Grand+Hotel&display=swap" rel="stylesheet">
	<style>
		@font-face {
			font-family: 'Gloria Hallelujah';
			src: local('☺'), url('../../../media/fonts/gloriahallelujah.woff') format('woff');
			font-weight: normal;
			font-style: normal;
			font-display: block;
		}
		.advent-calendar h1, .advent-calendar h2, .advent-calendar h3, .advent-calendar h4{font-family: 'Gloria Hallelujah', sans-serif; font-weight: normal; text-transform: unset; padding-top: 0;}
	</style>
<?php $this->endblock('extrahead'); ?>

<?php $this->block('main_layout'); ?>
	<div class="advent-calendar">
		<?php if(!empty($promotion_calendar_from) AND !empty($promotion_calendar_to) AND (date('Ymd') >= date('Ymd', $promotion_calendar_from) AND date('Ymd') <= date('Ymd', $promotion_calendar_to))): ?>
			<div class="wrapper">
				<div class="ac-intro">
					<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
					<?php if(!empty($cms_page['content'])): ?>
						<?php echo Arr::get($cms_page, 'content'); ?>
					<?php endif; ?>
				</div>
				<div class="ac-items">
					<?php $i = 1 ?>
					<?php foreach ($promotion_calendar['items'] AS $date => $item): ?>
						<?php if (date('Ymd') == date('Ymd', Arr::get($item, 'item_date'))): ?>
							<a class="advent-item aci<?php echo $i ?>" href="/<?php echo $selected_calendar ?>-detalji/?calendaritem_id=<?php echo $item['id']; ?>">
						<?php else: ?>
							<div class="advent-item advent-item-disabled aci<?php echo $i ?>">
						<?php endif; ?>
							<span class="aci-icon aci-icon<?php echo $i ?>"></span>
							<span class="aci-number"><?php echo $i ?></span>
							<div class="back-side">
								<?php if (date('Ymd') == date('Ymd', Arr::get($item, 'item_date'))): ?>
									<h4><?php echo Arr::get($cmslabel, 'promotion_calendar_today'); ?></h4>
								<?php elseif (date('Ymd') > date('Ymd', Arr::get($item, 'item_date'))): ?>
									<?php echo Arr::get($cmslabel, 'promotion_calendar_ended'); ?>
								<?php else: ?>
									<?php echo Arr::get($cmslabel, 'promotion_calendar_otherdays'); ?>
								<?php endif; ?>
							</div>
						<?php if (date('Ymd') == date('Ymd', Arr::get($item, 'item_date'))): ?>
							</a>
						<?php else: ?>
							</div>
						<?php endif; ?>
						<?php $i++; ?>
					<?php endforeach; ?>
				</div>
			</div>
		<?php elseif(date('Ymd') < date('Ymd', $promotion_calendar_from)): ?>
			<div class="ac-intro">
				<?php $selected_calendar_before_title = Arr::get($cmslabel, $selected_calendar.'_before_start_title'); ?>
				<?php $selected_calendar_before_content = Arr::get($cmslabel, $selected_calendar.'_before_start_content'); ?>
				
				<?php if(!empty($selected_calendar_before_title)): ?>
					<h1><?php echo $selected_calendar_before_title ?></h1>
				<?php else: ?>
					<h1><?php echo Arr::get($cmslabel, 'promotion_before_start_title'); ?></h1>
				<?php endif; ?>

				<?php if(!empty($selected_calendar_before_content)): ?>
					<?php echo $selected_calendar_before_content ?>
				<?php else: ?>
					<?php echo Arr::get($cmslabel, 'promotion_before_start_content'); ?>
				<?php endif; ?>

				<a class="btn-green2 back-to-homepage" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'back_to_homepage'); ?></a>
			</div>
		<?php else: ?>
			<div class="ac-intro ac-intro-ended">
				<?php $selected_calendar_ended_title = Arr::get($cmslabel, $selected_calendar.'_ended_title'); ?>
				<?php $selected_calendar_ended_content = Arr::get($cmslabel, $selected_calendar.'_ended_content'); ?>

				<?php if(!empty($selected_calendar_ended_title)): ?>
					<h1><?php echo $selected_calendar_ended_title ?></h1>
				<?php else: ?>
					<h1><?php echo Arr::get($cmslabel, 'promotion_ended_title'); ?></h1>
				<?php endif; ?>

				<?php if(!empty($selected_calendar_ended_content)): ?>
					<?php echo $selected_calendar_ended_content ?>
				<?php else: ?>
					<?php echo Arr::get($cmslabel, 'promotion_ended_content'); ?>
				<?php endif; ?>

				<a class="btn-green2 back-to-homepage" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'back_to_homepage'); ?></a>
			</div>
		<?php endif; ?>
	</div>
<?php $this->endblock('main_layout'); ?>
<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>