<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-calendar page-calendar-detail<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
	<style>
		@font-face {
			font-family: 'Gloria Hallelujah';
			src: local('☺'), url('../../../media/fonts/gloriahallelujah.woff') format('woff');
			font-weight: normal;
			font-style: normal;
			font-display: block;
		}
		.advent-calendar h1, .advent-calendar h2, .advent-calendar h3, .advent-calendar h4{font-family: 'Gloria Hallelujah', sans-serif; font-weight: normal; text-transform: unset; padding-top: 0;}
	</style>
<?php $this->endblock('extrahead'); ?>

<?php $calendar_item = Catalog::promotion_calendar_item(Arr::get($_GET, 'calendaritem_id', 0));?>

<?php $this->block('main_layout'); ?>
	<div class="advent-calendar advent-calendar-detail">
		<div class="wrapper">
			<div class="ac-intro">
				<?php if (!empty($calendar_item['error'])): ?>
					<h1><?php echo Arr::get($cmslabel, 'promotion_item_before_start_title'); ?></h1>
					<p><?php echo Arr::get($cmslabel, 'promotion_item_before_start_content'); ?></p>
				<?php else: ?>
					<?php $calendar_item_lang = $calendar_item['description_'.$info['lang']]; ?>
					<?php $calendar_item_title = $calendar_item['title_'.$info['lang']]; ?>
					<?php if(!empty($calendar_item_title)): ?><h1><?php echo $calendar_item_title ?></h1><?php endif; ?>
					<?php if(!empty($calendar_item_lang)): ?><p><?php echo $calendar_item_lang ?></p><?php endif; ?>
					<?php if(!empty($calendar_item['cataloglist_code']) AND !empty($calendar_item['cataloglist_id']) AND $calendar_item['item_type'] == '1'): ?>
						<?php $calendar_item_list = Widget_Catalog::speciallist($info['lang'], $calendar_item['cataloglist_code'], true); ?>
						<a href="<?php if(!empty($calendar_item_list['url'])): ?><?php echo $calendar_item_list['url'] ?><?php endif; ?>" class="btn-green2"><?php echo Arr::get($cmslabel, 'promotion_see_products'); ?></a>
					<?php elseif(!empty($calendar_item['coupon_code']) AND !empty($calendar_item['coupon_id']) AND $calendar_item['item_type'] == '2'): ?>
						<div class="calendar-coupons">
							<?php $coupons = Webshop::coupons(array('lang' => $info['lang'], 'only_my' => TRUE, 'user_email' => $info['user_email'])); ?>
							<?php $active_coupons = array_values($shopping_cart['total_extra_coupon_code']) ?>
							
							<?php if($shopping_cart['item_count'] > 0 AND in_array($calendar_item['coupon_code'], $active_coupons)): ?>
								<div class="calendar-btn-coupon-active">
									<span class="ww-coupons-title2"><?php echo str_replace("%COUPON_CODE%", $calendar_item['coupon_code'], Arr::get($cmslabel, 'coupon_activate_message')); ?></span>
									<a class="btn-green2 calendar-btn-coupon-homepage" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'promotion_see_homepage'); ?></a>
								</div>
							<?php elseif (empty($coupons[$calendar_item["coupon_id"]])): ?>
								<?php if($shopping_cart['item_count'] > 0): ?>
									<input class="ww-coupons-input" type="hidden" name="coupon_code" id="coupon_code" value="<?php echo $calendar_item['coupon_code']; ?>" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
									<div class="coupon_message ww-coupons-title2" style="display: none"></div>
									<a class="btn-green2 btn-coupon-add calendar-btn-coupon" href="javascript:cmscoupon.set('webshop_coupon', '<?php echo $calendar_item['coupon_code']; ?>')"><?php echo Arr::get($cmslabel, 'activate_coupon'); ?></a>
									<div class="calendar-btn-coupon-active2">
										<?php if(in_array($calendar_item['coupon_code'], $active_coupons)): ?>
											<span class="ww-coupons-title2"><?php echo str_replace("%COUPON_CODE%", $calendar_item['coupon_code'], Arr::get($cmslabel, 'coupon_activate_message_activated')); ?></span>
										<?php else: ?>
											<span class="ww-coupons-title2"><?php echo str_replace("%COUPON_CODE%", $calendar_item['coupon_code'], Arr::get($cmslabel, 'coupon_activate_message')); ?></span>
										<?php endif; ?>
										<a class="btn-green2 calendar-btn-coupon-homepage" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'promotion_see_homepage'); ?></a>
									</div>
								<?php else: ?>
									<input class="ww-coupons-input" type="hidden" name="coupon_code" id="coupon_code" value="<?php echo $calendar_item['coupon_code']; ?>" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
									<a class="btn-green2 btn-coupon-add calendar-btn-coupon" href="javascript:cmscoupon.save_for_later('webshop_coupon')"><?php echo Arr::get($cmslabel, 'activate_coupon'); ?></a>
									<div class="calendar-btn-coupon-active2">
										<span class="ww-coupons-title2"><?php echo str_replace("%COUPON_CODE%", $calendar_item['coupon_code'], Arr::get($cmslabel, 'coupon_activate_message_empty')); ?></span>
										<a class="btn-green2 calendar-btn-coupon-homepage" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'promotion_see_homepage'); ?></a>
									</div>
								<?php endif; ?>
							<?php else: ?>
								<div class="calendar-btn-coupon-active">
									<?php if($shopping_cart['item_count'] > 0): ?>
										<?php if(in_array($calendar_item['coupon_code'], $active_coupons)): ?>
											<span class="ww-coupons-title2"><?php echo str_replace("%COUPON_CODE%", $calendar_item['coupon_code'], Arr::get($cmslabel, 'coupon_activate_message_activated')); ?></span>
										<?php else: ?>
											<span class="ww-coupons-title2"><?php echo str_replace("%COUPON_CODE%", $calendar_item['coupon_code'], Arr::get($cmslabel, 'coupon_activate_message_activated2')); ?></span>
										<?php endif; ?>
									<?php else: ?>
										<span class="ww-coupons-title2"><?php echo str_replace("%COUPON_CODE%", $calendar_item['coupon_code'], Arr::get($cmslabel, 'coupon_activate_message_empty')); ?></span>
									<?php endif; ?>
									<a class="btn-green2 calendar-btn-coupon-homepage" href="<?php echo Utils::homepage($info['lang']); ?>"><?php echo Arr::get($cmslabel, 'promotion_see_homepage'); ?></a>
								</div>
							<?php endif; ?>
						</div>
					<?php endif; ?>
				<?php endif; ?>
			</div>
		</div>
	</div>
<?php $this->endblock('main_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>
