<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-wide page-flyer<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<?php if(Arr::get($cmslabel, 'flyer_pdf_download')): ?>
		<div class="flyer-download"><?php echo Arr::get($cmslabel, 'flyer_pdf_download'); ?></div>
	<?php endif; ?>

	<?php $rotator_items = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'flyer', 'limit' => 1]); ?>
	<?php if($rotator_items): ?>
		<?php foreach ($rotator_items as $item): ?>
			<h1><?php echo $item['title']; ?></h1>
			<div class="page-subtitle"><?php echo $item['title2']; ?></div>

			<div class="flyer-content"><?php echo $item['content']; ?></div>
		<?php endforeach; ?>
	<?php endif; ?>
<?php $this->endblock('content_layout'); ?>