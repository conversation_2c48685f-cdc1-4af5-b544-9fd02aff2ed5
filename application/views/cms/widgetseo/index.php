<meta property="og:site_name" content="<?php echo Text::meta(Utils::site_name()); ?>"/>
<?php $noimage = Utils::no_image((isset($noimage) ? $noimage : ''), $info['site_url']); ?>
<?php $is_search = ((sizeof($_GET) == 1 AND isset($_GET['search_q'])) OR ( sizeof($_GET) == 2 AND isset($_GET['search_q'])AND $pagination->current_page > 1)); ?>
<?php $page = (!empty($pagination->current_page) ? $pagination->current_page : 1); ?>
<?php if (isset($category) AND $category): ?>
    <?php if (is_array($category)): ?>

    <?php else: ?>
        <?php if (isset($category->seo_description)): ?><meta name="description" content="<?php echo Text::meta($category->seo_description); ?>" /><?php endif; ?>
        <?php if (isset($category->seo_keywords)): ?><meta name="keywords" content="<?php echo Text::meta($category->seo_keywords); ?>" /><?php endif; ?>
        <meta property="og:title" content="<?php echo Text::meta($category->seo_title); ?>"/>
        <meta property="twitter:title" content="<?php echo Text::meta($category->seo_title); ?>"/>
        <meta property="og:description" content="<?php echo Text::meta(($category->seo_description ? $category->seo_description : $category->short_description)); ?>" />
        <meta property="twitter:description" content="<?php echo Text::meta(($category->seo_description ? $category->seo_description : $category->short_description)); ?>" />
        <?php if (!empty($category->item->image)): ?>
            <meta property="og:image" content="<?php echo Utils::file_url($category->item->image); ?>" />
            <meta property="twitter:image" content="<?php echo Utils::file_url($category->item->image); ?>" />
            <link rel="image_src" href="<?php echo Thumb::generate($category->item->image, 300, 800, FALSE, 'thumb', TRUE, $noimage); ?>" />
        <?php elseif ($noimage): ?>
            <meta property="og:image" content="<?php echo $noimage; ?>" />
            <meta property="twitter:image" content="<?php echo $noimage; ?>" />
            <link rel="image_src" href="<?php echo $noimage; ?>" />
        <?php endif; ?>
    <?php endif; ?>
<?php elseif (!empty($kind)): ?>
    <?php if (is_array($kind)): ?>
        <?php
        if ($page > 1 OR ! empty($extra_kind)) {
            $kind['seo_description'] = '';
            $kind['short_description'] = '';
            $kind['content'] = '';
            $kind['seo_keywords'] = '';
        }

        if ($page > 1 AND isset($pagination)) {
            if (!empty($kind['seo_title_full'])) {
                $kind['seo_title_full'] .= sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $page, $pagination->total_pages);
            } elseif (!empty($kind['seo_title'])) {
                $kind['seo_title'] .= sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $page, $pagination->total_pages);
            }
        }
        ?>
        <?php $og_title_exist = false; ?>
        <?php if (!empty($item['seo_og_title'])): ?>
            <meta property="og:title" content="<?php echo Text::meta($item['seo_og_title']); ?>"/>
            <?php $og_title_exist = true; ?>
        <?php endif; ?>
        <?php $og_description_exist = false; ?>
        <?php if (!empty($item['seo_og_description'])): ?>
            <meta property="og:description" content="<?php echo Text::meta($item['seo_og_description']); ?>"/>
            <?php $og_description_exist = true; ?>
        <?php endif; ?>
        <?php $og_image_exist = false; ?>
        <?php if (!empty($kind['seo_og_image'])): ?>
            <meta property="og:image" content="<?php echo Utils::file_url($kind['seo_og_image']); ?>" />
            <?php
            $og_image_exist = true;
            $noimage = Utils::file_url($kind['seo_og_image']);
            ?>
        <?php endif; ?>
        <?php $custom_text = $kind['title'] . " iz webshopa Centar Tehnike visoke kvalitete i proizvođača iz kategorije " . $kind['title'] . ". Plaćanje do 36 rata ✓"; ?>
        <?php if (!empty($kind['seo_description'])): ?>
            <meta name="description" content="<?php echo Text::meta($kind['seo_description'], 0, 325); ?>" />
            <?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta($kind['seo_description'], 0, 325); ?>" /><?php endif; ?>
        <?php elseif (!empty($custom_text)) : ?>
            <meta name="description" content="<?php echo Text::meta($custom_text, 0, 325); ?>"/>
        <?php elseif (!empty($kind['short_description'])): ?>
            <meta name="description" content="<?php echo Text::meta($kind['short_description'], 0, 325); ?>" />
            <?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta($kind['short_description'], 0, 325); ?>" /><?php endif; ?>
        <?php elseif (!empty($kind['content'])): ?>
            <meta name="description" content="<?php echo Text::meta($kind['content'], 0, 325); ?>" />
            <?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta($kind['content'], 0, 325); ?>" /><?php endif; ?>
        <?php else: ?>
            <meta name="description" content="" />
            <?php if (!$og_description_exist): ?><meta property="og:description" content="" /><?php endif; ?>
        <?php endif; ?>
        <?php if (!empty($kind['seo_keywords'])): ?><meta name="keywords" content="<?php echo Text::meta($kind['seo_keywords']); ?>" /><?php endif; ?>
        <?php if (!empty($kind['seo_title_full'])): ?>
            <?php if (!$og_title_exist): ?><meta property="og:title" content="<?php echo (Kohana::config('app.cms.category_seo_title_exclude_parents')) ? Text::meta($kind['seo_title']) : Text::meta($kind['seo_title_full']); ?>"/><?php endif; ?>
            <meta property="twitter:title" content="<?php echo Text::meta($kind['seo_title_full']); ?>"/>
        <?php elseif (!empty($kind['seo_title'])): ?>
            <?php if (!$og_title_exist): ?><meta property="og:title" content="<?php echo Text::meta($kind['seo_title']); ?>"/><?php endif; ?>
            <meta property="twitter:title" content="<?php echo Text::meta($kind['seo_title']); ?>"/>
        <?php endif; ?>
        <?php if (!empty($kind['main_image'])): ?>
            <?php if (!$og_image_exist): ?><meta property="og:image" content="<?php echo Utils::file_url($kind['main_image']); ?>" /><?php endif; ?>
            <meta property="twitter:image" content="<?php echo Utils::file_url($kind['main_image']); ?>" />
            <link rel="image_src" href="<?php echo Thumb::generate($kind['main_image'], 300, 800, FALSE, 'thumb', TRUE, $noimage, 85); ?>" />
        <?php elseif ($noimage): ?>
                <?php if (!$og_image_exist): ?><meta property="og:image" content="<?php echo $noimage; ?>" /><?php endif; ?>
            <meta property="twitter:image" content="<?php echo $noimage; ?>" />
            <link rel="image_src" href="<?php echo $noimage; ?>" />
        <?php endif; ?>
        <?php if (!empty($kind['amp_url'])): ?>
            <link rel="amphtml" href="<?php echo $kind['amp_url']; ?>" />
        <?php endif; ?>
    <?php else: ?>
        <?php if (isset($kind->seo_description)): ?><meta name="description" content="<?php echo Text::meta($kind->seo_description); ?>" /><?php endif; ?>
        <?php if (isset($kind->seo_keywords)): ?><meta name="keywords" content="<?php echo Text::meta($kind->seo_keywords); ?>" /><?php endif; ?>
        <meta property="og:title" content="<?php echo Text::meta($kind->seo_title); ?>"/>
        <meta property="twitter:title" content="<?php echo Text::meta($kind->seo_title); ?>"/>
        <meta property="og:description" content="<?php echo Text::meta(($kind->seo_description ? $kind->seo_description : $kind->short_description)); ?>" />
        <meta property="twitter:description" content="<?php echo Text::meta(($kind->seo_description ? $kind->seo_description : $kind->short_description)); ?>" />
        <?php if (!empty($kind->item->image)): ?>
            <meta property="og:image" content="<?php echo Utils::file_url($kind->item->image); ?>" />
            <meta property="twitter:image" content="<?php echo Utils::file_url($kind->item->image); ?>" />
            <link rel="image_src" href="<?php echo Thumb::generate($kind->item->image, 300, 800, FALSE, 'thumb', TRUE, $noimage); ?>" />
        <?php elseif ($noimage): ?>
            <meta property="og:image" content="<?php echo $noimage; ?>" />
            <meta property="twitter:image" content="<?php echo $noimage; ?>" />
            <link rel="image_src" href="<?php echo $noimage; ?>" />
        <?php endif; ?>
    <?php endif; ?>
<?php elseif (isset($cms_page)): ?>
    <?php if (isset($cms_page['seo_description'])): ?><meta name="description" content="<?php echo Text::meta($cms_page['seo_description']); ?>" /><?php endif; ?>
    <?php if (isset($cms_page['seo_keywords'])): ?><meta name="keywords" content="<?php echo Text::meta($cms_page['seo_keywords']); ?>" /><?php endif; ?>
    <meta property="og:title" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?>"/>
    <meta property="twitter:title" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?>"/>
    <meta property="og:description" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_description')); ?>" />
    <meta property="twitter:description" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_description')); ?>" />
    <?php if (!empty($cms_page['main_image'])): ?>
        <meta property="og:image" content="<?php echo Utils::file_url($cms_page['main_image']); ?>" />
        <meta property="twitter:image" content="<?php echo Utils::file_url($cms_page['main_image']); ?>" />
        <link rel="image_src" href="<?php echo Thumb::generate($cms_page['main_image'], 300, 800, FALSE, 'thumb', TRUE, $noimage, 85); ?>" />
    <?php elseif ($noimage): ?>
        <meta property="og:image" content="<?php echo $noimage; ?>" />
        <meta property="twitter:image" content="<?php echo $noimage; ?>" />
        <link rel="image_src" href="<?php echo $noimage; ?>" />
    <?php endif; ?>
    <?php if (!empty($cms_page['amp_url'])): ?>
        <link rel="amphtml" href="<?php echo $cms_page['amp_url']; ?>" />
    <?php endif; ?>
<?php endif; ?>

<?php if (isset($pagination)): ?>
    <?php if ($pagination->current_page AND ( ($pagination->current_page == 1 AND sizeof($_GET) == 0) OR ( sizeof($_GET) == 1 AND $pagination->current_page > 1))): ?>
        <?php if(Kohana::config('app.seo.head_pagination_links')): ?>
            <?php if ($pagination->previous_page): ?><link rel="prev" href="<?php echo HTML::chars($pagination->url($pagination->previous_page)); ?>"><?php endif; ?>
            <?php if ($pagination->next_page): ?><link rel="next" href="<?php echo HTML::chars($pagination->url($pagination->next_page)); ?>"><?php endif; ?>
        <?php endif ?>

        <?php if (Kohana::config('app.utils.canonical_same_url')): ?>
            <?php if (!empty($category)): ?>
                <link rel="canonical" href="<?php echo ((is_array($category)) ? $category['url'] : $category->get_absolute_url()); ?>" />
            <?php elseif (!empty($kind)): ?>
                <link rel="canonical" href="<?php echo ((is_array($kind)) ? $kind['url'] : $kind->get_absolute_url()); ?>" />
            <?php elseif (isset($cms_page['get_absolute_url']) AND ! $is_search): ?>
                <?php if (!empty($cms_page['seo_canonical_url'])): ?>
                    <link rel="canonical" href="<?php echo $cms_page['seo_canonical_url']; ?>" />
                <?php else: ?>
                    <link rel="canonical" href="<?php echo Arr::get($cms_page, 'get_absolute_url'); ?>" />
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>
    <?php else: ?>
        <?php if (!empty($kind['url']) AND ! empty($info['full_url']) AND $kind['url'] == urldecode($info['full_url'])): ?>
            <?php if (Kohana::config('app.utils.canonical_same_url')): ?>
                <?php if (!empty($category)): ?>
                    <link rel="canonical" href="<?php echo ((is_array($category)) ? $category['url'] : $category->get_absolute_url()); ?>" />
                <?php elseif (!empty($kind)): ?>
                    <link rel="canonical" href="<?php echo ((is_array($kind)) ? $kind['url'] : $kind->get_absolute_url()); ?>" />
                <?php elseif (isset($cms_page['get_absolute_url']) AND ! $is_search): ?>
                    <link rel="canonical" href="<?php echo Arr::get($cms_page, 'get_absolute_url'); ?>" />
                <?php endif; ?>
            <?php endif; ?>
        <?php else: ?>
            <?php if (!empty($category)): ?>
                <link rel="canonical" href="<?php echo ((is_array($category)) ? $category['url'] : $category->get_absolute_url()); ?>" />
            <?php elseif (!empty($kind)): ?>
                <link rel="canonical" href="<?php echo ((is_array($kind)) ? $kind['url'] : $kind->get_absolute_url()); ?>" />
            <?php elseif (isset($cms_page['get_absolute_url']) AND ! $is_search): ?>
                <link rel="canonical" href="<?php echo Arr::get($cms_page, 'get_absolute_url'); ?>" />
            <?php endif; ?>
            <?php if (Kohana::config('app.sitename') !== 'mobis.hr'): ?>
                <meta name="robots" content="noindex, follow" />
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>
<?php else: ?>
    <?php if ((count($_GET) OR Kohana::config('app.utils.canonical_same_url')) AND isset($cms_page['get_absolute_url'])): ?>
        <link rel="canonical" href="<?php echo Arr::get($cms_page, 'get_absolute_url'); ?>" />
    <?php endif; ?>
<?php endif; ?>
<?php if (!empty($info['full_url'])): ?>
    <meta property="og:url" content="<?php echo urldecode($info['full_url']); ?>" />
    <meta property="twitter:url" content="<?php echo urldecode($info['full_url']); ?>" />
<?php endif; ?>
<?php if (!empty($this->info['languages_urls']) AND count(array_filter($this->info['languages_urls'])) == count(Kohana::config('app.languages')) AND count(Kohana::config('app.languages')) > 1): ?>
    <?php $hreflangs = Kohana::config('app.html_lang_href'); ?>
    <?php foreach ($this->info['languages_urls'] AS $lang => $lang_url): ?>
        <?php

        if (in_array($lang, array_keys($hreflangs))) {
            $lang = $hreflangs[$lang];
        }

        if (!$lang_url) {
            continue;
        }

        ?>
        <link rel="alternate" hreflang="<?php echo $lang; ?>" href="<?php echo $lang_url . ((!empty($page) AND $page > 1) ? '?page=' . $page : ''); ?>" />
    <?php endforeach; ?>
<?php endif; ?>
<?php if (!empty($breadcrumb)) : ?>
    <?php $last_breadcrumb = @array_pop(array_keys($breadcrumb)); ?>
    <?php $i = 1; ?>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            <?php foreach ($breadcrumb as $link => $title) : ?>
                {"@type": "ListItem", "position":"<?php echo $i; ?>", "item": {"@id":"<?php echo $link; ?>", "name":"<?php echo $title; ?>"}}<?php if ($link !== $last_breadcrumb) {echo ',';} ?>
                <?php $i++; ?>

            <?php endforeach; ?>
        ]
    }
    </script>
<?php endif; ?>

