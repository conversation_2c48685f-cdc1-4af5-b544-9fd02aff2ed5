<meta property="og:site_name" content="<?php echo Text::meta(Utils::site_name()); ?>"/>
<?php $noimage = Utils::no_image((isset($noimage) ? $noimage : ''), $info['site_url']); ?>
<?php $og_title_exist = false; ?>
<?php if (!empty($cms_page['seo_og_title'])): ?>
    <meta property="og:title" content="<?php echo Text::meta($cms_page['seo_og_title']); ?>"/><?php $og_title_exist = true; ?>
<?php endif; ?>
<?php $og_description_exist = false; ?>
<?php if (!empty($cms_page['seo_og_description'])): ?>
    <meta property="og:description" content="<?php echo Text::meta($cms_page['seo_og_description']); ?>"/><?php $og_description_exist = true; ?>
<?php endif; ?>
<?php $og_image_exist = false; ?>
<?php if (!empty($cms_page['seo_og_image'])): ?>
    <meta property="og:image" content="<?php echo Utils::file_url($cms_page['seo_og_image']); ?>" />
    <?php
    $og_image_exist = true;
    $noimage = Utils::file_url($cms_page['seo_og_image']);
    ?>
<?php endif; ?>
<?php if (isset($cms_page['seo_description'])): ?>
    <meta name="description" content="<?php echo Text::meta($cms_page['seo_description']); ?>" />
<?php endif; ?>
<?php if (isset($cms_page['seo_keywords'])): ?>
    <meta name="keywords" content="<?php echo Text::meta($cms_page['seo_keywords']); ?>" />
<?php endif; ?>
<?php if (!$og_title_exist): ?><meta property="og:title" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?>"/><?php endif; ?>
<meta property="twitter:title" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?>"/>
<?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_description')); ?>" /><?php endif; ?>
<meta property="twitter:description" content="<?php echo Text::meta(Arr::get($cms_page, 'seo_description')); ?>" />
<?php if (!empty($cms_page['main_image'])): ?>
    <?php if (!$og_image_exist): ?><meta property="og:image" content="<?php echo Utils::file_url($cms_page['main_image']); ?>" /><?php endif; ?>
    <meta property="twitter:image" content="<?php echo Utils::file_url($cms_page['main_image']); ?>" />
    <link rel="image_src" href="<?php echo Thumb::generate($cms_page['main_image'], 300, 800, FALSE, 'thumb', TRUE, $noimage, 85); ?>" />
<?php elseif ($noimage): ?>
    <?php if (!$og_image_exist): ?><meta property="og:image" content="<?php echo $noimage; ?>" /><?php endif; ?>
    <meta property="twitter:image" content="<?php echo $noimage; ?>" />
    <link rel="image_src" href="<?php echo $noimage; ?>" />
<?php endif; ?>
<?php if (isset($cms_page) AND is_array($cms_page) AND !empty($cms_page['seo_canonical_url'])): ?>
    <link rel="canonical" href="<?php echo $cms_page['seo_canonical_url']; ?>" />
<?php elseif (count($_GET) OR Kohana::config('app.utils.canonical_same_url')): ?>
    <link rel="canonical" href="<?php echo Arr::get($cms_page, 'get_absolute_url'); ?>" />
<?php endif; ?>
<?php if (isset($cms_page) AND is_array($cms_page) AND !empty($cms_page['amp_url'])): ?>
    <link rel="amphtml" href="<?php echo $cms_page['amp_url']; ?>" />
<?php endif; ?>
<?php if (isset($cms_page) AND is_array($cms_page) AND !empty($cms_page['seo_robots'])): ?>
    <meta name="robots" content="<?php echo str_replace(['no', 'nf'], ['noindex', 'nofollow'], $cms_page['seo_robots'])?>" />
<?php elseif (isset($noindex) AND $noindex): ?>
    <meta name="robots" content="noindex" />
<?php endif; ?>
<?php if (!empty($info['full_url'])): ?>
    <meta property="og:url" content="<?php echo urldecode($info['full_url']); ?>" />
    <meta property="twitter:url" content="<?php echo urldecode($info['full_url']); ?>" />
<?php endif; ?>
<?php if (!empty($this->info['languages_urls']) AND count(array_filter($this->info['languages_urls'])) == count(Kohana::config('app.languages')) AND count(Kohana::config('app.languages')) > 1): ?>
  <?php $hreflangs = Kohana::config('app.html_lang_href'); ?>
    <?php foreach ($this->info['languages_urls'] AS $lang => $lang_url): ?>
        <?php

        if (in_array($lang, array_keys($hreflangs))) {
            $lang = $hreflangs[$lang];
        }

        if (!$lang_url) {
            continue;
        }

        ?>
        <link rel="alternate" hreflang="<?php echo $lang; ?>" href="<?php echo $lang_url; ?>" />
    <?php endforeach; ?>
<?php endif; ?>
<?php if (!empty($breadcrumb)) : ?>
    <?php $last_breadcrumb = @array_pop(array_keys($breadcrumb)); ?>
    <?php $i = 1; ?>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
            <?php foreach ($breadcrumb as $link => $title) : ?>
                {"@type": "ListItem", "position":"<?php echo $i; ?>", "item": {"@id":"<?php echo $link; ?>", "name":"<?php echo $title; ?>"}}<?php if ($link !== $last_breadcrumb) {echo ',';} ?>
                <?php $i++; ?>

            <?php endforeach; ?>
        ]
    }
    </script>
<?php endif; ?>