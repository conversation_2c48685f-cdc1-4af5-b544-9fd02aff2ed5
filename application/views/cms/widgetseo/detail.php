<meta property="og:site_name" content="<?php echo Text::meta(Utils::site_name()); ?>"/>
<?php $noimage = Utils::no_image((isset($noimage) ? $noimage : ''), $info['site_url']); ?>
<?php if (is_array($item)): ?>
    <?php $og_title_exist = false; ?>
    <?php if (!empty($item['seo_og_title'])): ?>
        <meta property="og:title" content="<?php echo Text::meta($item['seo_og_title']); ?>"/>
        <?php $og_title_exist = true; ?>
    <?php endif; ?>
    <?php $og_description_exist = false; ?>
    <?php if (!empty($item['seo_og_description'])): ?>
        <meta property="og:description" content="<?php echo Text::meta($item['seo_og_description']); ?>"/>
        <?php $og_description_exist = true; ?>
    <?php endif; ?>
    <?php $og_image_exist = false; ?>
    <?php if (!empty($item['seo_og_image'])): ?>
        <meta property="og:image" content="<?php echo Utils::file_url($item['seo_og_image']); ?>" />
        <?php
        $og_image_exist = true;
        $noimage = Utils::file_url($item['seo_og_image']);
        ?>
    <?php endif; ?>
    <?php $custom_text = $item['title'] . " iz kategorije " . $item['category_title'] . " u webshopu Centar Tehnike. Velik izbor ✓ Plaćanje pouzećem ✓ Plaćanje do 36 rata ✓"; ?>
    <?php if (!empty($item['seo_description'])): ?>
        <meta name="description" content="<?php echo Text::meta($item['seo_description'], 0, 325); ?>" />
        <?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta($item['seo_description'], 0, 325); ?>" /><?php endif; ?>
        <meta property="twitter:description" content="<?php echo Text::meta($item['seo_description'], 0, 325); ?>" />
    <?php elseif (!empty($custom_text)) : ?>
        <meta name="description" content="<?php echo Text::meta($custom_text, 0, 325); ?>"/>
        <?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta($custom_text, 0, 325); ?>" /><?php endif; ?>
        <meta property="twitter:description" content="<?php echo Text::meta($custom_text, 0, 325); ?>"/>
    <?php elseif (!empty($item['short_description'])): ?>
        <meta name="description" content="<?php echo Text::meta($item['short_description'], 0, 325); ?>" />
            <?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta($item['short_description'], 0, 325); ?>" /><?php endif; ?>
        <meta property="twitter:description" content="<?php echo Text::meta($item['short_description'], 0, 325); ?>" />
    <?php elseif (!empty($item['content'])): ?>
        <meta name="description" content="<?php echo Text::meta($item['content'], 0, 325); ?>" />
       <?php if (!$og_description_exist): ?><meta property="og:description" content="<?php echo Text::meta($item['content'], 0, 325); ?>" /><?php endif; ?>
        <meta property="twitter:description" content="<?php echo Text::meta($item['content'], 0, 325); ?>" />
    <?php endif; ?>
    <?php if (!empty($item['seo_keywords'])): ?><meta name="keywords" content="<?php echo Text::meta($item['seo_keywords']); ?>" /><?php endif; ?>
    <?php if (!empty($item['seo_h1'])): ?>
        <?php if (!$og_title_exist): ?><meta property="og:title" content="<?php echo (Kohana::config('app.cms.social_use_seo_title')) ? Text::meta($item['seo_title']) : Text::meta($item['seo_h1']); ?>"/><?php endif; ?>
        <meta property="twitter:title" content="<?php echo (Kohana::config('app.cms.social_use_seo_title')) ? Text::meta($item['seo_title']) : Text::meta($item['seo_h1']); ?>"/>
    <?php endif; ?>
    <?php if (!empty($item['main_image'])): ?>
        <?php if (!$og_image_exist): ?><meta property="og:image" content="<?php echo Utils::file_url($item['main_image']); ?>" /><?php endif; ?>
        <meta property="twitter:image" content="<?php echo Utils::file_url($item['main_image']); ?>" />
        <link rel="image_src" href="<?php echo Thumb::generate($item['main_image'], 300, 800, FALSE, 'thumb', TRUE, $noimage, 85); ?>" />
    <?php elseif ($noimage): ?>
        <?php if (!$og_image_exist): ?><meta property="og:image" content="<?php echo $noimage; ?>" /><?php endif; ?>
        <meta property="twitter:image" content="<?php echo $noimage; ?>" />
        <link rel="image_src" href="<?php echo $noimage; ?>" />
    <?php endif; ?>
    <?php if (!empty($item['price'])): ?>
        <meta property="og:type" content="product" />
    <?php endif;?>
    <?php if (!empty($item['datetime_published'])): ?>
        <meta property="og:type" content="article" />
        <meta name="twitter:card" value="summary" />
        <meta property="article:published_time" itemprop="datePublished" content="<?php echo date('Y-m-d', $item['datetime_published']); ?>" />
        <?php if (!empty($item['category_title'])): ?>
            <meta property="article:section" itemprop="articleSection" content="<?php echo Text::meta($item['category_title']); ?>" />
        <?php endif; ?>
        <?php if (!empty($item['seo_keywords_tags']) AND is_array($item['seo_keywords_tags'])): ?>
            <?php foreach ($item['seo_keywords_tags'] AS $seo_keywords_tag): ?>
                <meta property="article:tag" content="<?php echo Text::meta(Arr::get($seo_keywords_tag, 'title')); ?>" />
            <?php endforeach; ?>
        <?php endif; ?>
    <?php endif; ?>
    <?php if (!empty($item['seo_canonical_url'])): ?>
        <link rel="canonical" href="<?php echo $item['seo_canonical_url']; ?>" />
    <?php elseif ((count($_GET) OR Kohana::config('app.utils.canonical_same_url')) AND isset($item['url'])): ?>
        <?php if (strpos($item['url'], '?') !== false): ?>
            <link rel="canonical" href="<?php echo substr($item['url'], 0, strpos($item['url'], '?')); ?>" />
        <?php else: ?>
            <link rel="canonical" href="<?php echo $item['url']; ?>" />
        <?php endif; ?>
    <?php endif; ?>
    <?php if (!empty($item['amp_url'])): ?>
        <link rel="amphtml" href="<?php echo $item['amp_url']; ?>" />
    <?php endif; ?>
<?php else: ?>
    <?php if ($item->seo_description): ?><meta name="description" content="<?php echo Text::meta($item->seo_description); ?>" /><?php endif; ?>
    <?php if ($item->seo_keywords): ?><meta name="keywords" content="<?php echo Text::meta($item->seo_keywords); ?>" /><?php endif; ?>
    <meta property="og:title" content="<?php echo Text::meta($item->seo_h1); ?>"/>
    <meta property="twitter:title" content="<?php echo Text::meta($item->seo_h1); ?>"/>
    <meta property="og:description" content="<?php echo Text::meta(($item->seo_description ? $item->seo_description : $item->short_description)); ?>" />
    <meta property="twitter:description" content="<?php echo Text::meta(($item->seo_description ? $item->seo_description : $item->short_description)); ?>" />
    <?php if (!empty($item->item->main_image)): ?>
        <meta property="og:image" content="<?php echo Utils::file_url($item->item->main_image); ?>" />
        <meta property="twitter:image" content="<?php echo Utils::file_url($item->item->main_image); ?>" />
        <link rel="image_src" href="<?php echo Thumb::generate($item->item->main_image, 300, 800, FALSE, 'thumb', TRUE, $noimage); ?>" />
    <?php elseif ($noimage): ?>
        <meta property="og:image" content="<?php echo $noimage; ?>" />
        <meta property="twitter:image" content="<?php echo $noimage; ?>" />
        <link rel="image_src" href="<?php echo $noimage; ?>" />
    <?php endif; ?>
    <?php if (count($_GET)): ?><link rel="canonical" href="<?php echo $item->get_absolute_url(); ?>" /><?php endif; ?>
<?php endif; ?>
<?php if (isset($item) AND is_array($item) AND !empty($item['seo_robots'])): ?>
    <meta name="robots" content="<?php echo str_replace(['no', 'nf'], ['noindex', 'nofollow'], $item['seo_robots'])?>" />
<?php elseif (isset($noindex) AND $noindex): ?>
    <meta name="robots" content="noindex" />
<?php endif; ?>
<?php if (!empty($info['url'])): ?>
    <meta property="og:url" content="<?php echo $info['url']; ?>" />
    <meta property="twitter:url" content="<?php echo $info['url']; ?>" />
<?php elseif (!empty($info['full_url'])): ?>
    <meta property="og:url" content="<?php echo urldecode($info['full_url']); ?>" />
    <meta property="twitter:url" content="<?php echo urldecode($info['full_url']); ?>" />
<?php endif; ?>
<?php if (!empty($this->info['languages_urls']) AND count(array_filter($this->info['languages_urls'])) == count(Kohana::config('app.languages')) AND count(Kohana::config('app.languages')) > 1): ?>
    <?php $hreflangs = Kohana::config('app.html_lang_href'); ?>
    <?php foreach ($this->info['languages_urls'] AS $lang => $lang_url): ?>
        <?php

        if (in_array($lang, array_keys($hreflangs))) {
            $lang = $hreflangs[$lang];
        }

        if (!$lang_url) {
            continue;
        }

        ?>
        <link rel="alternate" hreflang="<?php echo $lang; ?>" href="<?php echo $lang_url; ?>" />
    <?php endforeach; ?>
<?php endif; ?>
<?php if (!empty($schema_org)): ?>
    <?php if (!empty($info['controller']) AND !empty($info['action']) AND $info['controller'] == 'catalog' AND $info['action'] == 'detail'): ?>
        <script type="application/ld+json">{
            "@context": "https://schema.org/",
            "@type": "Product",
            "name": "<?php echo Text::meta(Arr::get($item, 'title')); ?>",
            "sku": "<?php echo Text::meta(Arr::get($item, 'code')); ?>",
            <?php if (!empty($item['main_image'])): ?>
            "image": "<?php echo Utils::file_url($item['main_image']); ?>",
            <?php endif; ?>
            "description": "<?php echo Text::meta(Arr::get($item, 'title')); ?> iz webshopa Centar Tehnike po povoljnoj cijeni od <?php echo Arr::get($item, 'price_custom'); ?> <?php echo Arr::get($currency, 'code'); ?>",
            "offers": {
                "@type": "Offer",
                "url": "<?php echo Arr::get($item, 'url'); ?>",
                "priceCurrency": "<?php echo Arr::get($currency, 'code'); ?>",
                "price": "<?php echo Arr::get($item, 'price_custom'); ?>",
                "availability": "<?php if (!empty($item['is_available'])): ?>InStock<?php else: ?>OutOfStock<?php endif; ?>"
            }
            <?php if (!empty($use_product_type) AND !empty($item['breadcrumbs'])): ?>
                <?php $product_types = array_slice($item['breadcrumbs'], 0, -1); ?>
                , "product_type": "<?php echo implode(' > ', array_values($product_types)); ?>"
                , "category": "<?php echo implode(' > ', array_values($product_types)); ?>"
                
            <?php endif; ?>

            <?php if (!empty($use_attribute) AND is_array($use_attribute) AND !empty($item['attributes'])): ?>
                <?php
                $item_attributes = [];
                foreach($item['attributes'] as $attribute) {
                    if (in_array($attribute['attribute_code'], array_keys($use_attribute))) {
                        if (!empty($item_attributes[$use_attribute[$attribute['attribute_code']]])) {
                            $item_attributes[$use_attribute[$attribute['attribute_code']]] .= ', ' . $attribute['title'];
                        } else {
                            $item_attributes[$use_attribute[$attribute['attribute_code']]] = $attribute['title'];
                        }
                    }
                }
                ?>
                <?php foreach($item_attributes as $item_attribute_code => $item_attribute_value): ?>
                    ,"<?php echo $item_attribute_code; ?>": "<?php echo Text::meta($item_attribute_value); ?>"
                <?php endforeach; ?>
            <?php endif; ?>
            <?php if(!empty($use_price)): ?>
                <?php if(!empty($item['basic_price_custom'])): ?>
                    ,"price" : "<?php echo $item['basic_price_custom'] .' '. Arr::get($currency, 'code'); ?>"
                <?php endif; ?>
                <?php if(!empty($item['basic_price_custom']) AND !empty($item['price_custom']) AND $item['basic_price_custom'] > $item['price_custom']): ?>
                    ,"sale_price" : "<?php echo $item['price_custom'] .' '. Arr::get($currency, 'code'); ?>"
                <?php endif; ?>
            <?php endif;?>
        }</script>
    <?php elseif (!empty($info['controller']) AND !empty($info['action']) AND $info['controller'] == 'publish' AND $info['action'] == 'detail'): ?>
        <script type="application/ld+json">
        {
            "@context": "https://schema.org/",
            "@type": "NewsArticle",
            "mainEntityOfPage": {
                "@type": "WebPage",
                "@id": "<?php echo $item['url']; ?>"
            },

            "headline": "<?php echo Text::meta(Arr::get($item, 'title')); ?>",

            <?php if (!empty($item['main_image'])): ?>
            "image": ["<?php echo Utils::file_url($item['main_image']); ?>"],
            <?php endif; ?>

            <?php if (!empty($item['seo_description'])): ?>
            "description": "<?php echo Text::meta($item['seo_description'], 0, 325); ?>",
            <?php elseif (!empty($item['short_description'])): ?>
            "description": "<?php echo Text::meta($item['short_description'], 0, 325); ?>",
            <?php elseif (!empty($item['content'])): ?>
            "description": "<?php echo Text::meta($item['content'], 0, 325); ?>",
            <?php endif; ?>

            "datePublished": "<?php echo date('Y-m-d\TH:i:sP', $item['datetime_published']); ?>",
            "dateModified": "<?php echo date('Y-m-d\TH:i:sP', $item['datetime_updated']); ?>",

            <?php if (!empty($item['author'])): ?>
            "author": {
                "@type": "Person",
                "name": "<?php echo Text::meta(Arr::get($item, 'author')); ?>"
            },
            <?php elseif (!empty(Kohana::config('app.sitename_detail.organization'))): ?>
            "author": {
                "@type": "Organization",
                "name": "<?php echo Text::meta(Kohana::config('app.sitename_detail.organization')); ?>"
            },
            <?php endif; ?>
            <?php if (!empty($item['feedback_comment_widget']['items'])): ?>
            "review": {
                "@type": "Review",
                <?php if (!empty($item['feedback_rate_widget']['rates'])): ?>
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": "<?php echo floor($item['feedback_rate_widget']['rates']); ?>",
                    "bestRating": "<?php echo ceil($item['feedback_rate_widget']['rates']); ?>"
                },
                <?php endif; ?>
                "author": [
                    <?php $comment_total = count($item['feedback_comment_widget']['items']); ?>
                    <?php foreach ($item['feedback_comment_widget']['items'] AS $comment_item): ?>
                        {"@type": "Person", "name": "<?php echo Text::meta(Arr::get($comment_item, 'display_name')); ?>"}<?php if ($comment_total > 1): ?>,<?php endif; ?>
                        <?php $comment_total--; ?>
                    <?php endforeach; ?>
                ]
            },
            <?php endif; ?>
            <?php if (!empty($item['feedback_rate_widget']['rates_votes'])): ?>
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "<?php echo $item['feedback_rate_widget']['rates']; ?>",
                "reviewCount": "<?php echo $item['feedback_rate_widget']['rates_votes']; ?>"
            },
            <?php endif; ?>
            "publisher": {
                "@type": "Organization",
                "name": "<?php echo Kohana::config('app.sitename_detail.organization'); ?>",
                "logo": {
                  "@type": "ImageObject",
                  "url": "<?php echo $info['site_url']; ?>/media/images/logo.png"
                }
            }
        }
        </script>
    <?php endif; ?>

    <?php if (!empty($breadcrumb)) : ?>
        <?php $last_breadcrumb = @array_pop(array_keys($breadcrumb)); ?>
        <?php $i = 1; ?>
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": [
                <?php foreach ($breadcrumb as $link => $title) : ?>
                    {"@type": "ListItem", "position":"<?php echo $i; ?>", "item": {"@id":"<?php echo $link; ?>", "name":"<?php echo $title; ?>"}}<?php if ($link !== $last_breadcrumb) {echo ',';} ?>
                    <?php $i++; ?>

                <?php endforeach; ?>
            ]
        }
        </script>
    <?php endif; ?>
<?php endif; ?>