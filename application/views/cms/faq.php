<?php $this->extend('cms/default'); ?>
<?php $this->block('page_class'); ?> page-faq<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php $rotator_items = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'faq', 'limit' => 0]); ?>
	<?php $last_header_title = ''; ?>
	<?php $i = 1; ?>
	<?php if($rotator_items): ?>
		<?php foreach($rotator_items as $item): ?>
			<?php if(!empty($item['title2']) AND $last_header_title != $item['title2']): ?>	
				<h2 class="fp-group-title fp-group-title<?php echo $i; ?>"><?php echo $item['title2']; ?></h2>
			<?php endif; ?>
			<article class="fp">
				<h2 class="fp-title"><span><?php echo $item['title']; ?></span></h2>
				<div class="fp-cnt"><?php echo $item['content']; ?></div>
			</article>
			<?php $last_header_title = $item['title2']; ?>
			<?php $i++; ?>
		<?php endforeach; ?>
	<?php endif; ?>

	<?php echo View::factory('cms/widget/share', ['item' => isset($cms_page) ? $cms_page : []]); ?>
<?php $this->endblock('content'); ?>