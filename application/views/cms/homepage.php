<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('main_layout'); ?>
	<?php $hero_slider = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'homepage', 'limit' => 1, 'cached' => false]); ?>
	<?php if(!empty($hero_slider)): ?>
		<div class="hp-header">
			<div class="hero-slider-container">
				<div class="hp-header-label hp-header-label1"><?php echo Arr::get($cmslabel, 'rotator_text_left'); ?></div>
				<div class="hp-header-label hp-header-label2"><?php echo Arr::get($cmslabel, 'rotator_text_right'); ?></div>
				<div class="hero-slider">
					<?php $i = 1; ?>
					<?php foreach ($hero_slider as $promo_item): ?>
						<?php 
							//Slike
							$promo_item_image_d = '';
							$promo_item_image = '';
							if(!empty($promo_item['image'])){
								$promo_item_image_d = Utils::file_url($promo_item['image']);
							}

							if(!empty($promo_item['image_2'])){
								$promo_item_image = Utils::file_url($promo_item['image_2']);
							}

							//Pozadina
							$background = $promo_item['element_background'];

							//Poravnanje teksta
							$position = $promo_item['element_position'];

							//gumba
							$link = $promo_item['link'];
							$link_text = $promo_item['element_button'];
						?>

						<?php if($link): ?>
							<a class="c-promo<?php if($background == 'light'): ?> c-promo-light<?php endif; ?><?php if($position == 'position_center'): ?> c-promo-center<?php endif; ?> c-promo-<?php echo $promo_item['id'] ?>" href="<?php echo $link ?>">
						<?php else: ?>
							<div class="c-promo<?php if($background == 'light'): ?> c-promo-light<?php endif; ?><?php if($position == 'position_center'): ?> c-promo-center<?php endif; ?> c-promo-<?php echo $promo_item['id'] ?>">
						<?php endif; ?>
							<div class="wrapper-hp-promo">
								<style>
									<?php if(!empty($promo_item_image_d) OR !empty($promo_item_image)): ?>
										.c-promo-<?php echo $promo_item['id'] ?>{background: url(<?php echo $promo_item_image_d ?>) center center no-repeat; }
										<?php if(!empty($promo_item_image)): ?>
											@media screen and (max-width: 750px) {
												.c-promo-<?php echo $promo_item['id'] ?>{background: url(<?php echo $promo_item_image ?>) center center no-repeat; }	
											}
										<?php endif; ?>
									<?php endif; ?>
								</style>

								<div class="c-promo-container">
									<?php if(!empty($promo_item['title2'])): ?>
										<div class="c-promo-headline">
											<?php echo $promo_item['title2']; ?>
										</div>
									<?php endif; ?>
									
									<?php if(!empty($promo_item['title'])): ?>
										<div class="c-promo-title">
											<?php echo $promo_item['title'] ?>
										</div>
									<?php endif; ?>
									<?php if(!empty($promo_item['content'])): ?>
										<div class="c-promo-content">
											<?php echo $promo_item['content'] ?>
										</div>
									<?php endif; ?>
									<?php if(!empty($link_text)): ?>
										<div class="c-promo-btns">
											<span class="btn<?php if($background == 'dark'): ?> btn-yellow<?php endif; ?> c-promo-btn"><?php echo $link_text; ?></span>
										</div>
									<?php endif; ?>
								</div>
							</div>
						<?php if($link): ?></a><?php else: ?></div><?php endif; ?>
						<?php $i++; ?>
					<?php endforeach; ?>
				</div>
			</div>
		</div>
	<?php endif; ?>

	<?php $categories_homepage = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'popular_categories', 'limit' => 1, 'extra_fields' => ['catalogcategory'])); ?>
	<?php if(!empty($categories_homepage)): ?>
		<div class="popular-categories-section">
			<div class="wrapper">
				<div class="popular-categories-cnt">
					<div class="pop-hp-categories-title"><?php echo Arr::get($cmslabel, 'hp_categories_title'); ?></div>
					<div class="pop-hp-categories">
						<?php foreach($categories_homepage as $hp_cat): ?>
							<?php if (Arr::get($hp_cat, 'catalogcategory_ids')): ?>
								<?php $categories = Widget_Catalog::categories(array('lang' => $info['lang'], 'limit' => 12, 'id' => Arr::get($hp_cat, 'catalogcategory_ids'))); ?>
								<?php foreach ($categories as $category): ?>
									<a class="cat-item hp-cat-item" href="<?php echo $category['url']; ?>">
										<img loading="lazy" <?php echo Thumb::generate($category['main_image'], array('width' => 144, 'height' => 144, 'default_image' => '/media/images/no-image-144.jpg', 'html_tag' => TRUE, 'srcset' => '144r 2x')); ?> alt="<?php echo Text::meta($category['title']); ?>" />
										<span><?php echo $category['title']; ?></span>
									</a>
								<?php endforeach; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				</div>
			</div>
		</div>
	<?php endif; ?>

	<?php $hp_promos_light = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'hp_promos_light', 'limit' => 24)); ?>
	<?php if(!empty($hp_promos_light)): ?>
		<div class="hp-promos-cnt">
			<div class="wrapper">
				<?php echo View::factory('cms/widget/hp_promos', ['items' => $hp_promos_light]); ?>
			</div>
		</div>
	<?php endif; ?>

	<div class="hp-brands">
		<div class="wrapper">
			<?php echo View::factory('catalog/widget/brands', ['mode' => 'hp_brands']); ?>
		</div>
	</div>

	<div class="section-gray section-promo">
		<div class="wrapper">
			<?php $benefit_items = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'benefits', 'limit' => 4)); ?>
			<?php if ($benefit_items): ?>			
				<div class="benefits">
					<?php foreach ($benefit_items as $benefit_item): ?>
						<div class="benefit-item">
							<a class="benefit<?php if(!empty($benefit_item['link2'])): ?> <?php echo $benefit_item['link2']; ?><?php endif; ?>" href="<?php echo $benefit_item['link']; ?>"><?php echo $benefit_item['title']; ?><?php if(!empty($benefit_item['title2'])): ?><span><?php echo $benefit_item['title2']; ?></span><?php endif; ?></a>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>

			<?php $hp_promos_dark = Widget_Rotator::elements(array('lang' => $info['lang'], 'category_code' => 'hp_promos_dark', 'limit' => 8)); ?>
			<?php if(!empty($hp_promos_dark)): ?>
				<div class="hp-promos-cnt hp-promos-cnt-dark">
					<?php echo View::factory('cms/widget/hp_promos', ['items' => $hp_promos_dark, 'mode' => 'hp-promos-dark']); ?>
				</div>
			<?php endif; ?>

			<div class="panel-row">
				<div class="panel panel1">
					<div class="panel-title"><?php echo Arr::get($cmslabel, 'hp_panel1_title'); ?></div>
					<div class="display-f panel-cols">
						<?php echo Arr::get($cmslabel, 'hp_panel1_content'); ?>
					</div>
					<div class="panel-footer">
						<div class="panel-footer-cnt">
							<?php echo Arr::get($cmslabel, 'hp_panel1_footer'); ?>
						</div>
					</div>
				</div>

				<div class="panel panel2">
					<div class="panel-title"><?php echo Arr::get($cmslabel, 'hp_panel2_title'); ?></div>
					<div class="display-f panel-cols">
						<?php echo Arr::get($cmslabel, 'hp_panel2_content'); ?>
					</div>
					<div class="panel-footer">
						<div class="panel-footer-cnt">
							<?php echo Arr::get($cmslabel, 'hp_panel2_footer'); ?>
						</div>
					</div>					
				</div>

				<div class="panel panel3">
					<div class="panel-title"><?php echo Arr::get($cmslabel, 'hp_panel3_title'); ?></strong></div>
					<div class="display-f panel-cols">
						<?php echo Arr::get($cmslabel, 'hp_panel3_content'); ?>
					</div>
					<div class="panel-footer">
						<div class="panel-footer-cnt">
							<?php echo Arr::get($cmslabel, 'hp_panel3_footer'); ?>
						</div>
					</div>						
				</div>
			</div>

			<?php if($info['user_device'] == 'm'): ?>
				<div class="hp-categories-cnt">
					<div class="hp-categories-title"><?php echo Arr::get($cmslabel, 'hp_categories_offer'); ?></div>
					<?php echo View::factory('catalog/widget/categories', ['class' => 'hp-categories', 'hpcat' => 1]); ?>
				</div>
			<?php endif; ?>

			<div class="display-f locations">
				<div class="flex-col locations-col1">
					<div class="panel-support">
						<div class="panel-support-header">
							<?php echo Arr::get($cmslabel, 'support_panel_title'); ?>
						</div>
						<div class="panel-support-footer">
							<?php echo Arr::get($cmslabel, 'support_panel_content'); ?>
						</div>
					</div>
				</div>
				<div class="flex-col locations-col2">
					<div class="panel-stores">
						<div class="panel-stores-title"><?php echo Arr::get($cmslabel, 'locations_panel_title'); ?></div>
						<?php echo View::factory('location/widget/locations', ['class' => 'nav-footer nav-stores']); ?>
					</div>
				</div>
			</div>

			<div class="panel-miele">
				<div class="logo-miele"></div>
				<?php echo Arr::get($cmslabel, 'miele_image'); ?>
				<div class="display-f panel-miele-cnt">
					<?php $miele_col1_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'miele_col1')); ?>
					<?php if($miele_col1_menu): ?>
						<div class="flex-col flex-col1">
							<?php foreach($miele_col1_menu as $miele_col1_menu_item): ?>
								<p<?php if($miele_col1_menu_item['style']): ?> class="<?php echo $miele_col1_menu_item['style']; ?>"<?php endif; ?>>
									<?php if(trim($miele_col1_menu_item['url'])): ?><a href="<?php echo $miele_col1_menu_item['url']; ?>"><?php endif; ?>
										<?php echo $miele_col1_menu_item['title']; ?>
									<?php if(trim($miele_col1_menu_item['url'])): ?></a><?php endif; ?>
								</p>
							<?php endforeach; ?>
						</div>
					<?php endif; ?>
					
					<?php $miele_col2_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'miele_col2')); ?>
					<?php if($miele_col2_menu): ?>
						<div class="flex-col">
							<ul>
								<?php foreach($miele_col2_menu as $miele_col2_menu_item): ?>
									<li><a href="<?php echo $miele_col2_menu_item['url']; ?>"<?php if($miele_col2_menu_item['target_blank']): ?> target="_blank"<?php endif; ?>><span><?php echo $miele_col2_menu_item['title']; ?></span></a></li>
								<?php endforeach; ?>
							</ul>
						</div>
					<?php endif; ?>

					<?php $miele_col3_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'miele_col3')); ?>
					<?php if($miele_col3_menu): ?>
						<div class="flex-col">
							<ul>
								<?php foreach($miele_col3_menu as $miele_col3_menu_item): ?>
									<li><a href="<?php echo $miele_col3_menu_item['url']; ?>"<?php if($miele_col3_menu_item['target_blank']): ?> target="_blank"<?php endif; ?>><span><?php echo $miele_col3_menu_item['title']; ?></span></a></li>
								<?php endforeach; ?>
							</ul>
						</div>
					<?php endif; ?>
				</div>
			</div>
		</div>
	</div>

	<div class="cw">
		<div class="wrapper wrapper-promo">
			<div class="cw-title"><?php echo Arr::get($cmslabel, 'special_products'); ?></div>
			<?php echo View::factory('cms/widget/promo', ['page_id' => $cms_page['id']]); ?>
		</div>
		
		<?php $special_list = Widget_Catalog::speciallist($info['lang'], 'best_buy', true); ?>
		<?php $special_items = Widget_Catalog::products(array('lang' => $info['lang'], 'list_code' => $special_list['code'], 'sort' => 'list_position', 'limit' => 15)); ?>
		<?php if(!empty($special_items)): ?>
			<div class="wrapper cw-items-wrapper">
				<div class="cw-items">
					<div class="slick-carousel cw-slider">
						<?php echo View::factory('catalog/index_entry', ['items' => $special_items]); ?>
					</div>
					<div class="cw-btns">
						<a class="btn btn-yellow btn-cw-all" href="<?php echo $special_list['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>

	<?php $blog = Widget_Publish::category($info['lang'], 'blog'); ?>
	<?php if($blog): ?>
		<?php $blog_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_code' => $blog['code'], 'limit' => 6, 'extra_fields' => ['short_description']]); ?>
		<div class="pw pw-hp">
			<div class="wrapper">
				<div class="pw-title"><a href="<?php echo $blog['url']; ?>"><?php echo Arr::get($cmslabel, 'special_articles'); ?></a></div>
				<div class="p-items hp-p-items">
					<?php echo View::factory('publish/index_entry', ['items' => $blog_items, 'featured' => 1]); ?>
				</div>
				<div class="pw-btns"><a class="btn btn-yellow btn-all" href="<?php echo $blog['url']; ?>"><?php echo Arr::get($cmslabel, 'all_articles'); ?></a></div>
			</div>
		</div>
	<?php endif; ?>
<?php $this->endblock('main_layout'); ?>