<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('page_class'); ?> page-contact<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<div class="contact-row">
		<div class="contact-col contact-col1">
			<?php echo Arr::get($cms_page, 'content'); ?>
		</div>

		<div class="contact-col contact-col2">
			<?php echo Arr::get($cms_page, 'element_extra'); ?>
			<div class="social-footer">
				<?php echo Arr::get($cmslabel, 'social'); ?>
			</div>

			<?php $map_points = Widget_Location::points(array('lang' => $info['lang'])); ?>
			<?php if($map_points): ?>
				<h3 class="locations-title"><?php echo Arr::get($cmslabel, 'store_locations'); ?></h3>
				<?php echo View::factory('location/widget/locations', ['class' => 'nav-locations nav-locations-contact', 'locations' => $map_points]); ?>
			<?php endif; ?>

			<?php echo Arr::get($cmslabel, 'contact_locations_link'); ?>
		</div>
	</div>
<?php $this->endblock('content'); ?>

<?php $this->block('after_main'); ?>
	<?php if ($map_points): ?>
		<div id="mapContainer" class="map-contact">
			<div id="map_canvas" style="width:100%;height:100%;"></div>
		</div>
		<?php foreach ($map_points AS $point): ?>
			<div style="display: none;"
				data-gmap_object_id="<?php echo $point['id']; ?>"
				data-gmap_object_lat="<?php echo $point['gmap']['lat']; ?>"
				data-gmap_object_lon="<?php echo $point['gmap']['lon']; ?>"
				data-gmap_object_tooltip="_content"
				data-id="<?php echo Text::meta($point['id']); ?>">
					<span class="infoBox-cnt">
						<?php if($point['main_image']): ?><span class="image"><img <?php echo Thumb::generate($point['main_image'], array('width' => 250, 'height' => 135, 'crop' => true, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" width="270" height="135" /></span><?php endif; ?>
						<span class="infoBox-body">
							<span class="title"><?php echo $point['title']; ?></span>
							<span class="address"><?php echo nl2br($point['address']); ?></span>
							<span class="contact"><?php echo nl2br($point['contact']); ?></span>
							<?php if(!empty($point['business_hour'])): ?>
							<span class="business-hour"><div class="working-hours"><strong><?php echo Arr::get($cmslabel, 'working_hours'); ?>:</strong></div><?php echo nl2br($point['business_hour']); ?></span>
							<?php endif; ?>
						</span>
					</span>
			</div>
		<?php endforeach; ?>
	<?php endif; ?>	
<?php $this->endblock('after_main'); ?>

<?php $this->block('sidebar_help'); ?> <?php $this->endblock('sidebar_help'); ?>

<?php $this->block('extrabody'); ?>
	<?php echo Html::media('cmslocation,infobox', 'js'); ?>
<?php $this->endblock('extrabody'); ?>