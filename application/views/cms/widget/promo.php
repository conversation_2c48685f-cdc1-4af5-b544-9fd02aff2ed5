<?php $class = (isset($class)) ? ' '.$class : '' ?>
<?php $code = (isset($code)) ? $code : 'promo' ?>
<?php $config = ['lang' => $info['lang'], 'category_code' => $code, 'limit' => 0]; ?>

<?php if(isset($category_id) AND $category_id): ?>
	<?php $config = array_merge($config, ['catalogcategory_id' => $category_id]); ?>
<?php elseif(isset($publish_id) AND $publish_id): ?>
	<?php $config = array_merge($config, ['publish_id' => $publish_id]); ?>
<?php elseif(isset($page_id) AND $page_id): ?>
	<?php $config = array_merge($config, ['page_id' => $page_id]); ?>
<?php endif; ?>

<?php $items = Widget_Rotator::elements($config); ?>

<?php if($items): ?>
	<div class="promo display-f<?php echo $class; ?>">
		<?php $i = 1; ?>
		<?php foreach ($items as $item): ?>
			<div class="promo-col promo-col-<?php echo $item['template']; ?> promo-col<?php echo $i; ?>">
				<?php if($item['link']): ?><a href="<?php echo $item['link']; ?>"><?php endif; ?>
				<?php $img_w = ($item['template'] == 'promo-half') ? 675 : 435; ?>
				<?php $img_h = ($item['template'] == 'promo-half') ? 320 : 300; ?>
				<picture>
					<?php if(!empty($item['image_2'])): ?>
						<source srcset="<?php echo Thumb::generate($item['image_2'], 640, 480, FALSE, 'thumb', TRUE, '/media/images/no-image-300.jpg'); ?>" media="(max-width: 750px)">
					<?php endif; ?>
					<img <?php echo Thumb::generate($item['image'], array('width' => $img_w, 'height' => $img_h, 'crop' => true, 'default_image' => '/media/images/no-image-300.jpg', 'html_tag' => TRUE)); ?> title="" alt="" />
				</picture>
				<?php if($item['link']): ?></a><?php endif; ?>
			</div>
			<?php $i++; ?>
		<?php endforeach; ?>
	</div>
<?php endif; ?>