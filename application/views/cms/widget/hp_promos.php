<?php $mode = (isset($mode))? $mode : '';  ?>

<div class="c-pr-cnt<?php if($mode == 'hp-promos-dark'): ?> c-pr-cnt-dark<?php endif; ?>">
	<?php $p = 1; ?>
	<?php $cur_template = ''; ?>
	<?php foreach ($items as $item): ?>
		<?php if ($p == 1): ?>
			<div class="c-pr-row c-pr-row1 <?php if ($item['template'] == 'promo-one_in_row'): ?> tmp-row1<?php elseif ($item['template'] == 'promo-three_in_row'): ?> tmp-row3<?php else: ?> tmp-row2<?php endif ?>">
		<?php endif ?>

		<?php if ($p > 1 AND $cur_template != $item['template']): ?>
			</div><div class="c-pr-row c-pr-row-2 <?php if ($item['template'] == 'promo-one_in_row'): ?> tmp-row1<?php elseif ($item['template'] == 'promo-three_in_row'): ?> tmp-row3<?php else: ?> tmp-row2<?php endif ?>">
		<?php endif ?>
				<div class="c-pr-item<?php if ($item['template'] == 'promo-one_in_row'): ?> c-pr-item-one<?php elseif($item['template'] == 'promo-three_in_row'): ?> c-pr-item-three<?php else: ?><?php if($item['element_size'] == 'size_bigger'): ?> c-pr-item-two-bigger<?php else: ?> c-pr-item-two-smaller<?php endif; ?><?php endif; ?><?php if($mode == 'hp-promos-dark'): ?> c-pr-item-dark<?php endif; ?><?php /* if(!empty($item['image_2'])): ?> has-mobile<?php endif; */ ?>">
					<?php if($item['link']): ?><a class="c-pr-item-img" href="<?php echo $item['link']; ?>"><?php else: ?><span class="c-pr-item-img"><?php endif; ?>
						<?php if ($item['template'] == 'promo-one_in_row'): ?>
							<?php if(!empty($item['image_2']) AND $info['user_device'] == 'm'): ?>
								<img loading="lazy" <?php echo Thumb::generate($item['image_2'], array('width' => 980, 'height' => 670, 'crop' => TRUE, 'default_image' => '/media/images/no-image-980.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo $item['title']; ?>" />
							<?php else: ?>
								<img loading="lazy" <?php echo Thumb::generate($item['image'], array('width' => 1400, 'height' => 400, 'crop' => TRUE, 'default_image' => '/media/images/no-image-1400.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo $item['title']; ?>" />
							<?php endif; ?>
						<?php elseif($item['template'] == 'promo-three_in_row'): ?>
							<img loading="lazy" <?php echo Thumb::generate($item['image'], array('width' => 980, 'height' => 670, 'crop' => TRUE, 'default_image' => '/media/images/no-image-980.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo $item['title']; ?>" />
						<?php else: ?>
							<?php if($item['element_size'] == 'size_bigger'): ?>
								<?php if(!empty($item['image_2']) AND $info['user_device'] == 'm'): ?>
									<img loading="lazy" <?php echo Thumb::generate($item['image_2'], array('width' => 980, 'height' => 670, 'crop' => TRUE, 'default_image' => '/media/images/no-image-980.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo $item['title']; ?>" />
								<?php else: ?>
									<img loading="lazy" <?php echo Thumb::generate($item['image'], array('width' => 920, 'height' => 300, 'crop' => TRUE, 'default_image' => '/media/images/no-image-920.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo $item['title']; ?>" />
								<?php endif; ?>
							<?php else: ?>
								<img loading="lazy" <?php echo Thumb::generate($item['image'], array('width' => 980, 'height' => 670, 'crop' => TRUE, 'default_image' => '/media/images/no-image-980.jpg', 'html_tag' => TRUE)); ?> alt="<?php echo $item['title']; ?>" />
							<?php endif; ?>
						<?php endif; ?>
					<?php if($item['link']): ?></a><?php else: ?></span><?php endif; ?>
						
					<?php if (!empty($item['title'])): ?>
						<div class="c-pr-content">
							<?php if (!empty($item['title'])): ?>
								<?php if($item['link']): ?>
									<a class="c-pr-title" href="<?php echo $item['link']; ?>"><?php echo $item['title'] ?></a>
								<?php else: ?>
									<div class="c-pr-title"><?php echo $item['title'] ?></div>
								<?php endif; ?>
							<?php endif ?>
							<?php if (!empty($item['title2'])): ?>
								<div class="c-pr-subtitle"><?php echo $item['title2'] ?></div>
							<?php endif ?>
						</div>
					<?php endif ?>
				</div>
		<?php $p++; ?>
		<?php $cur_template = $item['template']; ?>
	<?php endforeach; ?>
	</div>
</div>