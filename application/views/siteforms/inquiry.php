<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-cms<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<form action="#inquiry_form" method="post" name="inquiry_form" id="inquiry_form" class="form-label ajax_siteform" accept-charset="utf-8" enctype="multipart/form-data">
	    <p class="global_error error global-error" <?php if ( ! isset($form['errors']) OR sizeof($form['errors']) == 0): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
		<?php foreach ($form['fields'] as $field => $field_element): ?>
			<?php $error = Valid::get_error($field, Arr::get($form, 'errors')); ?>
			<p class="field field-<?php echo $field; ?>">
				<label for="field-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'f_inquiry_'.$field, Arr::get($cmslabel, $field)); ?><?php if (isset($form['requested']) AND in_array($field, $form['requested'])): ?> *<?php endif; ?></label>
				<?php echo $field_element; ?>
				<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
			</p>
		<?php endforeach; ?>
		<button type="submit"><?php echo Arr::get($cmslabel, 'send'); ?></button>
	</form>	
<?php $this->endblock('content'); ?>