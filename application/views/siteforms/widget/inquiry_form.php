<?php $contact_form = Widget_Cms::form('hr', 'code:inquiry_form', $info); ?>
<?php if ($contact_form): ?>
	<div id="contact_form_layout" class="cd-inquiry">
		<div class="cd-inquiry-title"><?php echo Arr::get($cmslabel, 'not_available'); ?></div>
		<form action="<?php echo $contact_form['form_url']; ?>#contact_form" method="post" name="contact_form" id="contact_form" class="ajax_siteform form-label" data-siteform_response="show_hide" data-siteform_gtm_event_tracking="trackEvent|Form Submit|Product not Available|Success">
			<?php foreach (@$contact_form['fields'] as $field => $field_element): ?>
				<?php if($field == 'subject'): ?>
					<input type="hidden" name="subject" value="<?php if(isset($subject) AND $subject) echo $subject; ?>">
				<?php else: ?>
					<?php $error = @$contact_form['errors'][$field][0]; ?>
					<p class="field field-<?php echo $field; ?>">
						<label for="field-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'f_inquiry_'.$field, Arr::get($cmslabel, $field)); ?><?php if (!in_array($field, @$contact_form['requested'])): ?><span class="not-mandatory"><?php echo Arr::get($cmslabel, 'not_mandatory'); ?></span><?php endif; ?></label>
						<?php echo $field_element; ?>
						<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
					</p>
				<?php endif; ?>
			<?php endforeach; ?>
			<button type="submit" class="btn btn-yellow btn-cd-inquiry"><span><?php echo Arr::get($cmslabel, 'inquiry_send', 'Pošalji'); ?></span></button>
		</form>
		<div id="contact_form_success" class="cd-inquiry-success" style="display:none;" data-siteform_ignore_scroll="0">
			<?php echo Arr::get($cmslabel, 'inquiry_success', 'Uspješno slanje'); ?>
		</div>
	</div>
<?php endif; ?>