<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>"
	data-kne_use_double_pricetags="<?php echo (Kohana::config('app.utils.kn_euro_conversion.use_double_pricetags')) ?? 0; ?>"
	data-kne_conversion_executed="<?php echo ((isset($kn_euro_conversion_executed)) ? (int)$kn_euro_conversion_executed : (Kohana::config('app.utils.kn_euro_conversion.conversion_executed')));?>"
	data-kne_conversion_exchange="<?php echo (Kohana::config('app.utils.kn_euro_conversion.conversion_exchange')) ?? 7.5345; ?>"
	data-kne_conversion_date="<?php echo (strtotime((!empty($kn_euro_conversion_date)) ? $kn_euro_conversion_date : '2023-01-01 00:00:00')) * 1000; ?>"
	data-kne_double_prices_begin_date="<?php echo (strtotime((Kohana::config('app.utils.kn_euro_conversion.double_prices_begin_date')) ?? '2022-09-05 00:00:00')) * 1000 ; ?>"
	data-kne_double_prices_end_date="<?php echo (strtotime((Kohana::config('app.utils.kn_euro_conversion.double_prices_end_date')) ?? '2023-12-31 23:59:59')) * 1000 ; ?>"
	data-kne_kn_format="<?php echo Catalog::currency('HRK', 'display') ?? '%s kn'; ?>"
	data-kne_euro_format="<?php echo Catalog::currency('EUR', 'display') ?? '%s EUR'; ?>"
	data-kne_display_format="<?php echo (Kohana::config('app.utils.kn_euro_conversion.display_format.standard')) ?? ''; ?>"
	data-kne_second_price_to_front="<?php echo Kohana::config('app.utils.kn_euro_conversion.converted_price_show_in_front'); ?>"
>
<head>
    <?php if (Kohana::$environment === 1): ?><?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?><?php endif; ?>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<link rel="dns-prefetch" href="//www.google-analytics.com">
	<link rel="dns-prefetch" href="//ssl.google-analytics.com">
	<link rel="dns-prefetch" href="//connect.facebook.net">
	<link rel="dns-prefetch" href="//static.ak.facebook.com">
	<link rel="dns-prefetch" href="//s-static.ak.facebook.com">
	<link rel="dns-prefetch" href="//fbstatic-a.akamaihd.net">
	<link rel="dns-prefetch" href="//maps.gstatic.com">
	<link rel="dns-prefetch" href="//maps.google.com">
	<link rel="dns-prefetch" href="//maps.googleapis.com">
	<link rel="dns-prefetch" href="//mt0.googleapis.com">
	<link rel="dns-prefetch" href="//mt1.googleapis.com">
	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
	<link rel="manifest" href="/site.webmanifest">
	<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
	<meta name="msapplication-TileColor" content="#da532c">
	<meta name="theme-color" content="#ffffff">
    <?php if (!empty($info['page_class']) AND $info['page_class'] == 'page-cms-homepage'):  ?>
    <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type":"Organization",
            "name": "Centar Tehnike d.o.o.",
            "url":"https://www.centar-tehnike.hr/",
            "logo":"https://www.centar-tehnike.hr/media/images/logo.svg",
            "address":
                {
                    "@type": "PostalAddress",
                    "streetAddress": "Županijska 31",
                    "addressLocality": "Osijek",
                    "addressRegion": "HR",
                    "postalCode": "31000",
                    "addressCountry": "Croatia"
                },
            "contactPoint":
             {
                 "@type": "ContactPoint",
                 "contactType": "contact",
                 "telephone": "+38531499573",
                 "email": "<EMAIL>"
             },
            "sameAs":[""]}
    </script>
    <?php endif;?>
	<title><?php $this->block('title'); ?><?php $this->endblock('title'); ?></title>
	<?php if (Kohana::$environment !== 1): ?><meta name="robots" content="noindex, nofollow"><?php endif; ?>
	<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
	<?php //echo Html::media('fancybox,standard', 'css'); ?>
	<?php echo Html::media('css_gdefault'); ?>
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
    <script src="https://www.google.com/recaptcha/api.js"></script>
</head>
<body class="<?php echo $info['page_class']; ?><?php if(!empty(Arr::get($cmslabel, 'webshop_disabled'))): ?> webshop-disabled <?php endif; ?><?php $this->block('page_class'); ?><?php $this->endblock('page_class'); ?>">
    <?php echo View::factory('cms/ga4_tracking'); ?>
	<?php if (!empty($gdpr_approved) AND is_array($gdpr_approved) AND in_array('analytics', $gdpr_approved)): ?>
		<?php echo Facebook::init('647088069007610', $info['lang']); ?>
	<?php endif; ?>

	<div class="page-wrapper" id="top">
		<?php $this->block('header'); ?>
			<?php if(!empty(Arr::get($cmslabel, 'corner'))): ?>
				<div class="corner-info">
					<div class="corner-info-cnt">
						<?php echo Arr::get($cmslabel, 'corner'); ?>
					</div>
				</div>
			<?php endif; ?>

			<header class="header">
				<div class="wrapper wrapper-header pos-r">
					<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
					<a class="btn-toggle-nav" href="javascript:void(0);"><span></span></a>
					<div class="nav-container">
						<ul class="nav">
							<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
							<?php echo Widget_Cms::menu(array('lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'main', 'selected' => $active_menu_item, 'cached' => false)); ?>
						</ul>
						<div class="social social-top">
							<?php echo Arr::get($cmslabel, 'social'); ?>
						</div>
					</div>
					<?php echo View::factory('search/widget/form'); ?>
					<div class="top-contact">
						<?php echo Arr::get($cmslabel, 'top_contact'); ?>
					</div>
					<?php echo View::factory('auth/widget/user_box'); ?>

					<div class="top-container">
						<div class="top-flyer">
							<?php echo View::factory('cms/widget/flyer'); ?>
						</div>
						<?php echo View::factory('catalog/widget/wishlist'); ?>
						<?php echo View::factory('catalog/widget/compare'); ?>
						<?php echo View::factory('webshop/widget/shopping_cart'); ?>
					</div>

					<?php echo View::factory('catalog/widget/categories'); ?>

					<span class="m-nav-title" data-menu_title="<?php echo Arr::get($cmslabel, 'navigation'); ?>"><?php echo Arr::get($cmslabel, 'navigation'); ?></span>
				</div>
				<div class="header-m"></div>
			</header>
			<div class="header-placeholder"></div>
		<?php $this->endblock('header'); ?>

		<?php $this->block('before_main'); ?><?php $this->endblock('before_main'); ?>

		<?php $this->block('main_layout'); ?>
			<main class="main">
				<div class="wrapper main-wrapper">
					<?php $this->block('content_layout'); ?>
						<aside class="sidebar">
							<?php $this->block('sidebar'); ?>
								<div class="sidebar-container">
									<?php $this->block('sidebar_nav'); ?>
										<ul class="nav-sidebar">
											<?php echo Widget_Cms::menu(array('lang' => $info['lang'], 'generate_tree' => TRUE, 'code' => 'sidebar', 'selected' => $active_menu_item, 'layout_config' => ['title' => '<span>%title_field%</span>'])); ?>
										</ul>
									<?php $this->endblock('sidebar_nav'); ?>
								</div>

								<div class="sidebar-cols">
									<div class="sidebar-col sidebar-col1 sidebar-flyer">
										<?php echo View::factory('cms/widget/flyer'); ?>
									</div>
									<div class="sidebar-col sidebar-col2 sidebar-advisor">
										<?php echo Arr::get($cmslabel, 'advisor'); ?>
									</div>
								</div>

								<?php $this->block('sidebar_help'); ?>
									<div class="sidebar-help">
										<p class="sidebar-help-title"><?php echo Arr::get($cmslabel, 'help_and_support_title'); ?></p>
										<?php echo Arr::get($cmslabel, 'help_and_support'); ?>
									</div>
								<?php $this->endblock('sidebar_help'); ?>
							<?php $this->endblock('sidebar'); ?>
						</aside>
						<div class="main-content">
							<div class="cms-content">
								<div class="bc">
									<?php $this->block('breadcrumb'); ?>
									<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url']); ?>
									<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
									<?php $this->endblock('breadcrumb'); ?>
								</div>
								<?php $this->block('content'); ?><?php $this->endblock('content'); ?>
							</div>
						</div>
					<?php $this->endblock('content_layout'); ?>
				</div>
			</main>
		<?php $this->endblock('main_layout'); ?>

		<?php $this->block('after_main'); ?><?php $this->endblock('after_main'); ?>

		<?php $this->block('newsletter'); ?>
			<?php echo View::factory('newsletter/widget/manage'); ?>
		<?php $this->endblock('newsletter'); ?>

		<?php $this->block('footer'); ?>
			<footer class="footer">
				<div class="wrapper wrapper-footer">
					<div class="footer-row footer-row1">
						<div class="footer-col footer-col1">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_col1_title'); ?></div>
							<div class="footer-col-cnt">
								<?php echo Arr::get($cmslabel, 'footer_col1'); ?>
								<div class="social-footer">
									<?php echo Arr::get($cmslabel, 'social'); ?>
								</div>
							</div>
						</div>
						<div class="footer-col footer-col2">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_col2_title'); ?></div>
							<div class="footer-col-cnt">
								<?php $footer_menu_col2 = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'footer_col2', 'selected' => $active_menu_item)); ?>
								<?php if($footer_menu_col2): ?>
									<ul class="nav-footer-col">
									<?php foreach ($footer_menu_col2 as $footer_menu_col2_item): ?>
									    <li><a href="<?php echo $footer_menu_col2_item['url']; ?>" title="<?php echo $footer_menu_col2_item['anchor_text']; ?>"<?php if($footer_menu_col2_item['target_blank']): ?> target="_blank"<?php endif; ?><?php if($info['basic_url'] == $footer_menu_col2_item['url']): ?> class="active"<?php endif; ?>><?php echo $footer_menu_col2_item['title']; ?></a></li>
									<?php endforeach; ?>
									</ul>
								<?php endif; ?>
							</div>
						</div>
						<div class="footer-col footer-col3">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_col3_title'); ?></div>
							<div class="footer-col-cnt">
								<?php $footer_menu_col3 = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'footer_col3', 'selected' => $active_menu_item)); ?>
								<?php if($footer_menu_col3): ?>
									<ul class="nav-footer-col">
									<?php foreach ($footer_menu_col3 as $footer_menu_col3_item): ?>
									    <li><a href="<?php echo $footer_menu_col3_item['url']; ?>" title="<?php echo $footer_menu_col3_item['anchor_text']; ?>"<?php if($footer_menu_col3_item['target_blank']): ?> target="_blank"<?php endif; ?><?php if($info['basic_url'] == $footer_menu_col3_item['url']): ?> class="active"<?php endif; ?>><?php echo $footer_menu_col3_item['title']; ?></a></li>
									<?php endforeach; ?>
									<li><a href="javascript:void(0);" class="footer-gdpr-popup-btn-edit gdpr_configurator_button" id="gdpr_configurator_button"><?php echo Arr::get($cmslabel, 'gdpr_edit'); ?></a></li>
									</ul>
								<?php endif; ?>
							</div>
						</div>
						<div class="footer-col footer-col4">
							<div class="footer-title"><?php echo Arr::get($cmslabel, 'footer_col4_title'); ?></div>
							<div class="footer-col-cnt">
								<?php echo Arr::get($cmslabel, 'footer_col4'); ?>
							</div>
						</div>
					</div>
					<div class="footer-row footer-row2">
						<div class="footer-col footer-col1">
							<?php echo Arr::get($cmslabel, 'footer_banner_col1'); ?>
						</div>
						<div class="footer-col footer-col2">
							<?php echo Arr::get($cmslabel, 'footer_banner_col2'); ?>
						</div>
						<div class="footer-col footer-col3">
							<div class="footer-col3-safe-purchase">
								<?php echo Arr::get($cmslabel, 'safe_purchase'); ?>
							</div>
						</div>
						<div class="footer-col footer-col4">
							<div class="cards">
								<?php echo Arr::get($cmslabel, 'cards'); ?>
							</div>
						</div>
					</div>
					<div class="footer-row footer-row3">
						<div class="footer-col footer-col-locations">
							<?php echo View::factory('location/widget/locations', ['class' => 'nav-footer nav-footer-locations']); ?>
						</div>
					</div>

					<div class="footer-row footer-row4">
						<div class="footer-col footer-col1">
							<p class="copy"><?php echo str_replace('%YEAR%', date('Y'), Arr::get($cmslabel, 'copyright')); ?></p>
						</div>
						<div class="footer-col footer-col-dev">
							<p class="dev">
								<a href="https://marker.hr/izrada-web-shopa/" title="Web shop izrada" target="_blank" class="dev-signature" rel="nofollow">Izrada web shopa</a>: 
								<a href="https://marker.hr/" target="_blank" rel="nofollow">Marker.hr</a>
							</p>
						</div>
					</div>

					<div class="footer-row footer-row5">
						<div class="footer-col-cnt"><?php echo Arr::get($cmslabel, 'footer'); ?></div>
					</div>

					<a href="#top" class="ontop"></a>
				</div>
			</footer>
		<?php $this->endblock('footer'); ?>
	</div>

	<?php echo View::factory('gdpr/widget/configurator'); ?>

	<button class="tally-chat-btn" data-tally-open="nWq7P3" data-tally-align-left="1" data-tally-hide-title="1" data-tally-emoji-text="👋" data-tally-emoji-animation="wave"><span>Korisnička podrška</span></button>

	<?php echo Html::media('js_gdefault'); ?>
	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>

    <?php if (Kohana::$environment === 1): ?>
        <?php
        $page_type = 'other';
        $items_ids = [];
        $items_ids2 = [];
        $total_value = '';
        $event = 'dynamicRemarketing';
        if (Arr::get($info, 'page_class') == 'page-cms-homepage') {
            $page_type = 'home';
        } elseif (Arr::get($info, 'controller') == 'catalog' AND Arr::get($info, 'action') == 'detail') {
            $page_type = 'offerdetail';
            $items_ids = [$this->item['id']];
            $items_ids2 = [$this->item['code']];
            $total_value = !empty($this->item['price_custom']) ? number_format($this->item['price_custom'], 2, '.', '') : '';
        } elseif (Arr::get($info, 'controller') == 'catalog' AND Arr::get($info, 'action') == 'index' AND !empty(Arr::get($_GET, 'search_q'))) {
            $page_type = 'searchresults';
            $items_ids = array_values(array_map(function ($element) {
                return $element['id'];
            }, $this->items));
            $items_ids2 = array_values(array_map(function ($element) {
                return $element['code'];
            }, $this->items));
            $total_value = !empty($this->items) ? number_format(array_sum(array_column($this->items, 'price_custom')), 2, '.', '') : '';
        } elseif (Arr::get($info, 'controller') == 'catalog' AND Arr::get($info, 'action') == 'index') {
            $page_type = 'offerdetail';
            $items_ids = array_values(array_map(function ($element) {
                return $element['id'];
            }, $this->items));
            $items_ids2 = array_values(array_map(function ($element) {
                return $element['code'];
            }, $this->items));
            $total_value = !empty($this->items) ? array_sum(array_column($this->items, 'price_custom')) : '';
        } elseif (Arr::get($info, 'controller') == 'webshop' AND in_array(Arr::get($info, 'action'), ['shopping_cart', 'login', 'shipping', 'shipping_only', 'payment', 'review_order'])) {
            $page_type = 'conversionintent';
            if (!empty($this->products)) {
                $items_ids = array_values(array_map(function ($element) {
                    return $element['id'];
                }, $this->products));
                $items_ids2 = array_values(array_map(function ($element) {
                    return $element['code'];
                }, $this->products));
            }
            $total_value = !empty($this->shopping_cart_info['total']) ? number_format($this->shopping_cart_info['total'], 2, '.', '') : '';
        } elseif (Arr::get($info, 'controller') == 'webshop' AND Arr::get($info, 'action') == 'thank_you' AND $order AND $order_first) {
            $page_type = 'conversion';
            if (!empty($this->order->items)) {
                $items_ids = array_map(function ($element) {
                    return $element['id'];
                }, $this->order->items->as_array());
                $items_ids2 = array_map(function ($element) {
                    return $element['code'];
                }, $this->order->items->as_array());
            }
            $total_value = number_format($this->order->total, 2, '.', '');
        }
        ?>
        <?php echo Google::dynamic_remarketing($info['gtagmanager_code'], $items_ids, $page_type, $total_value, $event, $items_ids2); ?>
    <?php endif; ?>
	
	<?php if (!empty($gdpr_approved) AND is_array($gdpr_approved) AND in_array('analytics', $gdpr_approved)): ?>
		<script>
			(function(w,d,t,u,n,a,m){w['MauticTrackingObject']=n;
				w[n]=w[n]||function(){(w[n].q=w[n].q||[]).push(arguments)},a=d.createElement(t),
				m=d.getElementsByTagName(t)[0];a.async=1;a.src=u;m.parentNode.insertBefore(a,m)
			})(window,document,'script','https://centartehnike.sales-snap.com/mtc.js','mt');
			mt('send', 'pageview');
		</script>
	<?php endif; ?>
	<?php //$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list')); ?>
	<?php //echo View::factory('newsletter/widget/manage_leaving', ['newsletter_form' => $newsletter_form]); ?>
	<?php echo View::factory('admin/widget_fe/toolbar'); ?>
</body>
</html>