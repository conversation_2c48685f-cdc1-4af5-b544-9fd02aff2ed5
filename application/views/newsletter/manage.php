<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['item' => $item]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1', $item['seo_h1']); ?></h1>
    <?php echo Arr::get($cms_page, 'content', $item['content']); ?>

	<?php if (Arr::get($_GET, 'success') === 'subscribe_confirm'): ?>
		<p class="global-success"><?php echo Arr::get($cmslabel, "success_subscribe_confirm"); ?></p>
	<?php elseif (Arr::get($_GET, 'success') === 'subscribe_confirm_coupon'): ?>
		<p class="global-success"><?php echo Arr::get($cmslabel, "success_subscribe_confirm_coupon"); ?></p>
	<?php elseif ($info['message_type'] AND $info['message']): ?>
		<?php if (is_array($info['message'])): ?>
			<p class="global-<?php echo $info['message_type']; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
		<?php else: ?>
			<p class="global-<?php echo $info['message_type']; ?>"><?php echo Arr::get($cmslabel, "{$info['message_type']}_{$info['message']}"); ?></p>
		<?php endif; ?>
	<?php endif; ?>
<?php $this->endblock('content'); ?>
<?php if (Kohana::$environment === 1 AND in_array(Arr::get($_GET, 'success'), ['subscribe_confirm', 'subscribe_confirm_coupon'])): ?>
    <?php $this->block('extrabody'); ?>
    <script>
        dataLayer.push({
            'event': 'trackEvent',
            'category': 'Newsletter'
            'action': 'Subscribe'
            'label': 'Success'
        });
    </script>
    <?php $this->endblock('extrabody'); ?>
<?php endif; ?>
