<?php if (!isset($newsletter_form)) {$newsletter_form = Widget_Newsletter::form(array('lang' => $info['lang'], 'code' => 'list'));} ?>
<?php if ($newsletter_form): ?>
<div class="nw">
	<div class="nw-col nw-col1">
		<div class="nw-col1-cnt">
			<div class="nw-title"><?php echo Arr::get($cmslabel, 'newsletter_widget_title'); ?></div>
			<div class="nw-subtitle"><?php echo Arr::get($cmslabel, 'newsletter_widget_subtitle'); ?></div>	
			<form class="nw-form" data-main_action="subscribe" action="<?php echo $newsletter_form['url_manage']; ?>" method="POST" id="newsletter_subscribe_<?php echo $newsletter_form['id']; ?>">
				<div class="nw-form-cnt">
					<input type="hidden" name="lang" value="<?php echo $info['lang']; ?>"  />
					<input type="hidden" name="list" value="<?php echo $newsletter_form['id']; ?>" />
					<input type="hidden" name="first_name" value="" />
					<input type="hidden" name="last_name" value="" />
					<input class="nw-input" type="text" name="email" placeholder="<?php echo Arr::get($cmslabel, 'nw_enter_email'); ?>" />
					<button class="btn-white nw-button g-recaptcha" data-sitekey="<?php echo Utils::get_recaptcha_key('site_key'); ?>" data-callback="onSubmit" data-action="submit" data-before_submit type="submit"><?php echo Arr::get($cmslabel, 'newsletter_signup', 'Pošalji'); ?></button>
				</div>
				<div id="field-error-email" class="nw-error newsletter-error" style="display: none;"></div>

				<?php if (!empty($newsletter_form['gdpr_accept_label'])): ?>
					<div class="nw-checkbox">
						<input type="hidden" name="gdpr_accept" value="0" />
						<input type="checkbox" name="gdpr_accept" value="1" id="gdpr_accept-1" />
						<label for="gdpr_accept-1"><?php echo str_replace(['<p>', '</p>'], " ", $newsletter_form['gdpr_accept_label']); ?></label>
						<span id="field-error-newsletter_gdpr_accept" class="error gdpr_accept-error" style="display: none"></span>
					</div>
				<?php endif; ?>
			</form>
			<div class="nw-success newsletter_subscribe_success" style="display: none;"><?php echo Arr::get($cmslabel, 'success_subscribe'); ?></div>
			<div class="nw-note"><?php echo Arr::get($cmslabel, 'newsletter_widget_note'); ?></div>
		</div>
	</div>
	<div class="nw-col nw-col2"></div>
</div>
<?php endif; ?>