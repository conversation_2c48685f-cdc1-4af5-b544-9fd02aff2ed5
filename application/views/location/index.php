<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-locations<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
	<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php echo Arr::get($cms_page, 'content'); ?>

	<?php $map_points = Widget_Location::points(array('lang' => $info['lang'])); ?>
	<?php if($map_points): ?>
		<?php echo View::factory('location/widget/locations', ['class' => 'nav-locations', 'url' => 0, 'locations' => $map_points]); ?>
	<?php endif; ?>
	
	<?php if ($map_points): ?>
		<div id="mapContainer" class="map">
			<div id="map_canvas" style="width:100%;height:100%;"></div>
		</div>
		<?php foreach ($map_points AS $point): ?>
			<div style="display: none;"
				data-gmap_object_id="<?php echo $point['id']; ?>"
				data-gmap_object_lat="<?php echo $point['gmap']['lat']; ?>"
				data-gmap_object_lon="<?php echo $point['gmap']['lon']; ?>"
				data-gmap_object_tooltip="_content"
				data-id="<?php echo Text::meta($point['id']); ?>">
					<span class="infoBox-cnt">
						<?php if($point['main_image']): ?><span class="image"><img <?php echo Thumb::generate($point['main_image'], array('width' => 250, 'height' => 135, 'crop' => true, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" width="270" height="135" /></span><?php endif; ?>
						<span class="infoBox-body">
							<span class="title"><?php echo $point['title']; ?></span>
							<span class="address"><?php echo nl2br($point['address']); ?></span>
							<span class="contact"><?php echo nl2br($point['contact']); ?></span>
							<?php if(!empty($point['business_hour'])): ?>
							<span class="business-hour"><div class="working-hours"><strong><?php echo Arr::get($cmslabel, 'working_hours'); ?>:</strong></div><?php echo nl2br($point['business_hour']); ?></span>
							<?php endif; ?>
						</span>
					</span>
			</div>
		<?php endforeach; ?>
	<?php endif; ?>

	<?php if ($items): ?>
		<div class="l-items" id="mappoints">
			<?php $i = 0; ?>
			<?php foreach ($items AS $item): ?>
				<div class="lp" id="<?php echo $item['code']; ?>">
					<h2 class="lp-title"><?php echo $item['title']; ?></h2>
					<div class="lp-col lp-col1">
						<?php $images = Utils::get_files('locationpoint', $item['id'], 'image', $info['lang'], 0); ?>
						<?php if ($images): ?>
							<div class="lp-images">
								<?php foreach ($images as $file): ?>
									<span rel="gallery-<?php echo $i; ?>" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
										<span><img <?php echo Thumb::generate($file['file'], ['width' => 420, 'height' => 280, 'crop' => TRUE, 'default_image' => '/media/images/no-image-420.jpg', 'html_tag' => true]); ?> alt="<?php echo Text::meta($file['description']); ?>" /></span>
									</span>
								<?php endforeach; ?>
							</div>
						<?php else: ?>
							<img src="/media/images/no-image-420.jpg" width="420" height="280" alt="">
						<?php endif; ?>
					</div>
					<div class="lp-col lp-col2">
						<?php if(!empty($item['address'])): ?>
							<div class="lp-address"><a href="#mapContainer"><?php echo nl2br($item['address']); ?></a></div>
						<?php endif; ?>
						<?php if(!empty($item['contact'])): ?>
							<div class="lp-contact"><?php echo nl2br($item['contact']); ?></div>
						<?php endif; ?>
						<?php if(!empty($item['business_hour'])): ?>
							<div class="lp-hours">
								<div class="lp-hours-title"><?php echo Arr::get($cmslabel, 'working_hours'); ?>:</div>
								<?php echo nl2br($item['business_hour']); ?>		
							</div>
						<?php endif; ?>
					</div>
				</div>
				<?php $i++; ?>
			<?php endforeach; ?>
		</div>
	<?php else: ?>
		<?php echo Arr::get($cmslabel, 'no_locations'); ?>
	<?php endif; ?>	
<?php $this->endblock('content'); ?>

<?php $this->block('extrabody'); ?>
	<?php echo Html::media('cmslocation,infobox', 'js'); ?>
<?php $this->endblock('extrabody'); ?>