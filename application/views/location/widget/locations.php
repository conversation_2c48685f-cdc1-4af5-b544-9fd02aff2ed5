<?php $class = (isset($class)) ? $class : ''; ?>
<?php $locations = (isset($locations)) ? $locations : Widget_Location::points(array('lang' => $info['lang'])); ?>
<?php if($locations): ?>
	<ul class="<?php if($class): ?><?php echo $class; ?><?php else: ?>nav-footer<?php endif; ?>">
		<?php foreach ($locations AS $location): ?>
			<li><a href="<?php if(!isset($url)): ?>/lokacije-trgovina/<?php endif; ?>#<?php echo $location['code']; ?>"><span><?php echo $location['title']; ?></span></a></li>
		<?php endforeach; ?>
	</ul>
<?php endif; ?>