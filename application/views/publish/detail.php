<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>

<?php $comment_status = (!empty($item['feedback_comment_widget'])) ? $item['feedback_comment_widget']['comments_status'] : 0; ?>
<?php $rates_status = (!empty($item['feedback_rate_widget'])) ? $item['feedback_rate_widget']['rates_status'] : 0; ?>

<?php $this->block('main_layout'); ?>
	<div class="wrapper">
		<div class="pd-header">
			<div class="bc">
				<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>			
			</div>

			<h1 class="pd-title"><?php echo $item['seo_h1'] ?></h1>

			<div class="pd-info">
				<div class="pd-date"><?php echo Date::humanize($item['datetime_published'], 'custom', 'd.m.Y.'); ?></div>
				
				<?php if ($rates_status > 1 AND $item['feedback_rate_widget']['rates_votes'] > 0): ?>
					<?php echo View::factory('feedback/rates', $item['feedback_rate_widget']); ?>
				<?php endif; ?>

				<?php if ($comment_status > 1): ?>
					<div class="pd-comments">
						<a class="value" href="#comments">
							<span class="label"><?php echo Arr::get($cmslabel, 'comments', 'Komentara'); ?></span>
							(<?php echo Arr::get($item['feedback_comment_widget'], 'comments', 0); ?>)
						</a>
					</div>
				<?php endif; ?>
			</div>
		</div>

		<?php if ($item['main_image']): ?>
			<div class="pd-hero-image">
				<img <?php echo Thumb::generate($item['main_image'], ['width' => 1400, 'height' => 800, 'default_image' => '/media/images/no-image-1400.jpg', 'html_tag' => true]); ?>  alt="<?php echo Text::meta($item['main_image_description']); ?>" />
			</div>
		<?php endif; ?>

		<div class="cms-content pd-content">
			<?php if(!empty($item['short_description'])): ?>
				<div class="extra pd-short-desc"><?php echo $item['short_description']; ?></div>
			<?php endif; ?>
			
			<!-- Post content and related documents -->
			<div class="pd-desc">
				<?php echo $item['content']; ?>
				<?php $documents = Utils::get_files('publish', $item['id'], '-image', $info['lang']); ?>
				<?php if ($documents): ?>
					<ul class="pd-documents">
						<?php foreach ($documents as $file): ?>
							<li><a href="<?php echo $file['url']; ?>" title="<?php echo Text::meta($file['description']); ?>"><?php echo Text::meta($file['title']); ?></a></li>
						<?php endforeach; ?>
					</ul>
				<?php endif; ?>
			</div>

			<?php $images = Utils::get_files('publish', $item['id'], 'image', $info['lang'], 1); ?>
			<?php if ($images): ?>
				<div class="pd-thumbs">
					<?php foreach ($images as $file): ?>
						<a href="<?php echo $file['url']; ?>" class="fancybox" rel="gallery" title="<?php echo Text::meta($file['title']); ?><?php if($file['description']): ?> - <?php endif; ?><?php echo Text::meta($file['description']); ?>">
							<img <?php echo Thumb::generate($file['file'], ['width' => 880, 'height' => 340, 'crop' => TRUE, 'default_image' => '/media/images/no-image-880.jpg', 'html_tag' => true]); ?> alt="<?php echo Text::meta($file['description']); ?>" />
						</a>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>

			<?php echo View::factory('cms/widget/share', ['item' => isset($item) ? $item : []]); ?>	

			<!-- Comments -->
			<?php if ($comment_status > 1): ?>
				<div class="comments-<?php echo $comment_status; ?>" id="comments">
					<?php echo View::factory('feedback/comments', ['item' => $item['feedback_comment_widget'], 'items' => $item['feedback_comment_widget']['items'], 'content' => $item['feedback_comment_widget']['content']]); ?>
				</div>
			<?php endif; ?>
		</div>
	</div>

	<!-- Related posts -->
	<?php $related_items = Widget_Publish::publishes(['lang' => $info['lang'], 'category_code' => 'blog', 'id_exclude' => $item['id'], 'limit' => 3]); ?>
	<?php if ($related_items): ?>
		<div class="pd-related">
			<div class="wrapper">
				<div class="pd-related-title"><?php echo Arr::get($cmslabel, 'publish_related', 'Pročitajte i...'); ?></div>
				<div class="p-items">
					<?php echo View::factory('publish/index_entry', ['items' => $related_items]); ?>
				</div>
				<div class="pw-related-btns"><a class="btn btn-yellow btn-all" href="<?php echo $item['category_url']; ?>"><?php echo Arr::get($cmslabel, 'all_articles'); ?></a></div>
			</div>
		</div>
	<?php endif; ?>
<?php $this->endblock('main_layout'); ?>