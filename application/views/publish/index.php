<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>


<?php $this->block('main_layout'); ?>
	<div class="wrapper">
		<div class="p-header">
			<div class="bc">
				<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $kind['breadcrumbs']); ?>
				<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>			
			</div>
			<h1><?php echo $kind['seo_h1']; ?></h1>
			<?php echo $kind['content']; ?>
		</div>

		<div id="items_<?php echo $kind['code']; ?>_layout">
			<?php echo View::factory('publish/index_layout', [
				'kind' => $kind,
				'q' => '',
				'items' => $items,
				'items_per_page' => $items_per_page,
				'items_all' => $items_all,
				'items_total' => $items_total,
				'child_categories' => $child_categories,
				'child_categories_published' => $child_categories_published,
				'pagination' => $pagination,
				]); ?>
		</div>
	</div>
<?php $this->endblock('main_layout'); ?>