<?php $featured = (isset($featured)) ? $featured : ''; ?>
<?php $label = (isset($label)) ? $label : ''; ?>
<?php $i = 1; ?>
<?php if($featured == 1 AND count($items) >= 2): ?>
	<div class="p-featured">
	<?php if($label): ?>
		<div class="hp-header-label blog-label"><?php echo Arr::get($cmslabel, 'special_articles'); ?></div>
	<?php endif; ?>
<?php endif; ?>
<?php foreach ($items as $item): ?>		
	<?php $class = ''; ?>
	<?php if($featured): ?>
		<?php if($i == 1): ?>
			<?php $class = 'pp-featured'; ?>
		<?php elseif($i == 2 OR $i == 3): ?>
			<?php $class = 'pp-featured-small'; ?>
		<?php endif; ?>
	<?php endif; ?>

	<a href="<?php echo $item['url']; ?>" class="pp <?php echo $class; ?>">
		<?php $img_w = ($i == 1 AND $featured) ? 920 : 435; ?>
		<?php $img_h = ($i == 1 AND $featured) ? 480 : 230; ?>

		<figure class="pp-image">
			<span><img loading="lazy" data-lazy="<?php echo Thumb::generate($item['main_image'], $img_w, $img_h, true, 'thumb', TRUE, '/media/images/no-image-'.$img_w.'.jpg'); ?>" <?php echo Thumb::generate($item['main_image'], array('width' => $img_w, 'height' => $img_h, 'crop' => TRUE, 'default_image' => '/media/images/no-image-'.$img_w.'.jpg', 'html_tag' => true)); ?> title="<?php echo Text::meta($item['main_image_title']); ?>" alt="<?php echo Text::meta($item['main_image_description']); ?>" /></span>
		</figure>
		<div class="pp-cnt">
			<h2 class="pp-title"><?php echo $item['title']; ?></h2>
			<?php if($i == 1 AND $featured AND !empty($item['short_description'])): ?>
				<div class="pp-short-desc"><?php echo strip_tags($item['short_description']); ?></div>
			<?php endif; ?>
		</div>
	</a>
	
	<?php if($featured == 1 AND $i == 3): ?></div><?php endif; ?>
	<?php $i++; ?>
<?php endforeach; ?>