<div class="aw">
	<a class="aw-btn<?php if($user): ?> aw-btn-loggedin<?php endif; ?>" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'my_account'); ?></span></a>
	<?php if ($user): ?>
		<div class="aw-dropdown-list aw-loggedin">
			<a class="aw-link aw-profile" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'my_profile'); ?></span></a>
			<a class="aw-link aw-orders" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false); ?>"><span><?php echo Arr::get($cmslabel, 'my_orders'); ?></span></a>
			<a class="aw-link coupon" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshopcoupon', FALSE); ?>/#coupons"><span><?php echo Arr::get($cmslabel, 'my_coupons'); ?></span></a>
			<?php if (Kohana::config('app.catalog.use_wishlists')): ?>
				<a href="<?php echo Utils::app_absolute_url($info['lang'], 'catalog'); ?>?special_view=wishlist&<?php echo time(); ?>"><span><?php echo Arr::get($cmslabel, 'my_wishlist'); ?></span></a>
			<?php endif; ?>
			<a class="aw-link edit" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'edit_profile'); ?></span></a>
			<a class="aw-link pass" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'change_password'); ?></span></a>
			<a class="aw-link logout" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>"><span><?php echo Arr::get($cmslabel, 'logout'); ?></span></a>
		</div>
	<?php else: ?>
		<div class="aw-dropdown-list aw-login">
			<form action="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>" method="post" accept-charset="utf-8" enctype="multipart/form-data" name="login" class="form-label form-quick-login ajax_siteform ajax_siteform_loading" data-tracking_gtm_forms="GTMForms|Login">
				<div class="aw-quick-login-title"><?php echo Arr::get($cmslabel, 'login_subtitle'); ?></div>
				<div id="field-error-email" class="field_error global-error aw-error" style="display: none"></div>
				<div class="field field-quick field-quick-username">
					<label for="quick-email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
					<input name="email" type="text" id="quick-email" tabindex="1" />
				</div>
				<div class="field field-quick field-quick-password">
					<label for="quick-password"><?php echo Arr::get($cmslabel, 'password'); ?></label>
					<input name="password" type="password" id="quick-password" tabindex="2" />
				</div>
				<div class="field field-quick field-remember">
					<input type="checkbox" name="remember" id="quick-id_remember" value="1" checked />
					<label for="quick-id_remember"><?php echo Arr::get($cmslabel, 'remember'); ?></label>
				</div>
				<div class="lr-btn-pw">
					<button type="submit" class="btn btn-secondary btn-aw-login"><span><?php echo Arr::get($cmslabel, 'login'); ?></span></button>
					<div class="quick-auth-links">
						<a class="forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'quick_forgotten_password'); ?></span></a>
					</div>
				</div>
			</form>
			<div class="aw-footer">
				<div class="aw-quick-login-title"><?php echo Arr::get($cmslabel, 'new_user'); ?></div>
				<a class="btn btn-aw-signup" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'open_account'); ?></span></a>
			</div>
		</div>
	<?php endif; ?>
</div>