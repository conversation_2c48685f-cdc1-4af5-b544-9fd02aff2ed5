<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="forgotten-password-wrapper">
		<h1 class="forgotten-password-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
		<?php echo Arr::get($cms_page, 'content'); ?>

		<?php if ($message_type AND $message): ?>
			<?php if (is_array($message)): ?>
				<p class="global-error global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
			<?php else: ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
			<?php endif; ?>
		<?php endif; ?>

		<?php if ($message_type != 'success'): ?>
			<form method="post" action="" id="forgotten-password-form" accept-charset="utf-8" enctype="multipart/form-data" name="forgotten_password" class="form-label ajax_siteform auth-form auth-forgotten-password-form">
				<?php $error = ($message_type AND $message) ? "{$message_type}_{$message}" : ""; ?>
				<p class="field">
					<label for="id_email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
					<input type="text" name="email" id="id_email" />
					<span id="field-error-email" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
				</p>
				<p class="submit"><button type="submit" class="g-recaptcha" data-sitekey="<?php echo Utils::get_recaptcha_key('site_key'); ?>" data-callback="onSubmit" data-action="submit" data-before_submit><?php echo Arr::get($cmslabel, 'send'); ?></button></p>
				<p class="auth-links">
					<a class="back" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', false); ?>"><span><?php echo Arr::get($cmslabel, 'back_to_login'); ?></span></a>
					<a class="signup" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'add_new_account'); ?></span></a>
				</p>
			</form>
		<?php endif; ?>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('loyalty'); ?> <?php $this->endblock('loyalty'); ?>