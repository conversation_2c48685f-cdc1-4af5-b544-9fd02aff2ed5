<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth<?php $this->endblock('page_class'); ?>

<?php $this->block('sidebar_nav'); ?>
	<?php if ($user): ?>
		<ul class="nav-sidebar">
			<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', '', FALSE) == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'my_profile'); ?></span></a></li>
			<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false)== $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false); ?>#main"><span><?php echo Arr::get($cmslabel, 'my_orders'); ?></span></a></li>
			<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', FALSE); ?>#coupons"><span><?php echo Arr::get($cmslabel, 'coupons'); ?></span></a></li>
			<?php if (Kohana::config('app.catalog.use_wishlists')): ?>
				<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'catalog') .'?special_view=wishlist&'.time(); ?>" <?php if (Utils::app_absolute_url($info['lang'], 'webshop', 'wishlist') == $info['url']): ?> class="active"<?php endif; ?>><span><?php echo Arr::get($cmslabel, 'my_wishlist'); ?></span></a></li>
			<?php endif; ?>
			<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE) == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'edit_profile'); ?></span></a></li>
			<li<?php if (Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE) == $info['url']): ?> class="selected"<?php endif; ?>><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'change_password'); ?></span></a></li>
			<li><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'logout'); ?></span></a></li>
		</ul>
	<?php endif; ?>
<?php $this->endblock('sidebar_nav'); ?>

<?php $this->block('content'); ?>
	<?php $this->block('h1'); ?>
		<h1 class="a-auth-title"><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	<?php $this->endblock('h1'); ?>

	<?php $message_type = (isset($message_type) AND $message_type) ? $message_type : Arr::get($info, 'message_type', ''); ?>
	<?php $message = (isset($message) AND $message) ? $message : Arr::get($info, 'message', ''); ?>
	<?php $this->block('error'); ?>
		<?php if ($message_type AND $message): ?>
			<?php if (is_array($message)): ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
			<?php else: ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
			<?php endif; ?>
		<?php endif; ?>
	<?php $this->endblock('error'); ?>

	<?php echo Arr::get($cms_page, 'content'); ?>
	<?php $this->block('content2'); ?><?php $this->endblock('content2'); ?>

    <?php if (Kohana::$environment === 1 AND $message_type == 'success' AND $message == 'confirm_signup'): ?>
        <script>
            dataLayer.push({
                'event': 'trackEvent',
                'category': 'Registration'
                'action': 'Process'
                'label': 'Success'
            });
        </script>
    <?php endif; ?>
<?php $this->endblock('content'); ?>
