<?php $this->extend('auth/default'); ?>

<?php $this->block('content2'); ?>
	<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="edit" class="ajax_siteform form-label auth-form auth-edit-profile-form" data-webshop_autocomplete="zipcode_city_location">
		<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
		<?php foreach ($customer_fields as $field): ?>
			<?php if (in_array($field, ['location', 'city'])) {continue;} ?>
			<?php $error = Valid::get_error($field, $message); ?>
			<p class="field<?php if($field == 'zipcode'): ?> field-autocomplete<?php endif; ?> field-<?php echo $field; ?>">
				<?php if($field == 'newsletter'): ?>
					<?php echo $item->input($field, 'form'); ?>	
					<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
				<?php elseif ($field == 'zipcode'): ?>
					<label for="field-location" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, 'zipcode_city', 'Poštanski broj / mjesto'); ?><?php if (in_array($field, $request_fields)): ?> <?php endif; ?></label>
					<input type="text" name="location" id="field-location" value="<?php echo $user->city . (!empty($user->zipcode) ? ' (' . $user->zipcode . ')' : ''); ?>" />
					<input type="hidden" name="zipcode" id="field-zipcode" value="<?php echo $user->zipcode; ?>" />
					<input type="hidden" name="city" id="field-city" value="<?php echo $user->city; ?>" />
					<span class="phone-tooltip location-tooltip"><?php echo Arr::get($cmslabel, 'zipcode_city_tooltip'); ?></span>
					<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if ( ! $error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
				<?php else:  ?>
					<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field); ?><?php if (in_array($field, $request_fields)): ?><?php endif; ?></label>
					<?php echo $item->input($field, 'form'); ?>		
				<?php endif; ?>
				<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
			</p>
		<?php endforeach; ?>
		<p class="submit"><button type="submit"><?php echo Arr::get($cmslabel, 'save'); ?></button></p>
	</form>
<?php $this->endblock('content2'); ?>
