<?php $this->extend('auth/default'); ?>
<?php $this->block('page_class'); ?> page-auth page-orders<?php $this->endblock('page_class'); ?>

<?php $this->block('content2'); ?>
	<div id="items_widgetlist_webshop_layout">
		<?php echo View::factory('auth/widgetlist/webshoporder', ['items' => $items, 'pagination' => $pagination, 'items_total' => $items_total, 'items_filtered_total' => $items_filtered_total]); ?>
	</div>
<?php $this->endblock('content2'); ?>

<?php $this->block('extrabody'); ?>
<?php echo Html::media('cmsinfinitescroll', 'js'); ?>
<script type="text/javascript">
	$(function(){
		$('#items_widgetlist_webshop').CmsInfiniteScroll();
	});
</script>
<?php $this->endblock('extrabody'); ?>