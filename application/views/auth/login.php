<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="auth-wrapper">		
		<div class="a-col a-col1 a-login-col1">
			<?php if ($message_type AND $message): ?>
				<?php if (is_array($message)): ?>
					<p class="global-<?php echo $message_type; ?> auth-dashboard-msg"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
					<p class="global-<?php echo $message_type; ?> auth-dashboard-msg"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
				<?php endif; ?>
			<?php endif; ?>

			<?php if ($message_type != 'success' OR (in_array($message, ['confirm_signup', 'reset_password', 'logout', 'forgotten_password']))): ?>	
				<h2 class="a-subtitle"><?php echo Arr::get($cmslabel, 'already_register_login'); ?></h2>
				<form method="post" id="login-form" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" class="form-label ajax_siteform auth-form auth-login-form">
					<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
					<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>
					<?php $error = ($message_type AND $message_type != 'success' AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<p class="field email">
						<label for="id_email"><?php echo Arr::get($cmslabel, 'email'); ?></label><input type="email" name="email" id="id_email" />
						<span id="field-error-email" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<p class="field password"><label for="id_password"><?php echo Arr::get($cmslabel, 'password'); ?></label><input type="password" name="password" id="id_password" /></p>
					<p class="field remember"><input type="checkbox" name="remember" id="id_remember" value="1" checked /><label for="id_remember"><?php echo Arr::get($cmslabel, 'remember'); ?></label></p>
					<p class="submit"><button class="btn btn-gray btn-arrow g-recaptcha" data-sitekey="<?php echo Utils::get_recaptcha_key('site_key'); ?>" data-callback="onSubmit" data-action="submit" data-before_submit type="submit"><?php echo Arr::get($cmslabel, 'login'); ?></button></p>
					<p class="auth-links">
						<a class="btn-forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
					</p>
				</form>
			<?php endif; ?>
		</div>

		<div class="a-col a-col2 a-login-col2">
			<?php echo Arr::get($cms_page, 'content'); ?>
			<a class="btn a-btn-signup" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'signup', FALSE); ?>"><span><?php echo Arr::get($cmslabel, 'signup'); ?></span></a>
		</div>	
	</div>	
<?php $this->endblock('content_layout'); ?>

<?php $this->block('loyalty'); ?> <?php $this->endblock('loyalty'); ?>