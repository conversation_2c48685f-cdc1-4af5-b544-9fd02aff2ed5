<div class="table-order table-header table-order-header">
	<div class="table-col col-order-num"><?php echo Arr::get($cmslabel, 'webshop_order_number'); ?></div>
	<div class="table-col col-order-date"><?php echo Arr::get($cmslabel, 'order_date'); ?></div>
	<div class="table-col col-order-status"><?php echo Arr::get($cmslabel, 'status'); ?></div>
	<div class="table-col col-order-total"><?php echo Arr::get($cmslabel, 'total'); ?></div>
	<!--<div class="table-col col-order-terms"></div>-->
	<div class="table-col col-order-btns"></div>
</div>

<?php foreach ($items as $item): ?>
	<div class="order-row clear" id="order-<?php echo $item->number; ?>">
		
		<!-- Order Item -->
		<div class="table-order" onClick="javascript:toggleBox(['div#order-<?php echo $item->number; ?>', 'span#btn-order-<?php echo $item->number; ?>']);">
			<div class="table-col col-order-num"><?php echo $item->number; ?></div>
			<div class="table-col col-order-date"><?php echo Date::humanize($item->datetime_created); ?></div>
			<div class="table-col col-order-status"><?php echo $item->status->title($item->lang); ?></div>
			<div class="table-col col-order-total">
				<strong>
					<?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?>
					<?php echo Utils::get_second_pricetag_string($item->total, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
				</strong>
			</div>
			<!--
			<div class="table-col col-order-terms">
				<?php $terms_pdf = Utils::file_url($item->terms_pdf); ?>
				<?php if($terms_pdf): ?>
					<a class="btn-terms-download" href="<?php echo $terms_pdf; ?>" target="_blank" title="<?php echo Arr::get($cmslabel, 'download_terms_pdf'); ?>"><span class="icon icon-pdf"><?php echo Arr::get($cmslabel, 'download_terms_pdf'); ?></span></a>
				<?php endif; ?>
			</div>
			-->
			<div class="table-col col-order-btns">
				<span class="btn-order-details" id="btn-order-<?php echo $item->number; ?>" href="javascript:toggleBox(['div#order-<?php echo $item->number; ?>', 'a#btn-order-<?php echo $item->number; ?>']);">
					<span class="btn-inactive"><?php echo Arr::get($cmslabel, 'webshop_btn_order_details', 'Prikaži detalje'); ?><span class="toggle-icon"></span></span>
					<span class="btn-active"><?php echo Arr::get($cmslabel, 'webshop_btn_hide_order_details', 'Sakrij detalje'); ?><span class="toggle-icon"></span></span>
				</span>
			</div>
		</div>

		<!-- Order Item Details (subitems) -->
		<?php if ($item->items): ?>
			<?php  $items_qty = Catalog::products(array('lang' => $info['lang'], 'filters' => array('id' => $item->items->as_array(NULL, 'product_id')))); ?>
			<div class="order-details clear">
				<div class="w-table w-table-details">
					<?php foreach ($item->items AS $item_item): ?>
					<div class="wp wp-details">
						<div class="wp-image">
							<figure>
								<img <?php echo Thumb::generate($item_item->main_image, array('width' => 110, 'height' => 110, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-100.jpg')); ?> alt="<?php echo $item_item->title; ?>" <?php if(!empty($items_qty[$item_item->product_id])): ?>data-product_main_image="<?php echo $items_qty[$item_item->product_id]['shopping_cart_code']; ?>"<?php endif; ?> />
							</figure>
						</div>
						<div class="wp-row-col2">
							<h2 class="wp-title" <?php if(!empty($items_qty[$item_item->product_id])): ?>data-product_title="<?php echo $items_qty[$item_item->product_id]['shopping_cart_code']; ?>"<?php endif; ?>><?php echo $item_item->title; ?></h2>
							<div class="wp-cnt">
								<div class="wp-attrs wp-details-attrs">
									<div class="wp-code"><span><?php echo Arr::get($cmslabel, 'code'); ?>:</span> <span <?php if(!empty($items_qty[$item_item->product_id])): ?>data-product_code="<?php echo $items_qty[$item_item->product_id]['shopping_cart_code']; ?>"<?php endif; ?>><?php echo $item_item->code; ?></span></div>
									<?php if($item_item->description): ?>
										<div class="wp-variations"><?php echo $item_item->description; ?></div>
									<?php endif; ?>
								</div>

								<div class="wp-total">
									<div>
										<div class="wp-price">
											<?php if ($item_item->basic_price > $item_item->price): ?>
												<?php /*
												<span class="wp-price-old product_total_basic" <?php if(!empty($items_qty[$item_item->product_id])): ?>data-product_basic_price="<?php echo $items_qty[$item_item->product_id]['shopping_cart_code']; ?>"<?php endif; ?>>
													<?php echo Utils::currency_format($item_item->basic_price * $item->exchange, $item->currency->display); ?>
													<?php echo Utils::get_second_pricetag_string($item_item->basic_price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
												</span>
												*/ ?>
												<span class="wp-price-current wp-price-discount product_total" <?php if(!empty($items_qty[$item_item->product_id])): ?>data-product_price="<?php echo $items_qty[$item_item->product_id]['shopping_cart_code']; ?>"<?php endif; ?>>
													<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?></span>
													<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item_item->price, ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
												</span>
											<?php else: ?>
												<span class="wp-price-current product_total" <?php if(!empty($items_qty[$item_item->product_id])): ?>data-product_price="<?php echo $items_qty[$item_item->product_id]['shopping_cart_code']; ?>"<?php endif; ?>>
													<span data-currency_format="full_price_currency"><?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?></span>
													<span data-currency_format="full_price_currency"><?php echo Utils::get_second_pricetag_string($item_item->price, ['currency_code' => $currency['code'], 'display_format' => 'none']); ?></span>
												</span>
											<?php endif ?>
										</div>
										<div class="wp-qty-count">
											<span class="product_qty"><?php echo $item_item->qty; ?></span> x 
											<?php echo Utils::currency_format($item_item->price * $item->exchange, $item->currency->display); ?>
											<?php echo Utils::get_second_pricetag_string($item_item->price, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
										</div>
									</div>
								</div>
							</div>
						</div>

					</div>
					<?php endforeach; ?>
					<div class="wp-sum">
						<span>
							<?php echo Arr::get($cmslabel, 'total'); ?>: 
							<strong>
								<?php echo Utils::currency_format($item->total * $item->exchange, $item->currency->display); ?>
								<?php echo Utils::get_second_pricetag_string($item->total, ['currency_code' => $currency['code'], 'display_format' => 'standard']); ?>
							</strong>
						</span>
					</div>
				</div>
			</div>
		<?php endif; ?>
	</div>
<?php endforeach; ?>