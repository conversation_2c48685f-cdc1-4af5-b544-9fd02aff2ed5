<?php $this->extend('auth/default'); ?>

<?php $this->block('content2'); ?>
	<form method="post" action="" id="change-password-form" accept-charset="utf-8" enctype="multipart/form-data" name="edit" class="ajax_siteform form-label auth-form auth-change-password-form">
		<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
		<?php foreach ($customer_fields as $field): ?>
			<?php $error = Valid::get_error($field, $message); ?>
			<p class="field field-<?php echo $field; ?>">
				<?php $label = (Arr::get($cmslabel, 'reset_'.$field)) ? Arr::get($cmslabel, 'reset_'.$field) : Arr::get($cmslabel, $field, $field); ?>
				<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo $label; ?></label>
				<?php echo $item->input($field, 'form'); ?>
				<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
			</p>
		<?php endforeach; ?>
		<button type="submit" class="g-recaptcha" data-sitekey="<?php echo Utils::get_recaptcha_key('site_key'); ?>" data-callback="onSubmit" data-action="submit" data-before_submit><?php echo Arr::get($cmslabel, 'save'); ?></button>
	</form>
<?php $this->endblock('content2'); ?>