<?php $this->extend('auth/default'); ?>

<?php $this->block('h1'); ?>
	<h1 class="a-auth-title"><?php echo Arr::get($cmslabel, 'welcome', 'Pozdrav'); ?> <?php echo $user->first_name; ?></h1>
<?php $this->endblock('h1'); ?>

<?php $this->block('error'); ?> <?php $this->endblock('error'); ?>

<?php $this->block('content2'); ?>
<div class="clear a-intro">
	<?php if ($message_type AND $message): ?>
		<?php if (is_array($message)): ?>
			<p class="global-<?php echo $message_type; ?> auth-dashboard-msg"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
		<?php else: ?>
			<p class="global-<?php echo $message_type; ?> auth-dashboard-msg"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}", "{$message_type}_{$message}"); ?></p>
		<?php endif; ?>
	<?php endif; ?>
	
	<div class="a-intro-left">
		<p class="a-intro-title"><?php echo Arr::get($cmslabel, 'you_can'); ?>:</p>
		<ul class="a-menu">
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', false), Arr::get($cmslabel, 'auth_view_orders')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', '', FALSE), Arr::get($cmslabel, 'auth_view_coupons')); ?></li>
			<?php if (Kohana::config('app.catalog.use_wishlists')): ?>
				<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'catalog') .'?special_view=wishlist&'.time(), Arr::get($cmslabel, 'auth_view_wishlist')); ?></li>
			<?php endif; ?>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE), Arr::get($cmslabel, 'auth_edit_profile')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'change_password', FALSE), Arr::get($cmslabel, 'auth_change_password')); ?></li>
			<li><?php echo str_replace("%LINK%", Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE), Arr::get($cmslabel, 'auth_logout')); ?></li>
		</ul>
	</div>

	<div class="a-intro-user">
		<p class="a-intro-title a-intro-user-title"><strong><?php echo $user->first_name; ?> <?php echo $user->last_name; ?></strong></p>
		<p>
			<span class="a-intro-email"><?php echo $user->email; ?></span><br>
			<?php echo $user->phone; ?>
			<?php if($user->address): ?>
				<?php echo $user->address; ?><br>
				<?php echo $user->address . ' ' . $user->house_number; ?><br>
				<?php echo $user->zipcode; ?> <?php echo $user->city; ?>
			<?php endif; ?>
		</p>
		<p><a class="btn btn-medium btn-yellow a-btn-edit" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'edit', FALSE); ?>" class="a-btn-edit"><?php echo Arr::get($cmslabel, 'edit_profile'); ?></a></p>
	</div>
</div>

<!-- Orders -->
<?php $orders = $user->get_webshoporder(TRUE, 5); ?>
<div class="clear auth-box orders-container" id="orders">
	<h2 class="a-section-title"><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'my_webshoporder', FALSE); ?>"><?php echo Arr::get($cmslabel, 'latest_orders'); ?></a></h2>
	<?php echo View::factory('auth/widgetlist/webshoporder', array('items' => $orders, 'dashboard' => true)); ?>
	<a name="coupons"></a>
</div>

<!-- Coupons -->
<div class="clear auth-box auth-box-coupons">
	<h2 class="a-section-title a-section-coupons-title"><?php echo Arr::get($cmslabel, 'coupon_dashboard_title'); ?></h2>
	<div class="a-section-coupons-cnt">
		<?php $coupons = Webshop::coupons(array('lang' => $info['lang'], 'only_my' => TRUE, 'only_active' => FALSE, 'filters' => array('expire_day' => '-30'))); ?>
		<?php echo View::factory('webshop/widgetlist/coupons', array('items' => $coupons, 'dashboard' => TRUE)); ?>	
	</div>
</div>
<?php $this->endblock('content2'); ?>