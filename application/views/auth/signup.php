<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-auth<?php $this->endblock('page_class'); ?>

<?php $this->block('content_layout'); ?>
	<div class="auth-wrapper">	
		<div class="a-col a-col1 a-signup-col1 box-shadow">
			<?php if ($message_type AND $message): ?>
				<?php if (is_array($message)): ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
				<?php else: ?>
					<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
				<?php endif; ?>
			<?php endif; ?>

			<?php if ($message_type != 'success'): ?>	
				<!-- Signup form -->
				<form method="post" action="" id="signup-form" accept-charset="utf-8" enctype="multipart/form-data" name="signup" class="form-label ajax_siteform auth-form auth-signup-form" data-webshop_autocomplete="zipcode_city">
					<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
					<h2 class="a-subtitle a-signup-login"><?php echo Arr::get($cmslabel, 'login_information', 'Informacije za prijavu'); ?></h2>
					<div class="form-wrapper">
						<?php foreach ($customer_fields as $field): ?>
							<?php if($field == 'first_name'): ?>
								</div>
									<h2 class="a-subtitle a-form-title-personal"><?php echo Arr::get($cmslabel, 'personal_information', 'Osobni podaci'); ?></h2>
								<div class="form-wrapper form-wrapper-personal">
							<?php endif; ?>
							<?php if($field == 'loyalty_request'): ?>
								</div>
									<h2 class="a-subtitle a-form-title-loyalty"><?php echo Arr::get($cmslabel, 'signup_loyalty_title'); ?></h2>
								<div class="form-wrapper form-wrapper-loyalty">
							<?php endif; ?>
							<?php if($field == 'newsletter'): ?>
								</div>
								<div class="form-wrapper form-wrapper-other">
							<?php endif; ?>					
							<?php $error = Valid::get_error($field, $message); ?>
							<?php if (in_array($field, ['accept_terms', 'newsletter'])): ?>
								<p class="field field-<?php echo $field; ?>">
									<?php echo $item->input($field, 'form'); ?>
									<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?></label>
									<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
								</p>
							<?php else: ?>
								<p class="field field-<?php echo $field; ?>">
									<?php $l = (Arr::get($cmslabel, $field.'_signup')) ? Arr::get($cmslabel, $field.'_signup') : Arr::get($cmslabel, $field, $item->meta()->field($field)->label); ?>
									<label for="field-<?php echo $field; ?>" class="label-<?php echo $field; ?>"><?php echo $l; ?></label>
									<?php if ($field == 'loyalty_request'): ?>
										<?php $options = Kohana::config('app.loyalty.user_join'); ?>
										<?php foreach ($options AS $option_id => $option_title): ?>
											<span class="field-loyalty field-loyalty_request-<?php echo $option_id; ?>">
												<input type="radio" id="field-loyalty_request-<?php echo $option_id; ?>" name="loyalty_request" value="<?php echo $option_id; ?>" <?php if ($option_id == 'n'): ?>checked="checked"<?php endif; ?>>
												<label for="field-loyalty_request-<?php echo $option_id; ?>"><?php echo Arr::get($cmslabel, $field.'_'.$option_id, $option_title); ?></label>
												<span class="note">
													<?php echo Arr::get($cmslabel, $field.'_'.$option_id.'_note'); ?>
													<?php if ($option_id == 'e'): ?>
														<label for="field-loyalty_code"><?php echo Arr::get($cmslabel, 'loyalty_code'); ?></label>
														<?php echo $item->input('loyalty_code', 'form'); ?>
														<span id="field-error-loyalty_code" class="field_error error" style="display: none"></span>
													<?php endif; ?>
												</span>
											</span>
										<?php endforeach; ?>
									<?php else: ?>
										<?php echo $item->input($field, 'form'); ?>
									<?php endif; ?>
									<span id="field-error-<?php echo $field; ?>" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></span>
								</p>
							<?php endif; ?>
						<?php endforeach; ?>
						<button class="btn btn-gray btn-signup g-recaptcha" data-sitekey="<?php echo Utils::get_recaptcha_key('site_key'); ?>" data-callback="onSubmit" data-action="submit" data-before_submit type="submit"><?php echo Arr::get($cmslabel, 'signup_confirm'); ?></button>
					</div>
				</form>
				<!-- / Signup form -->
			<?php endif; ?>
		</div>

		<div class="a-col a-col2 a-signup-col2">
			<?php echo Arr::get($cms_page, 'content'); ?>
			<a class="btn a-btn-signup" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo Utils::app_absolute_url($info['lang'], 'auth', '', false); ?>"><span><?php echo Arr::get($cmslabel, 'login'); ?></span></a>
			<p class="auth-links">
				<a class="btn-forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1"><?php echo Arr::get($cmslabel, 'forgotten_password'); ?></a>
			</p>
		</div>
	</div>
<?php $this->endblock('content_layout'); ?>