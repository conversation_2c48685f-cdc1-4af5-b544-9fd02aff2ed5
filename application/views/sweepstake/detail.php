<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> sweepstake-<?php echo $item['code']; ?><?php $this->endblock('page_class'); ?>
	
<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('content_layout'); ?>
	<h1 class="wrapper sws-title"><?php echo $item['seo_h1']; ?></h1>

	<?php if(!empty($item['questions'])): ?>
		<div class="sweepstake-pager-cnt">
			<div class="wrapper">
				<ul class="sweepstake-pager">
					<?php $p = 0; ?>
					<?php foreach ($item['questions'] as $question): ?>
						<li class="sweepstake-pager-item<?php echo $p; ?><?php if($p == 0): ?> active<?php endif; ?>"><span><?php echo $p + 1; ?></span></li>
						<?php $p++; ?>
					<?php endforeach; ?>
				</ul>
				<a class="btn-sweepstake-cancel" href="<?php echo $info['url']; ?>"><?php echo Arr::get($cmslabel, 'sweepstake_cancel', 'Poništi sve odgovore'); ?></a>
			</div>
		</div>
	<?php endif; ?>	

	<div class="sweepstake-pages">
		<?php if ($can_vote AND Arr::get($_GET, 'successcode') !== 'ok'): ?>
			<form action="<?php echo $item['vote_url']; ?>" method="POST" class="kontext ajax_siteform" data-siteform_error_method="sweepstakeFormValidation">
				<?php $i = 0; ?>
				<?php foreach ($item['questions'] AS $question_id => $question): ?>
					<div class="pos-r clear sweepstake-page layer layer<?php echo $i; ?><?php if($i == 0): ?> show<?php endif; ?>" data-question_id="<?php echo $question_id; ?>" data-question_layer="<?php echo $i; ?>">
						<div class="wrapper wrapper-sweepstake">
							<div class="sweepstake-fields">
								<h2 class="sweepstake-title"><?php echo $question['title']; ?></h2>
								<?php if (!empty($question['tips'])): ?><div class="sweepstake-subtitle"><?php echo $question['tips']; ?></div><?php endif; ?>
								<?php $error = (!empty($errors["question_{$question_id}"][0])) ? $errors["question_{$question_id}"][0] : ''; ?>
								<div id="field-error-<?php echo "question_{$question_id}"; ?>" class="field_error error sweepstake-error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, "error_{$error}", $error); ?></div>
								<div class="sweepstake-field"
								<?php
								if (!empty($question['answers'])) {
									foreach ($question['answers'] AS $question_answer_id => $question_answer) {
										if (!empty($question_answer['next_question'])) {
											echo ' data-next_question_'.$question_answer_id.'="'.$question_answer['next_question'].'"';
										}
									}
								}
								?>
								>

									<?php if ($question['type'] == 'input'): ?>
										<div class="sweepstake-input-field">
											<?php echo Form::input("question_{$question_id}", Arr::get($_POST, "question_{$question_id}", '')); ?>
										</div>
									<?php elseif ($question['type'] == 'selectm'): ?>
										<?php echo Form::select_as_checkbox("question_{$question_id}", $question['answers_option'], Arr::get($_POST, "question_{$question_id}", '')); ?>
									<?php elseif ($question['type'] == 'text'): ?>
										<?php echo Form::textarea("question_{$question_id}", Arr::get($_POST, "question_{$question_id}", '')); ?>
									<?php else: ?>
										<?php //echo Form::select_as_radio2("question_{$question_id}", $question['answers_option'], Arr::get($_POST, "question_{$question_id}", '')); ?>
										<?php if (!empty($question['answers'])): ?>
											<?php foreach ($question['answers'] AS $answer): ?>
												<span class="clear answer answer-<?php echo $answer['id']; ?>">
													<?php if(!empty($answer['main_image'])): ?>
														<img <?php echo Thumb::generate($answer['main_image'], array('width' => 90, 'height' => 60, 'crop' => true, 'default_image' => '/media/images/no-image-50.jpg', 'html_tag' => true)); ?> alt="" />
													<?php endif; ?>
													<input type="radio" id="question_<?php echo $question['id']; ?>-<?php echo $answer['id']; ?>" name="question_<?php echo $question['id']; ?>" value="<?php echo $answer['id']; ?>">
													<label for="question_<?php echo $question['id']; ?>-<?php echo $answer['id']; ?>"><?php echo $answer['title']; ?></label>
												</span>
											<?php endforeach; ?>
										<?php endif; ?>												
									<?php endif; ?>
								</div>
								<?php if (!empty($question['use_comment'])): ?>
									<textarea name="questioncomment_<?php echo $question_id; ?>" cols="30" rows="10" placeholder="<?php echo Arr::get($cmslabel, 'comment'); ?>"></textarea>
								<?php endif; ?>
							</div>
						</div>
							
						<div class="wrapper sweepstake-buttons">
							<?php if ($i < count($item['questions'])-1): ?>
								<button type="submit" class="btn btn-sweepstake btn-sweepstake-next"><span><?php echo Arr::get($cmslabel, 'next_question', 'Sljedeće pitanje'); ?></span></button>
							<?php else: ?>
								<?php $label = (Arr::get($cmslabel, 'sweepstake_vote_'.$item['code'])) ? Arr::get($cmslabel, 'sweepstake_vote_'.$item['code']) : Arr::get($cmslabel, 'sweepstake_vote', 'Prikaži rezultate'); ?>
								<button type="submit" class="btn btn-sweepstake btn-sweepstake-next"><span><?php echo $label; ?></span></button>
							<?php endif; ?>
							<a href="javascript:;" class="btn btn-sweepstake btn-sweepstake-<?php if ($i == 0): ?>disabled<?php else: ?>prev<?php endif; ?>"><span><?php echo Arr::get($cmslabel, 'prev_question', 'Prethodno pitanje'); ?></span></a>
						</div>
					</div>
					<?php $i++; ?>
				<?php endforeach; ?>
			</form>
		<?php else: ?>
			<div class="form-pages">
				<div class="form-wrapper form-top sweepstake-thank-you">
					<?php echo Arr::get($cmslabel, 'already_vote'); ?>
				</div>
			</div>
		<?php endif; ?>
	</div>
<?php $this->endblock('content_layout'); ?>

<?php $this->block('newsletter'); ?> <?php $this->endblock('newsletter'); ?>

<?php $this->block('extrabody'); ?>
	<?php echo Html::media('js_gkontext'); ?>
	<script>
		var k = kontext(document.querySelector('.kontext')),
			bulletsContainer = $('.sweepstake-pager');

		// Create one bullet per layer
		/*
		for( var i = 0, len = k.getTotal(); i < len; i++ ) {
			var bullet = document.createElement( 'li' );
			bullet.className = i === 0 ? 'active' : '';
			bullet.setAttribute( 'index', i );
			var page = i+1;
			bulletsContainer.append('<li>'+i+'</li>');
		}
		*/

		// Update the bullets when the layer changes
		k.changed.add( function( layer, index ) {
			var bullets = document.body.querySelectorAll( '.sweepstake-pager li' );
			for( var i = 0, len = bullets.length; i < len; i++ ) {
				bullets[i].className = i === index ? 'active' : '';
			}
		} );
	</script>
<?php $this->endblock('extrabody'); ?>