<template>
	<BaseCmsLabel code="comments" class="comments-title" tag="div" />

	<BaseCmsLabel code="comments_subtitle" class="comments-subtitle" tag="div" />

	<div id="comment_form" class="clear comment-form-container">
		<BaseFeedbackCommentsForm class="clear form-label form-animated-label comment-form" id="comment_form" :parent-comment-id="props.parentCommentId" v-slot="{fields, onReset, status}">
			<template v-if="!status?.success">
				<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
					<BaseFormInput v-if="item.type == 'hidden'" />
					<p v-else class="field comment-field" :class="['comment-field-' + item.name, {'ffl-floated': floatingLabel && item.name != 'rate', 'hidden': item.type == 'hidden'}]">
						<span v-if="item.name == 'rate'" class="label"><BaseCmsLabel code="your_rate" /></span>
						<BaseFormInput :id="props.parentCommentId ? `comment-${item.name}-${props.parentCommentId}` : `comment-${item.name}`" />
						<BaseCmsLabel v-if="item.name != 'rate'" tag="label" :for="props.parentCommentId ? `comment-${item.name}-${props.parentCommentId}` : `comment-${item.name}`" :code="labels.get('form_comments_' + item.name) ?  labels.get('form_comments_'+item.name) : item.name" />
						<span class="error" v-show="errorMessage" v-html="errorMessage" />
					</p>
				</BaseFormField>
				<div class="comment-form-button">
					<div class="buttons comment-buttons">
						<button class="btn btn-yellow btn-send-comment g-recaptcha" type="submit"><BaseCmsLabel tag="span" code="send_comment" /></button>
					</div>
					<BaseCmsLabel code="comment_form_note" class="comment-form-note" tag="div" />
				</div>
			</template>

			<div class="comment_success" v-if="status?.success">
				<div class="comment-success-message" v-html="labels.get('comment_success')"></div>
				<a class="btn btn-yellow" id="comment_add_new" href="javascript:void(0);" @click="onReset"><BaseCmsLabel code="comment_add_new" /></a>
			</div>
		</BaseFeedbackCommentsForm>
	</div>
	
	<template v-if="item.feedback_comment_widget.comments > 0">
		<BaseFeedbackComments :items="item?.feedback_comment_widget?.items ? item?.feedback_comment_widget?.items : {}" v-slot="{items: commentsPublish}">
			<div id="comment-list-items">
				<template v-if="item.feedback_comment_widget.comments > 0">
					<FeedbackCommentEntry v-for="comment in commentsPublish" :key="comment.id" :item="comment" :mode="[mode]" />	
				</template>
			</div>
		</BaseFeedbackComments>
	</template>
	<BaseCmsLabel v-else code="no_comments" class="no-comments" tag="div" />
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item', 'mode']);
</script>

<style scoped lang="less">
</style>