<template>
	<div class="cd-not-available" >
		<ClientOnly>
			<BaseCmsLabel code="product_not_available_header" class="cd-not-available-header" tag="div" />
			<BaseCmsLabel code="product_not_available_subheader" class="cd-not-available-subheader" tag="div" />
			<div class="cd-notifyme-form">
				<BaseFeedbackNotificationForm class="form-label cd-form-notifyme form-animated-label" v-slot="{fields, status, loading}">
					<div class="cd-form-notifyme-cnt" v-if="!status?.success">
						<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel}">
							<div class="field" v-if="field.type != 'hidden'" :class="{'ffl-floated': floatingLabel}">
								<BaseFormInput :id="`notification-${field.name}`" />
								<label :for="`notification-${field.name}`"><BaseCmsLabel code="nw_enter_email" /></label>
								<span class="error" v-show="errorMessage" v-html="errorMessage" />
							</div>
							<BaseFormInput v-else :id="`notification-${field.name}`" :value="productId" />
						</BaseFormField>
						<button :disabled="loading" class="btn btn-green btn-notifyme-form" type="submit"><BaseCmsLabel code="notifyme" /></button>
					</div>
					<div class="notifyme-success" v-show="status?.success">
						<BaseCmsLabel code="notifyme_catalog_ty" class="notifyme-success-content" tag="div" />
					</div>
				</BaseFeedbackNotificationForm>
			</div>
		</ClientOnly>
	</div>
</template>

<script setup>
	const props = defineProps(['item', 'status']);
	const productId = computed(() => (props.item?.widget_code) ? props.item.widget_code.replace(/_/g, "-") : null);
</script>

<style lang="less" scoped>
	.cd-not-available{
		position: relative; display: block; padding-top: 26px;
		@media (max-width: @tp){padding-top: 15px;}
		@media (max-width: @m){padding-top: 15px;}
	}
	.cd-not-available-header{
		display: inline-block; margin-bottom: 10px; font-size: 24px; line-height: 32px; color: var(--white); font-weight: bold; padding: 13px 24px 11px; background: var(--red);
		@media (max-width: @t){font-size: 16px; line-height: 20px;}
		@media (max-width: @tp){padding: 13px 20px 11px; margin-bottom: 8px;}
		@media (max-width: @m){font-size: 20px; line-height: 24px; text-align: left; width: 100%;}

	}
	.cd-not-available-subheader{
		font-size: 16px; line-height: 19px; padding-bottom: 16px;
		@media (max-width: @t){font-size: 15px;}
		@media (max-width: @tp){font-size: 14px; padding-bottom: 10px;}
	}
	
	:deep(.cd-form-notifyme-cnt){
		position: relative; display: flex; align-items: center; width: 100%;
		.field{
			width: 100%; padding-bottom: 0;
			&.ffl-floated{
				label{font-size: 12px; top: 6px;}
			}
			input{padding-right: 170px; width: 100%; height: 65px; font-size: 18px;}
			label{font-size: 18px; top: 25px;}
			@media (max-width: @t){
				input{height: 55px; font-size: 16px; padding-right: 160px;}
				label{font-size: 16px; top: 19px;}
			}
			@media (max-width: @tp){
				input{height: 48px; font-size: 14px; padding-right: 120px;}
				label{font-size: 14px; top: 15px;}
			}
		}
		
	}
	.btn-notifyme-form{
		position: absolute; top: 0px; right: 0px; width: 164px; height: 65px; padding: 0; margin: 0; min-width: 160px;
		@media (max-width: @t){height: 55px; width: 155px;}
		@media (max-width: @tp){width: 116px; min-width: 116px; height: 48px; font-size: 14px; line-height: 48px;}
	}
	.notifyme-success{width: 100%; color: var(--green);}
</style>