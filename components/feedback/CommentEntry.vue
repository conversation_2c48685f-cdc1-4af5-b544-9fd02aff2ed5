<template>
	<article class="comment" :class="[mode, {'comment-manager': Number(item.manager) == 1}, {'comment-child': Number(item.parent_id) != 0}]">
		<div class="comment-col comment-content">
			<div class="comment-header">
				<span class="comment-rate fz0" v-if="mode != 'publish'">
					<span v-for="i in 5" :key="i" class="icon-star-empty" :class="{'icon-star': i <= item.rate}"></span>
				</span>
				<span class="comment-username">{{ item.display_name }}</span> - <span class="comment-date"><BaseUtilsFormatDate :date="item.datetime_created" format="DD.MM.YYYY." /></span>
			</div>
			<div class="comment-message">{{ item.message }}</div>
		</div>
	</article>
</template>

<script setup>
	const props = defineProps(['item', 'mode']);
	const {getUser} = useAuth();
	const activeCommentForm = ref(false);
	const canReplay = computed(() => {
		const user = getUser();
		return (Number(props.item.parent_id) == 0 && (user?.staff || user?.superuser || user?.developer));
	})
</script>

<style lang="less" scoped>
</style>
