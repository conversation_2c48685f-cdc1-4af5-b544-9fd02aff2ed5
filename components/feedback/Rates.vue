<template>
	<div class="cp-rate add_rate" :class="[mode]">
		<template v-for="item in 5" :key="item.id">
			<span class="icon-star-empty" :class="{'icon-star': item <= rate}"></span>
		</template>
	</div>
</template>

<script setup>
	const props = defineProps(['rates','mode']);
	let rate = ref(null);
	onMounted(() => {
		rate.value = Math.round(props.rates);
	});
</script>

<style lang="less" scoped>
</style>
