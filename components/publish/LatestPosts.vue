<template>
	<BasePublishCategoriesWidget :fetch="{code: 'blog', response_fields: ['code', 'url_without_domain']}" v-slot="{items: categories}">
		<BasePublishPostsWidget v-if="categories?.length" :fetch="{category_code: categories[0].code, limit: 6, response_fields: ['id', 'title', 'short_description', 'url_without_domain', 'main_image_thumbs'], extra_fields: ['short_description']}" v-slot="{items}">
			<div class="pw pw-hp" v-if="items?.length">
				<div class="wrapper">
                    <div class="pw-title"><NuxtLink :to="categories[0].url_without_domain"><span v-html="labels.get('special_articles')"></span></NuxtLink></div>               
                    <ClientOnly>
                        <div class="p-items hp-p-items">
                            <template v-if="!mobileBreakpoint">
                                <div v-if="items?.length >= 1" class="p-featured">
                                    <PublishIndexEntry v-for="(post, index) in items.slice(0, 3)" :key="post.id" :item="post" :index="index" />
                                </div>
                                <PublishIndexEntry v-for="(post, index) in items.slice(3)" :key="post.id" :item="post" :index="index + 3" />
                            </template>
                            <template v-else>
                                <PublishIndexEntry v-for="(post, index) in items" :key="post.id" :item="post" :index="index + 3" />
                            </template>
                        </div>
                    </ClientOnly>
                    <div class="pw-btns"><NuxtLink :to="categories[0].url_without_domain" class="btn btn-yellow btn-all"><BaseCmsLabel code="all_articles" /></NuxtLink></div>
                </div>
			</div>
		</BasePublishPostsWidget>
	</BasePublishCategoriesWidget>
</template>

<script setup>
    const labels = useLabels();
	const {onMediaQuery} = useDom();

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 750px)',
	});        
</script>