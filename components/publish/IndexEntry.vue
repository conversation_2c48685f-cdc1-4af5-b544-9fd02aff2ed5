<template>
	<NuxtLink :to="item.url_without_domain" :class="['pp', {'pp-featured': index == 0}, {'pp-featured-small': index == 1 || index == 2}, mode]">
		<figure class="pp-image">
			<span>
				<BaseUiImage loading="lazy" :data="imageOptions.image" :default="imageOptions.noImage" :title="item.main_image_title" :alt="item.main_image_description" />
			</span>
		</figure>
		<div class="pp-cnt">
			<h2 v-if="item.title" class="pp-title">{{item.title}}</h2>
			<div v-if="item.short_description && index == 0" class="pp-short-desc">
				{{stripHtml(limitWords(item.short_description, 500, '...'))}}
			</div>
		</div>
	</NuxtLink>
</template>

<script setup>
	const props = defineProps({
		item: Object,
		index: Number,
		mode: String,
	})
	const {stripHtml, limitWords} = useText();
	const imageOptions = computed(() => {
		if(props.index == 0) {
			return {image: props.item?.main_image_thumbs?.['width920-height480-crop1'], noImage: '/images/no-image-920.jpg'};
		}
		return {image: props.item?.main_image_thumbs?.['width435-height230-crop1'], noImage: '/images/no-image-435.jpg'};
	})
</script>

<style lang="less" scoped>
	.pp.landing{
		width: 100%; margin: 0;
	}
</style>