<template>
	<div class="wp" :id="'product_details_'+data.shopping_cart_code">
		<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}">
			<div class="wp-image">
				<NuxtLink :to="data.item.url_without_domain">
					<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width100-height100']" default="/images/no-image-100.jpg" :alt="data.item.title" />
				</NuxtLink>
				<div class="wp-btn-delete wp-btn-delete-m" :class="{'loading': loading}" @click="onRemove"><BaseCmsLabel code="remove_product" tag="span" /></div>
			</div>
			<div class="wp-row-col2">
				<h2 class="wp-title">
					<NuxtLink :to="data.item.url_without_domain">{{ data.item.title }}</NuxtLink>
					<span v-if="data.item?.attributes" class="wp-attribute">{{ data.item.attributes }}</span>
				</h2>
				<div class="wp-attrs mobile">
					<div class="wp-title-container">
						<div class="wp-code">{{ (data.item.variation?.code) ? data.item.variation.code : data.item.code }}</div>

						<template v-if="data.item.coupon_price">
							<BaseCmsLabel code="coupon_value" />: <BaseUtilsFormatCurrency :price="data.item.coupon_price" />
						</template>
						<template v-else-if="data.item.bonus_total">
							<BaseCmsLabel code="bonus_total_value" />: <BaseUtilsFormatCurrency :price="data.item.bonus_total" />
						</template>
						<template v-else-if="data.item.type && data.item.type == 'download'">
							<BaseCmsLabel code="download_files" />
						</template>
					</div>

					<div class="wp-btn-delete wp-btn-delete-d" :class="{'loading': loading}" @click="onRemove"><BaseCmsLabel code="remove_product" tag="span" /></div>
				</div>
				<div class="wp-cnt">
					<div class="wp-attrs">
						<div class="wp-title-container">
							<div class="wp-code">{{ (data.item.variation?.code) ? data.item.variation.code : data.item.code }}</div>

							<template v-if="data.item.coupon_price">
								<BaseCmsLabel code="coupon_value" />: <BaseUtilsFormatCurrency :price="data.item.coupon_price" />
							</template>
							<template v-else-if="data.item.bonus_total">
								<BaseCmsLabel code="bonus_total_value" />: <BaseUtilsFormatCurrency :price="data.item.bonus_total" />
							</template>
							<template v-else-if="data.item.type && data.item.type == 'download'">
								<BaseCmsLabel code="download_files" />
							</template>
						</div>

						<div class="wp-btn-delete wp-btn-delete-d" :class="{'loading': loading}" @click="onRemove"><BaseCmsLabel code="remove_product" tag="span" /></div>
					</div>

					<BaseWebshopQty :quantity="data.quantity" :item="data" mode="cart" v-slot="{loading, status, onDecrement, onIncrement, onUpdate, onReset, quantity}">
						<div class="wp-qty-container" :class="{'loading': loading}">
							<div class="cd-qty wp-qty">
								<span class="btn-qty btn-qty-dec" @click="onDecrement">-</span>
								<input class="qty-input product_qty_input" type="text" :value="quantity" @keyup="onUpdate" @blur="onReset" />
								<span class="btn-qty btn-qty-inc" @click="onIncrement">+</span>
								<div class="wp-unit" v-if="data?.unit">{{data.unit}}</div>
								<div class="wp-unit" v-else><BaseCmsLabel code="unit" /></div>
								<span v-show="status" class="wp-message product_message" :class="{'product_message_response_error': status?.data?.label_name == 'error_limitqty'}"><BaseCmsLabel :code="status" /></span>
							</div>
						</div>
					</BaseWebshopQty>
					
					<div class="wp-total" :class="[{'has-discount': data.total_basic > data.total}, { 'no-energy-badge': !data.item.attributes_special?.find(attr => attr.attribute_code === 'energetski-razred') }]">
						<div>
							<div :class="['wp-price-current', (data.total_basic > data.total ? 'red' : '')]"><span class="product_total"><BaseUtilsFormatCurrency :wrap="true" :price="data.total" /></span></div>
							<div class="wp-qty-count" v-if="data.quantity > 1">
								<span class="product_qty">{{ data.quantity }}</span> x <BaseUtilsFormatCurrency :price="data.unit_price" />
							</div>
						</div>
 
						<CatalogEnergyBadge :item="data.item" modeClass="wp-attr-energy" />
					</div>
				</div>
			</div>
		</BaseWebshopRemoveProduct>
	</div>
</template>

<script setup>
	const props = defineProps(['data', 'mode']);
</script>

<style lang="less" scoped>
	.wp-attrs.mobile{display: none;}
	.wp-attrs{
		@media (max-width: @m){
			display: none;
			&.mobile{display: block;}
		}
	}
</style>
