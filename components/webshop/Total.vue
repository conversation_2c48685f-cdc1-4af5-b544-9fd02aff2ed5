<template>
	<div class="cart-totals" :class="simple && 'ww-totals'">
		<BaseWebshopTotal v-slot="{items}">
			<template v-if="items">
				<div class="cart-total-without-discount cart_info_total_items_basic_without_discount_box" v-show="items.total_items_basic_without_discount">
					<span class="w-totals-label"><BaseCmsLabel code="total_minus_discount" default="Ukupno bez popusta" />:</span>
					<span class="w-totals-value cart_info_total_items_basic_without_discount"><BaseUtilsFormatCurrency :price="items.total_items_basic_without_discount" /></span>
				</div>
				<div class="cart-total-discount cart_info_total_items_basic_discount_box" v-if="items.total_items_basic_discount">
					<span class="w-totals-label"><BaseCmsLabel code="total_discount" default="Popust" />:</span>
					<span class="w-totals-value cart_info_total_items_basic_discount"><BaseUtilsFormatCurrency :price="items.total_items_basic_discount" /></span>
				</div>
				<div class="cart-total-without-tax" v-if="items.total_items_basic">
					<span class="w-totals-label"><BaseCmsLabel code="total_minus_tax" default="Osnovica za obračun PDV-a" />:</span>
					<span class="w-totals-value cart_info_total_items_basic"><BaseUtilsFormatCurrency :price="items.total_items_basic" /></span>
					<small v-if="items.total_basic_taxranks_description" class="cart_info_total_basic_taxranks_description" v-html="items.total_basic_taxranks_description" />
				</div>
				<div class="cart-total-tax" v-if="items.total_items_tax">
					<span class="w-totals-label">
						<BaseCmsLabel code="total_tax" default="Ukupan PDV" />
						<template v-if="items.tax_percentage"> ({{items.tax_percentage}}%)</template>:
					</span>
					<span class="w-totals-value cart_info_total_items_tax"><BaseUtilsFormatCurrency :price="items.total_items_tax" /></span>
					<small v-if="items.total_tax_taxranks_description" class="cart_info_total_tax_taxranks_description" v-html="items.total_tax_taxranks_description" />
				</div>
				<div class="cart_info_total_extra_extraitem_priority_order_box" v-if="(priorityItem = items.extraitems.find(el => el.type == 'extraitem_priority_order' && el.total > 0))">
					<span class="w-totals-label"><BaseCmsLabel code="priority_order_title" default="Prioritetna narudžba" />:</span>
					<span class="w-totals-value cart_info_total_extra_extraitem_priority_order"><BaseUtilsFormatCurrency :price="priorityItem.total" /></span>
				</div>
				<div class="cart-total-without-shipping" v-if="items.total_items_total">
					<span class="w-totals-label"><BaseCmsLabel code="total_minus_shipping" default="Ukupno bez dostave" />:</span>
					<span class="w-totals-value value cart_info_total_items_total"> <BaseUtilsFormatCurrency :price="items.total_items_total" /> </span>
				</div>
				<div class="total_extra_payment_box" v-if="items.total_extra_payment">
					<span class="w-totals-label"><BaseCmsLabel code="additional_payment" default="Dodatna naplata" />:</span>
					<span class="w-totals-value total_extra_payment"><BaseUtilsFormatCurrency :price="items.total_extra_payment" /></span>
				</div>
				<div class="cart_info_total_extra_discount_box" v-if="items.total_extra_discount">
					<span class="w-totals-label"><BaseCmsLabel code="discount" default="Popust" />:</span>
					<span class="w-totals-value cart_info_total_extra_discount"><BaseUtilsFormatCurrency :price="items.total_extra_discount" /></span>
				</div>
				<div class="cart_info_total_extra_coupon_box" v-if="items.total_extra_coupon">
					<span class="w-totals-label"><BaseCmsLabel code="coupon" default="Kupon" />:</span>
					<span class="w-totals-value cart_info_total_extra_coupon"> <BaseUtilsFormatCurrency :price="items.total_extra_coupon" /> </span>
				</div>
				<div class="cart_info_total_extra_coupon_product_box" v-if="items.total_extra_coupon_product">
					<span class="w-totals-label"><BaseCmsLabel code="coupon_product" default="Kupon" />:</span>
					<span class="w-totals-value cart_info_total_extra_coupon_product"><BaseUtilsFormatCurrency :price="items.total_extra_coupon_product" /></span>
				</div>
				<div class="cart_info_total_extra_affiliate_box" v-if="items.total_extra_affiliate">
					<span class="w-totals-label"><BaseCmsLabel code="affiliate" />:</span>
					<span class="w-totals-value cart_info_total_extra_affiliate"><BaseUtilsFormatCurrency :price="items.total_extra_affiliate" /></span>
				</div>
				<div class="cart_info_total_extra_abandonment_box" v-if="items.total_extra_abandonment">
					<span class="w-totals-label"><BaseCmsLabel code="abandonment" />:</span>
					<span class="w-totals-value cart_info_total_extra_abandonment"><BaseUtilsFormatCurrency :price="items.total_extra_abandonment" /></span>
				</div>
				<div class="cart_info_total_extra_loyalty_box" v-if="items.total_extra_loyalty">
					<span class="w-totals-label">
						<BaseCmsLabel code="loyalty" default="Kartica kupca" />
						<span class="cart_info_total_extra_loyalty_discount_percent">{{' '+items.total_extra_loyalty_discount_percent}}</span>%:
					</span>
					<span class="w-totals-value cart_info_total_extra_loyalty"><BaseUtilsFormatCurrency :price="items.total_extra_loyalty" /></span>
				</div>
				<div class="cart_info_total_extra_cc_discount_box" v-if="items.total_extra_cc_discount">
					<span class="w-totals-label"><BaseCmsLabel code="cc_discount" />:</span>
					<span class="w-totals-value cart_info_total_extra_cc_discount"><BaseUtilsFormatCurrency :price="items.total_extra_cc_discount" /></span>
				</div>
				<div class="cart_info_total_extra_cover_box" v-if="items.total_extra_cover">
					<span class="w-totals-label"><BaseCmsLabel code="cover" />:</span>
					<span class="w-totals-value cart_info_total_extra_cover"><BaseUtilsFormatCurrency :price="items.total_extra_cover" /></span>
				</div>
				<div class="cart_info_total_extra_relatedlist_box" v-if="items.total_extra_relatedlist">
					<span class="w-totals-label"><BaseCmsLabel code="relatedlist" />:</span>
					<span class="w-totals-value cart_info_total_extra_relatedlist"><BaseUtilsFormatCurrency :price="items.total_extra_relatedlist" /></span>
				</div>
				<div class="cart_info_total_extra_supplement_box" v-if="items.total_extra_supplement">
					<span class="w-totals-label"><BaseCmsLabel code="supplement" />:</span>
					<span class="w-totals-value cart_info_total_extra_supplement"><BaseUtilsFormatCurrency :price="items.total_extra_supplement" /></span>
				</div>
				<div class="cart-total-shipping">
					<span class="w-totals-label"><BaseCmsLabel code="shipping" default="Dostava" />:</span>
					<span class="w-totals-value cart_info_total_extra_shipping">
						<template v-if="items.total_extra_shipping == 0">
							<BaseCmsLabel code="free" class="w-totals-label-free" tag="span" />
						</template>
						<template v-else>
							<BaseUtilsFormatCurrency :price="items.total_extra_shipping" />
						</template>
					</span>
				</div>
				<div class="ww-total cart-total" v-if="items.total">
					<span class="w-totals-label"><BaseCmsLabel code="total_to_pay" default="Sveukupno" />:</span>
					<span class="w-totals-value value cart_info_total"><BaseUtilsFormatCurrency :price="items.total" /></span>
				</div>
			</template>
		</BaseWebshopTotal>
	</div>
</template>

<script setup>
	const props = defineProps(['simple', 'mode']);
</script>

<style lang="less" scoped>
</style>
