<template>
	
</template>

<script setup>
	const props = defineProps(['cart', 'step']);
	const {gtmTrack} = useGtm();

	const itemsCart = [];

	onMounted(() => {
		props.cart.parcels[0].items.forEach(item => {
			itemsCart.push({
				id: item.item.id ? item.item.id : '',
				name: item.item.title ? item.item.title : '',
				price: item.total ? item.total : '',
				category: item.item.category.title ? item.item.category.title : '',
				brand: item.item.manufacturer.title ? item.item.manufacturer.title : '',
				quantity: item.quantity ? item.quantity : ''
			});
		});

		gtmTrack('checkout', {
			ecommerce: {
				checkout: {
					actionField: {option: props.cart.cart.token_id, step: props.step},
					products: itemsCart,
				}
			}
		}, {custom: true})
	});
</script>