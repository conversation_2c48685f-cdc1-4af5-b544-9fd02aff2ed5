<template>
	
		<div class="step" :class="[{'current-step': title == true},{'step1': step === 1},{'step2': step === 2},{'step3': step === 3},{'completed-step': completed == true}]">
			<template v-if="title === true">
				<BaseCmsLabel tag="span" :code="'step'+step" class="label" />
			</template>
			<template v-else>
				<BaseUtilsAppUrls v-slot="{items: appUrls}">
					<template v-if="step === 1">
						<NuxtLink class="step_link" :to="appUrls.webshop_customer">
							<BaseCmsLabel tag="span" code="step1" class="label" />
							<!-- <BaseCmsLabel v-if="completed == true" tag="span" class="change-step" code="change_edit_step" /> -->
						</NuxtLink>
					</template>
					<template v-if="step === 2">
						<NuxtLink class="step_link" :to="appUrls.webshop_customer">
							<BaseCmsLabel tag="span" code="step2" class="label" />
							<!-- <BaseCmsLabel v-if="completed == true" tag="span" class="change-step" code="change_edit_step" /> -->
						</NuxtLink>
					</template>
					<template v-if="step === 3">
						<NuxtLink class="step_link" :to="appUrls.webshop_payment">
							<BaseCmsLabel tag="span" code="step3" class="label" />
							<!-- <BaseCmsLabel v-if="completed == true" tag="span" class="change-step" code="change_edit_step" /> -->
						</NuxtLink>
					</template>
				</BaseUtilsAppUrls>
			</template>
		</div>
</template>

<script setup>
	const props = defineProps({
		step: {
			type: null,
			default: false,
		},
		completed:{
			type: Boolean,
			default: false,
		},
		title:{
			type: Boolean,
			default: false,
		},
	});
</script>

<style lang="less" scoped>
</style>
