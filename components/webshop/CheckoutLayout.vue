<template>
	<div class="checkout-layout">
	<Body class="page-checkout" />
		<ClientOnly>
			<BaseWebshopCheckout>
				<div class="wc-header">
					<div class="wc-wrapper">
						<BaseCmsLogo class="logo wc-logo" id="logo" />
					</div>
				</div>

				<main class="main">
					<div class="wrapper main-wrapper">
						<div class="wc-container">
							<div class="w-sidebar-container">
								<div class="w-sidebar-support">
									<BaseCmsLabel code="help_and_support_title" class="sidebar-help-title" tag="p" />
									<BaseCmsLabel code="help_and_support" tag="div" />
									<BaseCmsLabel code="cards" class="w-sidebar-cards" tag="div" />
								</div>

								<div class="w-sidebar-note">
									<BaseCmsLabel code="safe_purchase" tag="div" />
									<BaseCmsLabel code="payment_note" class="w-sidebar-payment-note" tag="div" />
								</div>
							</div>

							<slot name="wcCol1" />
						</div>
					</div>
				</main>
			</BaseWebshopCheckout>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</div>
</template>

<script setup>
	const activeCart = ref(false);
	const labels = useLabels();

</script>

<style lang="less" scoped>
	.main-wrapper{width: 600px; padding: 0 0 40px; margin: auto;}
	.wc-container{width: 100%; position: relative;}
	.wc-header{background: var(--gray); height: 85px; color: #fff;}
	.wc-wrapper{width: 600px; margin: auto; position: relative;}
	.logo{background: url(assets/images/logo-white.svg) no-repeat left top; background-size: contain; width: 90px; height: 60px; left: 0; top: 13px;}
	:deep(.w-sidebar-support){
		position: absolute; left: -300px; width: 250px; text-align: center; top: 50px;
		a{text-decoration: none;}
		p{padding: 0;}
		.sidebar-help-title{padding: 0 0 10px;}
	}
	.w-sidebar-cards{max-width: 190px; margin: 35px auto 0;}
	.w-exchange-rate{padding: 15px 0 0; font-size: 14px;}
	.sidebar-help-title{font-size: 19px; line-height: 26px;}
	:deep(.wc-subtitle){font-size: 24px; line-height: 32px; padding: 0 0 20px;}
	#field-b_company_oib{width: 100%!important;}
	:deep(.wc-btns){
		text-align: right;
		@media (max-width: @t){
			text-align: left; padding-top: 22px;
		}
	}
	
	:deep(.btn-checkout){min-width: 245px;}
	:deep(.wc-step-note){font-size: 12px; line-height: 24px; width: 235px; text-align: center; text-transform: uppercase; padding: 7px 0 0; margin: 0 0 0 auto;}
	:deep(.w-sidebar-note){
		position: absolute; top: 52px; right: -380px; width: 315px; font-size: 13px; line-height: 20px;
		ul{
			.list; margin: 0;
			li:before{top:9px}
		}
	}
	:deep(.safe-purchase){
		font-size: 15px; line-height: 20px; padding: 10px 0 3px 85px; min-height: 80px;
		background-size: 65px auto;
	}
	:deep(.w-sidebar-payment-note){
		padding: 30px 0 0; font-size: 16px;
		ul{font-size: 15px;}
	}
	:deep(.webshop_widget_payment_installments){
		label{position: relative; top: auto; left: auto;}
	}
	.cc_types{margin: 10px 0;}
	.cc_installments select{width: 100%!important;}

	/*------- steps -------*/
	:deep(.steps) { 
		position: absolute; display: flex; top: -110px; right: 0; text-transform: uppercase; font-weight: bold;
		.step{min-width: 220px; height: 60px; display: flex; align-items: center; justify-content: center; margin: 0 0 0 5px; text-align: center; font-size: 18px; border-bottom: 0; opacity: .5;}
		a { color: #fff; text-decoration: none; display: block;}
		.current-step { 
			opacity: 1; position: relative; color: var(--textColor); background: var(--yellow);
			&:after{.pseudo(15px,15px); background: var(--yellow); .rotate(45deg); left: 50%; margin-left: -7px; bottom: -6px;}
		}
	}
	/*------- /steps -------*/

	:deep(.label-payment){display: none;}
	:deep(.form-label){
		input[type=checkbox]+label, input[type=radio]+label{color: #000; font-size: 17px;}
		textarea{height: 100px; margin-bottom: 15px;}
		.ww-coupons-add{width: 100%;}
	}
	:deep(.field-payment, .section-shipping){
		&>span{display: block; margin:0px 0 8px;}
	}
	:deep(.payment_info), :deep(.shipping_info){
		font-size:13px; line-height:18px; display: block; padding: 4px 0 5px 0; color: #5F5F5F; display: none;
		&:empty { display: none!important; }
	}
	:deep(.wc-card-payment){
		margin: 10px 0;
		.label{position: relative; padding: 0; cursor: text; z-index: 10; font-size: 15px; line-height: 20px; color: #5F5F5F; font-weight: 400; width: auto; text-align: left; .transition(all);}
		.field-cc_installments{max-width: 170px;}
	}
	:deep(.section-shipping) {
		.global-error { 
			display: none; 
			&.active { display: block; }
		}
		.cart_info_total_extra_shipping {display: none!important; position:absolute; font-size: 13px; text-transform: uppercase; top:0; right: 0; padding:5px 0 0 28px; color: var(--green);}
		.btn-change-address{display: inline-block; vertical-align: top; margin-top: 6px; margin-bottom: 10px; color: #000;}
	}
	:deep(.shipping-row){
		padding:0 0 8px 0; position:relative;
		&.single-shipping{
			margin-top: -10px;
			input[type=radio]+label{
				padding-left: 0; font-weight: bold; width: 100%;
				&:before, &:after{display: none!important;}
				&.hide{display: none!important;}
			}
			.shipping_info{
				padding-left: 0;
				&.custom{padding: 0!important; margin-top: 0;}	
			}
		}
		.shipping_info.custom{padding: 0!important; margin-top: -10px;}
		input[type=radio]+label{
			&.hide{display: none!important;}
		}
		&.hidden{display: none;}
		.shipping_info{padding-top: 8px;}
	}
	:deep(.payment-options){
		padding: 0 0 40px;
		input[type=radio]+label{width: 100%;}
		.field-payment.loading{
			pointer-events: none;  position: relative;
			.payment-row{opacity: 0.3;}			
			&:before{.pseudo(40px,40px); top: 50%; left: 50%; margin-left: -20px; margin-left: -20px; background: #fff url(assets/images/loader.gif) no-repeat center top; background-size: contain; z-index: 10; }
		}
	}
	:deep(.section-bill>div:last-child){padding-bottom: 30px;}
	:deep(.wc-bill-address), :deep(.wc-r1){
		padding: 0px 0 15px; line-height: 24px; font-size: 15px;
		p{padding: 0;}
	}
	:deep(.step2-shipping){
		padding: 20px 0;
		.wc-subtitle{padding: 0 0 15px;}
		&>span{display: block; margin: 0 0 10px;}
		.shipping_info{padding-top: 10px;}
	}

	:deep(.ww-coupons){
		float: none; text-align: left; padding: 15px 0; border-top: 1px solid var(--borderColor); border-bottom: 1px solid var(--borderColor);
		&.active{
			.wc-coupons-header{display: none;}
			.wc-coupons-body{max-height: none;}
		}
	}
	.ww-coupons-form{
		label{position: relative; top: auto; left: auto; display: block; font-size: 20px;}
	}
	.ww-coupons-list{width: 100%; float: none;}
	:deep(.wc-cart-title){padding: 15px 0; position: relative; font-size: 18px; cursor: default;}
	:deep(.wc-cart-container){
		overflow: hidden; max-height: 0; .transition(max-height);
		&.active{max-height: 800px;}
	}
	:deep(.ww-coupons-add){
		margin-top: 20px;
		&.active{margin-bottom: 10px;}
	}
	:deep(.wc-coupons-header){position: relative; font-size: 18px; cursor: default;}
	:deep(.wc-coupons-body){
		overflow: hidden; max-height: 0; .transition(max-height);
		&.active{max-height: 350px;}
	}
	:deep(.wc-btn-toggle){
		position: absolute; display: block; top: 4px; right: 0; padding: 0 22px 0 0; text-transform: uppercase; color: var(--textColor); text-decoration: underline; font-size: 14px; line-height: 20px; font-weight: bold;
		.toggle-icon{position: absolute; right: 0; top: 2px;}
		&.active{
			.toggle-icon:before{display: none;}
		}
	}
	:deep(.wc-btn-toggle-cart){
		top: 17px;
		.c{display: none;}
		&.active{
			.c{display: block;}
			.o{display: none;}
		}
	}
	:deep(.wc-btn-toggle-coupons){
		.l2{display: none;}
		&.active{
			.l1{display: none;}
			.l2{display: inline;}
		}
	}
	.step3-message{border-top: 1px solid var(--borderColor); padding: 30px 0 0 0;}
	.step3-field-message{padding-bottom: 0!important;}
	:deep(.wc-step3-btns-container){
		display: flex; padding: 30px 0 0 0; align-items: center; justify-content: space-between; width: 100%; border-top: 1px solid var(--borderColor);
		@media (max-width: @t){display: block; padding: 30px 0 0 0;}
	}
	:deep(.wc-terms){display: flex; flex-flow: column; width: 100%;}
	:deep(.webshop-accept-terms){
		position: relative; margin: 0 0 10px;
		a{font-weight: bold;}
		input[type=checkbox]+label{
			font-size: 15px; line-height: 24px; padding-top: 1px;
			&:before{top: 1px; border-width: 2px; border-color: var(--gray);}
		}
		.error{padding-left: 35px;}
	}
	:deep(.webshop-newsletter){margin: 0;}
	:deep(.wwp){padding-left: 0; padding-right: 0;}
	:deep(.wwp-col1){width: 100px;}
	:deep(.w-table){margin: 0;}
	:deep(.w-totals-cnt){padding: 20px 0 30px 110px; font-size: 15px; line-height: 25px;}
	:deep(.cart-total){font-weight: bold; font-size: 16px; padding-top: 3px; color: #000;}
	:deep(.w-btn-change){font-size: 14px; text-transform: uppercase; color: var(--textColor); line-height: 14px; display: inline-block; vertical-align: top; margin: 20px 0 0;}
	:deep(.section-shipping){padding-bottom: 30px;}

	:deep(.wc-accept-terms-tooltip){
		position: absolute; top: -3px; left: -150px; display: none; background: var(--yellow); color: var(--textColor); font-size: 15px; line-height: 17px; padding: 7px 10px; width: 130px; text-align: center;
		&:before{.pseudo(10px,10px); background: var(--yellow); .rotate(45deg); right: -3px; top: 11px;}
		&.active{display: block;}
	}
	:deep(.webshop-alert-terms){
		background: var(--yellow); color: #000; padding: 10px 20px 9px; font-size: 15px; line-height: 22px; position: relative; margin-bottom: 35px;
		&:after{.icon-danger-red; font: 25px/25px var(--fonti); color: #fff; position: absolute; top: 8px; right: 15px;}
		&:before{.pseudo(10px,10px); background: var(--yellow); .rotate(45deg); left: 22px; bottom: -3px;}
	}
	:deep(.wc-shipping-remaining){
		background: var(--yellow); color: var(--textColor); padding: 7px 20px; font-size: 15px; margin-top: 5px; position: relative;
		.wc-shipping-remaining-value{font-weight: bold;}
		&:after{.icon-danger-red; font: 25px/25px var(--fonti); position: absolute; top: 8px; right: 15px; color: #fff;}
	}
	/* :deep(.btn-finish:not(.active)){
		cursor: default;
	} */

	:deep(.col-cont){
		display: flex; flex-wrap: wrap;
		.wc-subtitle{width: 100%;}
		.global-error{width: 100%;}
		.field{width: 100%;}
		.field-address, .field-b_address{width: calc(~"100% - 130px");}
		.field-house_number, .field-b_house_number{
			width: 120px; margin-left: 10px;
			input{width: 100%!important;}
		}
	}
	/*------- credit card payment -------*/
	.cc_expire input, .cc_cvv input{padding: 0 5px; text-align: center;}
	.cc_installments{max-width: 170px;
		.chzn-container-single .chzn-single span{color: #000;}
	}
	.cc_type{position: absolute; padding-left: 10px !important;}

	.webshop_widget_payment_advanced{
		position: relative; padding-top: 6px;
		#field-error-cc{
			left: 100px;
			&:after{left: 7px;}
		}
		label{width: 100% !important; display: block !important; font-size: 16px !important;}
		div{padding-bottom: 8px; position: relative;}
		.error{left: 130px; right: auto;}
		.clear{padding: 0;}
		#field-cc_cvv{margin-right: 8px;}
		a{color: var(--textColor);}
		input[type=text]{
			&:hover, &:focus{background: #fff; color: #000;}
		}
	}
	
	:deep(.field-payment-11){
		position: relative;
		label{width: 100%;}
		input[type=radio]:checked + label{
			.payment_info{
				position: relative; padding-right: 85px;
				&:after{.pseudo(72px,32px); background: url(assets/images/kekspay.svg) no-repeat top left; background-size: contain; right: 0; top: -10px;}
			}
		}
	}
	/*------- /credit card payment -------*/

	/*------- shipping restiction -------*/
	:deep(.shipping-restriction-note){
		position: relative; display: block; padding: 13px 20px; background: var(--yellow); font-size: 16px; line-height: 22px; margin: 5px 0; color: var(--textColor);
		&:empty{padding: 0; margin: 0; background: transparent; display: none;}	
		&.payment{
			margin: 0 0 -20px;
			a{
				margin: 10px 0 0;
				&:hover{text-decoration-color: transparent;}	
			}	
		}
	}
	/*------- /shipping restiction -------*/

	@media screen and (max-width: 1400px) {
		:deep(.w-sidebar-note){right: -340px;}
	}

	@media screen and (max-width: 1240px) {
		:deep(.main-wrapper), :deep(.wc-wrapper){width: auto;}
		:deep(.wc-header), :deep(.main){padding: 0 30px;}
		:deep(.wc-header){height: 70px;}
		:deep(.logo){width: 75px; height: 50px; top: 10px;}
		:deep(.steps){
			right: auto; left: 140px; top: -80px;
			.step{height: 50px;}
		}
		:deep(.form-cart){width: 60%; flex-grow: 0; flex-shrink: 0;}
		:deep(.wc-container){display: flex; flex-direction: row-reverse;}
		:deep(.w-sidebar-support), :deep(.w-sidebar-note){left: auto; right: 0; position: relative;}
		:deep(.w-sidebar-support){width: auto;}
		:deep(.w-sidebar-note){width: auto; margin-top: 45px;}
		:deep(.w-sidebar-container){width: 40%; padding-left: 60px;}
		:deep(.w-sidebar-payment-note){padding: 20px 0 0;}
		:deep(.wc-accept-terms-tooltip){display: none!important;}
		:deep(.w-sidebar-support .sidebar-help-title){padding: 0 0 2px;}
		:deep(.webshop-alert-terms){margin-bottom: 25px;}
	}

	@media screen and (max-width: 990px) {
		:deep(.wc-subtitle){font-size: 18px; line-height: 23px; padding: 0 0 10px;}	
		:deep(.steps){
			top: -75px; left: 110px;
			.step{font-size: 14px; height: 45px;}
		}
		:deep(.w-sidebar-container){padding-left: 35px; width: 45%;}
		:deep(.form-cart){width: 55%;}
		:deep(.form-label){
			input[type=checkbox]+label, input[type=radio]+label{font-size: 15px;}
		}
		:deep(.wc-coupons-header), :deep(.wc-cart-title){font-size: 15px;}
		:deep(.wc-btn-toggle){font-size: 12px;}
		:deep(.webshop-accept-terms){font-size: 13px;}
		:deep(.page-checkout-step3 .w-sidebar-note){display: none;}
		:deep(.w-sidebar-support){top: 30px;}
		:deep(.w-sidebar-note){margin-top: 15px;}
	}

	@media screen and (max-width: 750px) {
		:deep(.steps){
			top: -50px; left: 70px;
			.step{min-width: 0; font-size: 12px; height: 35px; padding: 0 10px;}
			.current-step:after{bottom: -3px;}
		}
		:deep(.main), :deep(.wc-header){padding: 0 15px;}	
		:deep(.wc-header){height: 50px;}
		:deep(.logo){width: 50px; height: 33px; top: 9px;}
		:deep(.wc-container){flex-wrap: wrap; flex-direction: column-reverse;}
		:deep(.w-sidebar-container), :deep(.form-cart){width: 100%; padding: 0;}
		:deep(.wc-step-note), :deep(.btn-checkout){width: 100%;}
		:deep(.w-sidebar-cards){margin: 25px auto 0;}
		:deep(.w-sidebar-note){margin: 0; text-align: center;}
		:deep(.w-sidebar-payment-note){
			text-align: center; padding: 20px 0 75px;
			ul li{
				padding: 3px 0;
				&:before{display: none;}
			}
		}	
		:deep(.safe-purchase){text-align: left; background-size: 45px auto; padding: 0px 0 3px 60px;}
		:deep(.w-sidebar-payment-note){padding-top: 10px;}
		:deep(.wc-subtitle){padding: 7px 0 10px;}
		:deep(.payment-options){padding: 0 0 20px;}
		:deep(.wc-btn-toggle){
			position: relative; top: auto; right: auto; margin-top: 4px; display: inline-block; vertical-align: top; padding-right: 0; padding-left: 22px;
			.toggle-icon{right: auto; left: 0;}
		}
		:deep(.wc-btn-toggle-cart){display: block;}
		:deep(.wc-shipping-remaining){
			font-size: 12px;
			&:after{font-size: 20px; line-height: 20px;}
		}
		:deep(.w-totals-cnt){padding: 20px 0;}
		:deep(.wwp-col1){width: 65px;}
		:deep(.w-sidebar-support){top: auto; padding-top: 20px;}
		:deep(.wc-cart-title), :deep(.ww-coupons){padding: 10px 0;}
		:deep(.section-bill>div:last-child){padding-bottom: 15px;}
		:deep(.webshop-alert-terms){font-size: 12px; line-height: 16px; padding: 13px 45px 15px 15px;}
		:deep(.step3-wc-btns){float: none; padding: 20px 0 0;}
		:deep(.main-wrapper){padding: 0 0 50px;}
		:deep(.w-btn-change){
			background: var(--gray); font-size: 13px; height: 35px; line-height: 35px; padding: 0 15px; color: var(--yellow); text-decoration: none; float: right; margin: 10px 0 15px;
			&:hover{text-decoration: none; color: var(--yellow);}
		}

		:deep(.col-cont){
			.field-address, .field-b_address{width: calc(~"100% - 110px");}
			.field-house_number, .field-b_house_number{
				width: 100px; margin-left: 10px;
				input{width: 100%!important; padding-right: 15px;}
			}
		}

		:deep(.shipping-restriction-note){
			padding: 12px 16px; font-size: 14px; line-height: 20px; margin: 5px 0;
		}
		:deep(.shipping-restriction-note.payment){
			margin: 4px 0 -8px;
			a{margin-top: 8px;}	
		}
	}
</style>

<style lang="less">
	.page-checkout-step3 .w-sidebar-note{
		@media (max-width: @tp){display: none;}
	}
</style>