<template>
	<BaseWebshopCart v-slot="{counter, cartUrl, total}">
		<div class="ww cart" :class="{'active': counter}" @mouseover="cartPreview = true" @mouseleave="cartPreview = false">
			<NuxtLink :to="cartUrl" class="btn-header ww-items">
                <div class="cart-empty"><BaseCmsLabel code="ww_empty" /></div>
                <span class="ww-counter">
                    <span class="value total-items cart_info_item_count total_items">{{counter}}</span> kom
                </span>
                <span v-if="total?.total_items_total > 0" class="value cart_info_total_items total_price price"><BaseUtilsFormatCurrency :price="total.total_items_total" /></span>						
			</NuxtLink>

			<ClientOnly>
				<LazyWebshopCartPreview v-if="counter && cartPreview && !tabletBreakpoint" mode="preview" />
			</ClientOnly>
		</div>
	</BaseWebshopCart>
</template>

<script setup>
    const {onMediaQuery} = useDom();
    const cartPreview = ref(false);
    const {matches: tabletBreakpoint} = onMediaQuery({
		query: '(max-width: 1250px)',
	});
</script>
