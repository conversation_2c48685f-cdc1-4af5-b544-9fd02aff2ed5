<template>
	<div class="wwp" :id="'product-'+data.shopping_cart_code">
		<div class="wwp-col wwp-col1">
			<figure class="wwp-image">
				<NuxtLink :to="data.item.url_without_domain">
				<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width100-height100']" default="/images/no-image-100.jpg" :alt="data.item.title" />
			</NuxtLink>
			</figure>
		</div>

		<div class="wwp-col wwp-col2">
			<div class="wwp-title">
				<NuxtLink :to="data.item.url_without_domain">
					{{ data.item.title }}
				</NuxtLink>
			</div>
			<div class="wwp-cnt">
				<div class="wwp-cnt-col1">
					<div class="wwp-code"><BaseCmsLabel code="code" class="wwp-label" tag="span" />: {{ (data.item.variation?.code) ? data.item.variation.code : data.item.code }}</div>
					
					<div class="wwp-coupons">
						<template v-if="data.item.coupon_price">
							<BaseCmsLabel code="coupon_value" />: <BaseUtilsFormatCurrency :price="data.item.coupon_price" />
						</template>
						<template v-else-if="data.item.bonus_total">
							<BaseCmsLabel code="bonus_total_value" />: <BaseUtilsFormatCurrency :price="data.item.bonus_total" />
						</template>
					</div>
				</div>

				<div class="wwp-price">
					<div class="wp-price">
						<div v-if="data.total_basic > data.total" class="wwp-price-old product_total_basic"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
						<div class="wwp-price-current"  :class="[{'red': data.total_basic > data.total}]"><span class="product_total"><BaseUtilsFormatCurrency :wrap="true" :price="data.total" /></span></div>
					</div>
					<div class="wwp-price-count" v-if="data.quantity > 1">
						<span class="product_qty">{{ data.quantity }} x </span><span><BaseUtilsFormatCurrency :price="data.unit_price" /></span>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['data', 'mode']);
</script>

<style lang="less" scoped>
</style>
