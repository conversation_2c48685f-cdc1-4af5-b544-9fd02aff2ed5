<template>
	<div class="clear ww-coupons-list ww-auth-coupons-list">
		<table class="ww-coupons-table ww-auth-coupons-table" id="webshop_coupon_list" v-if="coupons?.length">
			<tbody>
				<tr class="ww-coupons-table-header">
					<BaseCmsLabel code="coupon_code_list" class="col-code" tag="td" />
					<BaseCmsLabel code="coupon_description" class="col-description" tag="td" />
					<BaseCmsLabel code="coupon_value_list" class="col-value" tag="td" />
					<BaseCmsLabel code="coupon_valid_until" class="col-valid" tag="td" />
				</tr>
				
				<tr v-for="item in coupons" :key="item.id">
					<td class="col-code">{{item.code}}</td>
					<td class="col-description">
						<template v-if="item.description">{{item.description}}</template>
						<template v-else>-</template>
					</td>
					<td class="col-value">
						<template v-if="item.type == 'f'">-<BaseUtilsFormatCurrency :price="item.coupon_price" /></template>
						<template v-else>-{{ item.coupon_percent * 100 }}%</template>
					</td>
					<td class="col-valid">
						<template v-if="+item.max_used > 0 && item.current_used >= item.max_used">
							iskorišten
						</template>
						<template v-else-if="item.datetime_expire">
							<template v-if="item.datetime_expire > Math.floor(Date.now() / 1000)">
								<BaseCmsLabel code="coupon_valid_until" class="label" tag="span" />
							</template>
							<template v-else>istekao</template>
							
							<BaseUtilsFormatDate :date="item.datetime_expire" format="DD.MM.YYYY. HH:mm" />
						</template>
						<template v-else>-</template>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
	const props = defineProps(['coupons']);
</script>

<style lang="less" scoped>
</style>