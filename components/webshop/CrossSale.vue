<template>
	<div class="shopping-cart-suggestion-container" v-if="crosssaleItems.length">
		<div class="shopping-cart-suggestion-header">
			<BaseCmsLabel code="shopping_cart_crosssale_title" class="shopping-cart-suggestion-title" tag="div" />
		</div>
		<div class="shopping-cart-suggestion-items-container">
			<BaseUiSwiper class="shopping-cart-suggestion-slider" :options="sliderOptions">
				<BaseUiSwiperSlide v-for="item in crosssaleItems" :key="item.shopping_cart_code">
					<CatalogIndexEntry :item="item" mode="cart" />
				</BaseUiSwiperSlide>
			</BaseUiSwiper>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['cartItems']);
	const catalog = useCatalog();
	const crosssaleItems = ref([]);
	const limit = 9;
	const excludeItems = ref([]);
	const excludeSameItems = ref([]);

	function divideNumberIntoEqualParts(total, count) {
		if (count === 0) return [];
		const base = Math.floor(total / count);
		const remainder = total % count;
		return Array(count).fill(base).map((v, i) => (i < remainder ? v + 1 : v));
	}

	const limits = divideNumberIntoEqualParts(limit, props.cartItems.length);

	onMounted(async () => {
		if(props.cartItems.length){
			excludeItems.value = props.cartItems.map(item => item.id);						
		
			await Promise.all(props.cartItems.map(async (item, index) => {
				if(!item?.meta_data?.related_widget_data) return
				
				const productId = item.id;

				const res = await catalog.fetchProducts({
					mode: 'widget',
					related_code: 'crosssale',
					related_item_id: productId,
					related_widget_data: item.meta_data.related_widget_data,
					limit: limits[index],
					id_exclude: excludeItems.value,
					always_to_limit: true,
					always_to_limit_strict_rules: true
				});				

				if (res.data?.items?.length) {
					crosssaleItems.value.push(...res.data.items);
				}
			}));			
			
			crosssaleItems.value = crosssaleItems.value.filter((item) => {
				if (excludeSameItems.value.includes(item.id)) return false;
				excludeSameItems.value.push(item.id);
				return true;
			});
		}
	});


	const { onMediaQuery, insertAfter} = useDom();
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});

	onMediaQuery({
		query: '(max-width: 750px)',
		enter: () => {
			insertAfter('.shopping-cart-suggestion-container', '.w-col2');
		},
	});

	let scItems = 3;
	if(mobileBreakpoint.value){
		scItems = 2;
	}

	const sliderOptions = {
		slidesPerView: scItems,
		slidesPerGroup: scItems,
		spaceBetween: -1,
		navigation: {enabled: true},
		enabled: false,
		breakpoints: {
			750: {
				enabled: true,
			},
		}
	}
</script>

<style lang="less" scoped>
	.shopping-cart-suggestion-container{
		position: relative; display: flex; flex-flow: column; width: 100%; margin-top: 110px; max-width: 790px;
		@media (max-width: @t){max-width: calc(~"100vw - 410px");}
		@media (max-width: @tp){max-width: calc(~"100vw - 359px"); margin-top: 120px;}
		@media (max-width: @m){max-width: 100%; margin: 4px 0 0;}
	}
	.shopping-cart-suggestion-header{position: relative; display: block; margin: 0 0 12px;}
	.shopping-cart-suggestion-title{
		display: block; font-size: 30px; line-height: 40px; font-weight: bold; text-transform: uppercase;
		@media (max-width: @tp){font-size: 20px; line-height: 24px;}
		@media (max-width: @m){font-size: 24px; line-height: 32px;}
	}
	:deep(.shopping-cart-suggestion-items-container){
		position: relative;
		.swipper-wrapper{padding-left: 1px;}
		.swiper-slide{display: flex; height: auto;}

		@media (max-width: @m){
			overflow: initial; width: calc(~"100% - -32px"); margin-left: -16px;
			.swiper-wrapper{
				display: flex; position: relative; padding: 1px 16px 16px; width: 100%; box-sizing: border-box; overflow-x: auto;
				&::-webkit-scrollbar {-webkit-appearance: none; height: 0;}
				&::-webkit-scrollbar-thumb {background-color: transparent;}
			}
			.swiper-slide{display: block; width: 95%!important; flex-grow: 0; flex-shrink: 0;}
		}
	}
	:deep(.swiper-navigation){
		position: absolute; height: 40px; top: calc(~"50% - 20px"); left: -62px; right: -62px; z-index: 1;
		@media (max-width: @t){left: -34px; right: -34px;}
		@media (max-width: @m){display: none!important;}
	}
	:deep(.swiper-button){
		position: absolute; left:-10px; top: 40%; z-index: 40; width: 60px; height: 60px; min-width: 0; background: #fff; box-shadow: inset 0 0 0 5px var(--yellow); font-size: 0; padding: 0; border-radius: 100%; cursor: pointer; .transition(background-color);
		@media (max-width: 1400px){width: 50px; height: 50px; top: 25%; left: 38px;}
		@media (max-width: @t){box-shadow: inset 0 0 0 3px var(--yellow); width: 40px; height: 40px;}
		&:before{
			.icon-arrow-right; font: 16px/60px var(--fonti); position: absolute; left: 0; top: 0; width: 100%; text-align: center; color: var(--textColor);
			@media (max-width: 1400px){line-height: 50px;}
			@media (max-width: @t){line-height: 40px; font-size: 12px;}
		}
		@media (min-width: @h){
			&:hover{background: var(--yellow);}
		}
		&.swiper-button-disabled{display: none;}
		&.swiper-button-lock{display: none;}
	}
	:deep(.swiper-button-prev){
		@media (max-width: 1400px){left: 38px;}
		@media (max-width: @t){left: 8px;}
		@media (max-width: @tp){left: 15px;}
		&:before{transform: rotate(180deg);}
	}
	:deep(.swiper-button-next){
		left: auto; right: -10px;
		@media (max-width: 1400px){right: 38px;}
		@media (max-width: @t){right: 8px;}
		@media (max-width: @tp){right: 15px;}
	}
</style>