<template>
	<div class="wwp wwp-quick" :id="'product-'+data.shopping_cart_code">
		<div class="wwp-col wwp-col1">
			<BaseWebshopRemoveProduct :item="data" v-slot="{onRemove, loading}">
				<div class="wwp-remove" :class="{'loading': loading}" @click="onRemove"><BaseCmsLabel code="remove" tag="span" /></div>
			</BaseWebshopRemoveProduct>
			<figure class="wwp-image">
				<NuxtLink :to="data.item.url_without_domain">
					<BaseUiImage loading="lazy" :data="data.item?.image_thumbs?.['width56-height56']" default="/images/no-image-56.jpg" :alt="data.item.title" />
				</NuxtLink>
			</figure>
		</div>

		<div class="wwp-col wwp-col2">
			<div class="wwp-title">
				<NuxtLink :to="data.item.url_without_domain">
					{{ data.item.title }}
				</NuxtLink>
			</div>

			<div class="wwp-cnt">
				<div class="wwp-cnt-col1">
					<div class="wwp-code">{{ (data.item.variation?.code) ? data.item.variation.code : data.item.code }}</div>
				</div>

				<div class="wwp-price">
					<div class="wp-price">
						<div v-if="data.total_basic > data.total" class="wwp-price-old product_total_basic"><BaseUtilsFormatCurrency :price="data.total_basic" /></div>
						<div class="wwp-price-current"  :class="[{'red': data.total_basic > data.total}]"><span class="product_total"><BaseUtilsFormatCurrency :price="data.total" /></span></div>
					</div>
					
					<CatalogEnergyBadge :item="data.item" modeClass="wwp-attr-energy" />
				</div>


				<div class="wwp-lowest-price" v-if="data.extra_price_lowest && data.extra_price_lowest > 0">
					<BaseCmsLabel code="extra_price_lowest" tag="span" />: <BaseUtilsFormatCurrency :price="data.extra_price_lowest" />
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['data', 'mode']);
</script>

<style lang="less" scoped>
</style>
