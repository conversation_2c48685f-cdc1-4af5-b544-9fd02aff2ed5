<template>
    <BaseWebshopAddToCartModal v-slot="{items, status, onClose, urls}" :auto-close="0">	
		<div v-if="items?.length" class="add-to-cart-modal-container">
            <BaseCatalogProductsWidget :key="items[0]?.id" :fetch="{related_code: 'related', related_item_id: items[0]?.id, limit: 3, always_to_limit_strict_rules: true}" v-slot="{items: relatedProducts}">           
                <BaseCatalogProductsWidget  v-if="items[0].is_available" :fetch="{
                    limit: 10, 
                    advanced_filters: {
                        id: items[0].id, 
                        category_id: items[0].category_id, 
                        manufacturer_id: items[0].manufacturer_id,
                        attributes_ids: items[0].attributes_ids, 
                        price: items[0].price_custom
                    },
                    warranty_main_item_price: {
                        basic_price: items[0].basic_price_custom, 
                        price: items[0].price_custom
                    },
                    warranty_product_id: items[0].id, 
                    only_available: false, 
                    sort: 'older'}" 
                    v-slot="{items: warrantyProducts}">

                    <div class="warranty-modal">
                        <div @click="onClose" class="modal-close-btn"></div>
                        <div :class="['warranty-modal-intro', (!relatedProducts?.length && !warrantyProducts.length) && 'warranty-modal-intro-basic']">                         
                            <div class="warranty-intro-cnt">
                                <template v-if="items?.length > 1">
                                    <template v-for="(label, index) in Object.values(status?.data?.labels_name)" :key="index">
                                        <h4 v-if="label && index == 0" class="message" :class="{'red': label == 'error_limitqty' || label == 'error_product_maximum_qty'}"><BaseCmsLabel :code='"modal_" + label' /></h4>                            
                                        <div v-if="label && index == 1" class="modal-title"><BaseCmsLabel code="modal_warranty_success" /></div>
                                    </template>
                                </template>
                                <template v-else>
                                    <h4 v-if="status?.data?.label_name" class="message" :class="{'red': status?.data?.label_name == 'error_limitqty' || status?.data?.label_name == 'error_product_maximum_qty'}"><BaseCmsLabel :code='"modal_" + status.data.label_name' /></h4>
                                </template>
                               
                               <CatalogWarranty v-if="warrantyProducts.length && items?.length < 2" :warrantyProducts="warrantyProducts" @warrantyChanged="selectedWarrantyCode = $event" />    

                                <div v-if="!relatedProducts?.length && !warrantyProducts.length" class="modal-image">
                                    <BaseUiImage v-if="items[0]?.main_image_thumbs" loading="lazy" :data="items[0]?.main_image_thumbs['width265-height265']" default="/images/no-image-124.jpg" />                                                
                                </div>
                                <div v-if="items[0].title && !warrantyProducts.length" class="modal-item-title">{{items[0].title}}</div>                                                                                                
                                <div v-if="!warrantyProducts.length" class="modal-item-price"> 
                                    <template v-if="items[0].price_custom > 0">
                                        <div class="cp-price-discount">
                                            <div v-if="items[0].discount_percent_base > 0 || items[0].price_custom < items[0].basic_price_base" class="cp-old-price"><BaseUtilsFormatCurrency :price="items[0].basic_price_base" /></div>
                                            <div class="cp-current-price" :class="{'red': items[0].discount_percent_base > 0 || items[0].price_custom < items[0].basic_price_base}">
                                                <span data-currency_format="full_price_currency"><BaseUtilsFormatCurrency :wrap="true" :price="items[0].price_custom" /></span>
                                            </div>
                                        </div>
                                    </template> 
                                </div>   
                            </div>
                            <div v-if="relatedProducts?.length || warrantyProducts.length" class="modal-image">
                                <BaseUiImage v-if="items[0]?.main_image_thumbs" loading="lazy" :data="items[0]?.main_image_thumbs['width100-height100']" default="/images/no-image-124.jpg" />                            
                            </div>
                        </div>

                        <div v-if="relatedProducts?.length" class="cd-row">
                            <div class="cd-related-products">
                                <div class="cd-related-title"><BaseCmsLabel code="warranty_related_products_title" default="Drugi kupci su također naručili..." /></div>
                                <div class="cd-related-slider">
                                    <CatalogIndexEntry v-for="item in relatedProducts" :key="item.id" :item="item" />                                 
                                </div>
                            </div>
                        </div>                                  

                        <div :class="['modal-continue', (!relatedProducts?.length && !warrantyProducts.length) && 'modal-continue-basic']">
                            <div @click="onClose" class="continue-shopping"><BaseCmsLabel code="continue_shopping" default="Nastavi kupovati" /></div>
                            <NuxtLink class="btn btn-green modal-view-cart" :to="urls.webshop_shopping_cart" @click="onClose()"><span><BaseCmsLabel code="view_shopping_cart" default="Pregled košarice" /></span></NuxtLink>                                    
                        </div>
                    </div>
                </BaseCatalogProductsWidget>
            </BaseCatalogProductsWidget>			
			<div v-if="items?.length" class="modal-bg-close" @click="onClose"></div>
		</div>
	</BaseWebshopAddToCartModal>
</template>

<script setup>
    const selectedWarrantyCode = ref('');

    function calcInstallmentsMaxPrice(item){
        if(item?.installments_info?.max_price){
			return item?.installments_info?.max_price;
		}
		return item?.basic_price_base           
    }
    
    function calcCardPrice(item){
        if(item.installments_info?.items?.length){
			return item.installments_info.items[item.installments_info.items.length - 1]
		}
		return null         
    }
</script>

<style lang="less" scoped>
    .add-to-cart-modal-container{position: fixed; bottom: 0; top: 0; right: 0; left: 0; background: rgba(0, 0, 0, .5); opacity: 1; z-index: 99999999; display: flex; align-items: center; justify-content: center;}
    .modal-bg-close{position: absolute; left: 0; top: 0; bottom: 0; right:0; z-index: 1;}
    .warranty-modal{max-width: 710px; min-width: 510px; width: auto; padding: 21px 33px; background: var(--white); color: var(--textColor); position: relative; z-index: 10; }
    .modal-close-btn{
        z-index: 10; position: relative; display: block; width: 20px; height: 20px; position: absolute; top: 20px; right: 20px; .transition(all); cursor: pointer; background: #fff; 
        &:before{position: absolute; .icon-close; font: 16px/20px var(--fonti); width: 100%; text-align: center; color: var(--red); top: 0; left: 0;}
    }


    .modal-desc-col{flex-grow: 1;}
    .modal-buttons{padding: 24px 0 0 0; width: 100%; display: flex; flex-flow: column;}
    .modal-view-cart{
        width: 100%; height: 55px; background: #0EAD69; color: var(--white);
        &:hover{color: var(--white);}
    }
    .modal-continue{
        display: flex; margin-top: 15px; overflow-x: hidden;
        @media(max-width: 750px){flex-flow: column-reverse; row-gap: 15px;}
    }
	.modal-continue-basic{flex-flow: column-reverse; row-gap: 15px;}    
    .continue-shopping{
        flex-shrink: 0; padding: 0 60px; display: flex; align-items: center; justify-content: center; font-size: 14px; line-height: 18px; text-decoration: underline; text-decoration-color: #FCD002 !important; text-underline-offset: 4px; cursor: pointer; 
        &:hover{text-decoration: none;}
    }



    .wp-message{color: transparent; background: unset;}
    .message{font-size: 22px; line-height: 28px; padding-top: 0; padding-bottom: 6px; text-transform: unset; color: var(--textColor);}
    @media(max-width: 750px){
        .message{font-size: 19px; line-height: 21px;}
    }
    :deep(.warranty-modal){
        overflow: auto; max-height: 96%;
        .modal-title{
            font-size: 18px; line-height: 23px; text-transform: initial; padding-bottom: 5px!important; font-weight: bold; color: var(--gray);
            p{padding-bottom: 0;}
        }
        .modal-image{
            margin-left: 80px; display: flex; align-items: center; justify-content: center; max-height: 200px; max-width: 200px;
            img{display: block; max-width: 100%; max-height: 100%; width: auto; height: auto;}
        }
        .warranty-intro-cnt{width: 340px; flex-shrink: 0; max-width: 340px;}
        .warranty-modal-intro{display: flex;}
        .modal-buttons{
            padding-top: 0 !important; width: 300px;
            p{padding-bottom: 6px;}
            input[type=checkbox]+label, input[type=radio]+label{font-size: 14px; line-height: 18px;}
        }
        .modal-warranty-add{background: #0EAD69; color: #fff; height: 40px; padding: 0 15px; font-size: 14px; line-height: 18px; margin: 10px 0 16px;}
        .cd-related-products{padding: 12px 0 0 0 !important; overflow-x: hidden; width: 644px;}
        .cd-services-price{display: inline;}
        .cd-related-title{font-size: 18px; line-height: 24px; padding-bottom: 5px;}
        .cd-row{display: block;}
        .cp-header-cnt{padding-top: 40px;}
        .cp-brand{top: 10px; left: 10px;}
        .cd-related-slider{
            display: flex; margin-left: 1px; width: auto;
            .cp{max-width: 215px; width: 215px;}
            .cp-image{
                figure{height: 100px;}
            }
            .cp-btn-addtocart{
                b{display: none;}
            }
            .cp-cnt-header{flex-grow: unset;}
            .cp-price-container-warranty{flex-grow: 1;}
            .cp-cnt{padding: 0 16px 16px;}
            .cp-card-discount{padding: 4px 0 3px; width: 28px; font-size: 10px;}
            .cp-card-prices{font-size: 10px;}
            .cp-title{font-size: 14px; line-height: 18px;}
            .cp-attrs{padding-bottom: 10px;}
            .cp-current-price{font-size: 24px;}
            .cp-rating-container{display: none !important;}
            .cp-badges{top: 10px; right: 15px; max-width: 30px;}
            .cp-special-list-badge{
                img{max-height: 30px;}
            }
            .cp-badge-status{max-height: 30px; height: 30px!important; width: 30px;}
            .cp-brand{
                img{max-height: 18px;}
            }
            .cp-attr-energy{
                font-size: 12px;
                img{max-width: 34px;}
            }
            .cp-attrs{
                font-size: 12px; line-height: 16px;
                li{
                    margin-right: 6px;
                    &:before{top: 5px; width: 4px; height: 4px;}
                }
            }
            .cp-unavailable{top: 73px;}
            .cp-btn-details{font-size: 12px; line-height: 14px; padding: 0 5px;}
            .cp-warranty-select{
                margin-top: 5px; position: relative;
                select{width: 100%; height: 32px; padding: 0 18px 0 12px; background: url(assets/images/icons/arrow-down.svg) no-repeat right 8px center; background-size: 8px auto;}
                .warranty-image{
                    position: absolute; left: 12px; top: 7px;
                    img{display: block; max-width: 18px; max-height: 18px;}
                }
            }
            .cp-btns{margin-top: 5px;}
            .cp-warranty-select-empty{min-height: 32px;}
            .badge-coupon-cnt{display: none!important;}
        }

        @media(max-width: 750px){
            max-height: none;
            .warranty-intro-cnt{width: auto; max-width: unset; flex-shrink: unset; padding-right: 40px;}
            .modal-image{display: none;}
            .cd-related-title{text-align: left; padding: 0 0 8px;}
            .cd-related-slider{
                flex-wrap: unset; overflow-x: auto; padding: 1px 0 0 1px; margin-left: 0; margin-right: 0; width: auto;
                &::-webkit-scrollbar{-webkit-appearance: none; height: 0px; display: none;}
                &::-webkit-scrollbar-thumb{height: 0px; display: none;}
                &::-webkit-scrollbar-track-piece{height: 0px; display: none;}
                &::-webkit-scrollbar-track{height: 0px; display: none;}
                .cp-image{
                    top: 12px; left: 12px; width: 90px;
                    figure{height: 90px;}
                }
                .cp{max-width: 290px; width: 295px; flex-shrink: 0;}
                .cp-extra-price-lowest{padding-top: 3px;}
                .cp-cnt{padding: 0 12px 12px;}
                .cp-brand{top: unset; left: unset;}
                .cp-btn-addtocart{
                    b{display: inline;}
                }
                .cp-badges{left: 13px;}
                .cp-header-cnt{padding-top: 12px;}
                .cp-cnt-header{padding-left: 102px; min-height: 138px;}
                .cp-warranty-select{
                    select{padding: 0 34px 0 15px; font-size: 13px; background-size: 9px auto; background: url(assets/images/icons/arrow-down.svg) no-repeat right 13px center; height: 40px;}
                }
                .cp-card-prices{flex-wrap: wrap; font-size: 12px; margin-bottom: 2px;}
                .cp-card-price{width: 50%;}
            }
        }        

    }
    .modal-item-price{
        .cp-current-price{font-size: 22px; margin-top: 3px;}
    }

    //basic modal 
    .warranty-modal-intro-basic{
        .message{padding-bottom: 10px;}
        .warranty-intro-cnt{display: flex; flex-flow: column; justify-content: center; align-items: center; width: 100%; max-width: 100%; text-align: center;}
        .modal-image{margin: 0; margin-bottom: 15px;}
        .modal-item-title{max-width: 410px; text-align: center;}
    }

    @media (max-width: 750px){
        .add-to-cart-modal-container{display: block;}
        .modal-title{font-size: 17px; line-height: 18px;}
        .modal-buttons{padding: 20px 0 0;}
        .modal-view-cart{padding: 0 15px; font-size: 14px; height: 40px; }
        .modal-close-btn{
            top: 15px; right: 15px;
            &:before{font-size: 14px;}
        }
        .warranty-modal{
            width: 100vw; height: 100%; padding: 15px; min-width: 0;
            .modal-title{padding-top: 5px;}
            .cd-services-price{display: initial;}
            .cd-related-products{width: auto;}
            .cd-related-slider{
                .cp-unavailable{top: auto;}
                .cp-btn-details{font-size: 14px; line-height: 16px; padding: 0 10px;}
            }
        }
        .warranty-modal-intro-basic{
            .message{max-width: 270px; margin: 0 auto;}
            .modal-image{max-width: 150px; display: block;}
            .warranty-intro-cnt{padding: 0;}
        }

    }
    
</style>