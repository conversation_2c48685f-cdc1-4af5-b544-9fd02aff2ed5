<template>
	<BaseWebshopCartPreview v-slot="{parcels, cartUrl}">
		<div class="ww-preview shopping_cart_preview">
			<div class="shoppingcart_items_small">

				<div class="ww-preview-items shopping_cart_preview_items" v-if="parcels[0].items.length">
					<div class="shoppingcart_items_small cart-small">
						<div class="w-table-small">
							<WebshopCartItemSmallQuick :mode="mode" :onClose="onClose" v-for="item in parcels[0].items" :data="item" :key="item.shopping_cart_code" />
						</div>
					</div>
				</div>

				<div class="ww-totals-cnt">
					<div class="ww-totals">
						<BaseWebshopTotal v-slot="{items}">
							<template v-if="items">
								<div class="ww-discount cart_info_total_extra_discount_box" v-if="items.total_extra_discount">
									<span class="ww-label"><BaseCmsLabel code="discount" default="Popust" />:</span>
									<span class="ww-value cart_info_total_extra_discount"><BaseUtilsFormatCurrency :price="items.total_extra_discount" /></span>
								</div>
								<div class="ww-totals-shipping">
									<span class="ww-label"><BaseCmsLabel code="shipping" default="Dostava" />:</span>
									<span class="ww-value cart_info_total_extra_shipping">
										<template v-if="items.total_extra_shipping == 0">
											<BaseCmsLabel code="free" tag="span" />
										</template>
										<template v-else>
											<BaseUtilsFormatCurrency :price="items.total_extra_shipping" />
										</template>
									</span>
								</div>
								<div class="ww-total" v-if="items.total">
									<span class="ww-label"><BaseCmsLabel code="totals" default="Sveukupno" />:</span>
									<span class="ww-value cart_info_total total_price"><BaseUtilsFormatCurrency :price="items.total" /></span>
								</div>
							</template>
						</BaseWebshopTotal>
					</div>

					<div class="ww-preview-footer">
						<div class="ww-preview-btns">
							<NuxtLink :to="cartUrl" class="btn btn-green ww-btn-view">
								<BaseCmsLabel code="view_shopping_cart" tag="span" />
							</NuxtLink>
						</div>

						<BaseWebshopFreeShipping v-slot="{item: toFree}">
							<WebshopFreeDeliveryProgressBar :toFree="toFree" />
						</BaseWebshopFreeShipping>

						<BaseCmsLabel code="free_delivery" class="ww-preview-free-delivery" tag="div" />
					</div>
				</div>
			</div>
		</div>
	</BaseWebshopCartPreview>
</template>

<script setup>
	const props = defineProps({
		mode: String,
		onClose: Function,
	});
</script>

<style lang="less" scoped>
</style>
