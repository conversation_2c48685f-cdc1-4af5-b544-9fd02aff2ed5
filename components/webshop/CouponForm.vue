<template>
	<BaseWebshopCouponForm v-slot="{onSubmit, onRemove, couponCode, message, error, loading, activeCoupon, handleInput}" :mode="props.mode">
		<div class="ww-coupons" :class="[{'ww-coupons-checkout': mode == 'checkout'}, {'ww-auth-coupons': mode == 'auth'}, {'base-coupon-error': error}, {'active': activeCoupon}, {'loading': loading}]">
			<div v-if="mode == 'checkout'" class="wc-coupons-header" :class="{'active': toggleCoupons}" @click="toggleCoupons = !toggleCoupons">
				<BaseCmsLabel code="coupon_have_coupon" class="wc-coupons-header-label" tag="div" />
				<span class="wc-btn-toggle wc-btn-toggle-coupons"><BaseCmsLabel code="toggle_coupon" tag="div" /><span class="toggle-icon"></span></span>
			</div>

			<template v-if="mode == 'auth'">
				<form class="ww-coupons-form ww-auth-coupons-form" @submit.prevent="onSubmit()">
					<BaseCmsLabel code="coupon_have_coupon" for="coupon_code" class="ww-coupons-label" tag="label" />
					<div class="ww-coupons-add ww-auth-coupons-add">
						<input
							id="coupon_code"
							:placeholder="labels.get('coupon_enter_code')"
							type="text"
							:value="couponCode"
							name="coupon_code"
							@keyup="handleInput"
							/>
						<button class="btn btn-blue ww-btn-add" type="submit"><BaseCmsLabel code="coupon_btn_add" tag="span" /></button>
						<BaseCmsLabel v-if="message" class="coupon_message" tag="div" :code="message" />
					</div>
				</form>
				<BaseAuthCoupons v-slot="{items}">
					<WebshopCouponList :coupons="items" />
				</BaseAuthCoupons>
			</template>

			<div v-else class="wc-coupons-body" :class="{'active': toggleCoupons}">

				<div v-if="activeCoupon" class="ww-coupons-active">
					<span class="ww-coupons-title" v-html="labels.get('coupon_included_code').replace('%COUPON_CODE%', '<strong>'+activeCoupon.code+'</strong>')"> </span>
					<span class="ww-coupon-delete" @click="onRemove"><BaseCmsLabel code="coupon_remove" tag="span" /></span>
					<BaseCmsLabel v-if="message" class="coupon_message" tag="div" :code="message" />
				</div>

				<form v-else class="ww-coupons-form" :class="[{'ww-auth-coupons-form': mode == 'auth'}]" @submit.prevent="onSubmit()">
					<BaseCmsLabel v-if="mode != 'checkout'" code="coupon_have_coupon" class="ww-coupons-label" tag="label" />
					
					<div class="ww-coupons-add">
						<input
							id="coupon_code"
							:placeholder="labels.get('coupon_enter_code')"
							type="text"
							:value="couponCode"
							name="coupon_code"
							@keyup="handleInput"
							/>
						<button class="btn btn-blue ww-btn-add" type="submit"><BaseCmsLabel code="coupon_btn_add" tag="span" /></button>
						<BaseCmsLabel v-if="message" class="coupon_message" tag="div" :code="message" />
					</div>
				</form>

				<template v-if="mode != 'auth'">
					<BaseAuthCoupons v-if="auth.isLoggedIn()" v-slot="{items, onActivate, status}">
						<div v-if="!activeCoupon && items?.length" class="ww-coupons-list">
							<BaseCmsLabel code="coupon_available" class="ww-coupons-list-title" tag="h5" />
							<table class="ww-coupons-table">
								<tr :class="{'active': !activeCoupon}" v-for="item in items" :key="item.id">
									<td class="col-code">{{item.code}}</td>
									<td class="col-type">
										<span v-if="item.type == 'f'"><BaseUtilsFormatCurrency :price="item.coupon_price" /></span>
										<span v-else>-{{ item.coupon_percent * 100 }}%</span>
									</td>
									<td class="col-link">
										<span class="btn-coupon-add" @click="onActivate(item.code)"><BaseCmsLabel code="coupon_use" /></span>
									</td>
								</tr>
							</table>
						</div>
						<BaseCmsLabel v-if="status" class="coupon_message" tag="div" :code="status.label_name" />
					</BaseAuthCoupons>
				</template>
			</div>
		</div>
	</BaseWebshopCouponForm>
</template>

<script setup>
	const props = defineProps(['mode']);
	const labels = useLabels();
	const auth = useAuth();
	const toggleCoupons = ref(false);
</script>

<style lang="less" scoped>
	/* .ww-coupons{float: unset; position: relative; margin-bottom: 32px; width: 100%; text-align: left;}
	.ww-coupons-label{font-size: 16px; line-height: 18px; padding: 0 0 9px;}
	.ww-coupons-add{
		width: 100%;
		&:before{font-size: 20px; line-height: 20px; top: 15px;}
		input{padding-left: 51px; font-size: 16px;}
	}
	.ww-btn-add{font-size: 18px;} */
</style>