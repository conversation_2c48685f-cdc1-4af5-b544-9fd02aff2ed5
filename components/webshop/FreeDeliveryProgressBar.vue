<template>
	<div class="ww-preview-note cart_info_total_extra_shipping_to_free_box" v-show="toFree?.amount != 0">
		<span class="ww-preview-note-label">
			<BaseCmsLabel code="min_total_missing" tag="span" /> <strong class="w-missing-shipping-value cart_info_total_extra_shipping_to_free"> <BaseUtilsFormatCurrency :price="toFree.amount" /></strong>
		</span>
	</div>

	<div class="free-delivery-missing cart_info_total_extra_shipping_to_free_box" v-show="toFree?.amount != 0">
		<span class="free-delivery-missing-bg cart_info_total_extra_shipping_to_free_percent" :style="'width: '+ toFree.percent"></span>
		<span class="free-delivery-missing-num">
			<span class="cart_info_total_items_total">
				<BaseUtilsFormatCurrency :price="Number(toFree.above) - Number(toFree.amount)" />
			</span> &nbsp;/&nbsp;
			<span class="cart_info_total_extra_shipping_free_above">
				<BaseUtilsFormatCurrency :price="toFree.above" />
			</span>
		</span>
	</div>
</template>



<script setup>
	const labels = useLabels();
	const {formatCurrency} = useCurrency();
	const props = defineProps(['toFree']);
</script>

<style lang="less" scoped>
</style>