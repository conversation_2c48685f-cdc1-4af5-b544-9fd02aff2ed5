<template>
	<div class="table-order table-header table-order-header">
		<BaseCmsLabel code="webshop_order_number" class="table-col col-order-num" tag="div" />
		<BaseCmsLabel code="order_date" class="table-col col-order-date" tag="div" />
		<BaseCmsLabel code="status" class="table-col col-order-status" tag="div" />
		<BaseCmsLabel code="total" class="table-col col-order-total" tag="div" />
		<div class="table-col col-order-btns"></div>
	</div>

	<div class="orders" id="items_widgetlist_webshop">
		<BaseUiAccordion>
			<BaseUiAccordionPanel v-for="item in visibleOrders" :key="item.id" :id="item.id" v-slot="{onToggle, active}">
				<div class="order-row" :class="{'active': active}">
					<div class="table-order" @click="onToggle">
						<div class="table-col col-order-num">{{item.id}}</div>
						<div class="table-col col-order-date"><BaseUtilsFormatDate :date="item.datetime_created" /></div>
						<div class="table-col col-order-status">{{item.status.title}}</div>
						<div class="table-col col-order-total"><strong><BaseUtilsFormatCurrency :price="item.total" /></strong></div>
						<div class="table-col col-order-btns">
							<div class="btn-order-details" :class="{'active': active}">
								<span class="btn-inactive" v-if="!active"><BaseCmsLabel code="webshop_btn_order_details" /><span class="toggle-icon"></span></span>
								<span class="btn-active" v-else><BaseCmsLabel code="webshop_btn_hide_order_details" /><span class="toggle-icon"></span></span>
							</div>
						</div>
					</div>

					<div v-if="item.order_items" v-show="active" class="order-details">
						<div class="w-table w-table-details">
							<AuthOrderItem v-for="product in item.order_items" :product="product" :key="product.id" />
							<div class="wp-sum">
								<span>
									<BaseCmsLabel code="total" />:
									<strong>
										<BaseUtilsFormatCurrency :price="item.total" />
									</strong>
								</span>
							</div>
						</div>
					</div>
				</div>
			</BaseUiAccordionPanel>
		</BaseUiAccordion>
	</div>

	<BaseAuthUser v-slot="{urls}">
		<template v-if="mode == 'dashboard' && orders?.length > 5">
			<div class="show-all-orders">
				<NuxtLink class="btn btn-yellow btn-medium btn-all-orders" :to="urls.auth_my_webshoporder"><BaseCmsLabel code="all_webshoporder" /></NuxtLink>
			</div>
		</template>
	</BaseAuthUser>
	-->
</template>

<script setup>
	const props = defineProps(['orders', 'mode']);
	const visibleOrders = computed(() => {
		return props.mode === 'dashboard' ? props.orders.slice(0, 5) : props.orders;
	});
</script>

<style lang="less" scoped>
</style>
