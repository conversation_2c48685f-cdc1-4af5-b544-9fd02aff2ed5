<template>
	<div class="wp wp-details">
		<figure class="wp-image">
			<figure><BaseUiImage loading="lazy" :product="product?.main_image_thumbs?.['width110-height110']" default="/images/no-image-100.jpg" :alt="product.title" /></figure>
		</figure>

		<div class="wp-row-col2">
			<h2 class="wp-title">{{ product.title }}</h2>
			<div class="wp-cnt">
				<div class="wp-attrs wp-details-attrs">
					<div class="wp-code"><BaseCmsLabel code="code" />: {{product.code}}</div>
					<div class="wp-variations" v-if="product.description">{{product.description}}</div>
				</div>

				<div class="wp-total">
					<div>
						<div class="wp-price">
							<span :class="['product_total', (product.basic_price > product.price ? 'wp-price-current wp-price-discount' : 'wp-price-current')]"><span><BaseUtilsFormatCurrency :wrap="true" :price="product.price" /></span></span>
						</div>
						<div class="wp-qty-count" v-if="parseInt(product.qty).toFixed(0) > 1">
							<span class="product_qty">{{ parseInt(product.qty).toFixed(0) }}</span> x <BaseUtilsFormatCurrency :price="product.price" />
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['product']);
</script>

<style lang="less" scoped>
</style>
