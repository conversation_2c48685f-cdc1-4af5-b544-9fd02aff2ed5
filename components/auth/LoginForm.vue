<template>
	<BaseCmsLabel v-if="mode == 'login'" code="already_register_login" class="a-subtitle" tag="h2" />
	<BaseCmsLabel v-if="mode == 'userBox'" code="login_subtitle" class="aw-quick-login-title" tag="div" />

	<AuthSocialLogin :mode="mode" v-if="mode != 'checkout'" />
	
	<BaseAuthLoginForm class="form-label" :class="[mode == 'checkout' && 'wc-login-form step1', mode]" v-slot="{loading, fields, formError, urls}" :submit-url="mode == 'checkout' ? 'webshop_customer' : 'auth'">
		<div class="global-error" v-if="formError">
			<BaseCmsLabel :code="formError" />
		</div>

		<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
			<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
				<BaseFormInput :id="mode == 'userBox' ? item.name+'ub' : item.name" />
				<BaseCmsLabel tag="label" :for="mode == 'userBox' ? item.name+'ub' : item.name" :code="item.name" />
				<span class="field_error error" v-show="errorMessage" v-html="errorMessage" />
			</p>
		</BaseFormField>

		<div class="submit auth-links-cnt" :class="[mode == 'checkout' && 'wc-step1-submit']">
			<button type="submit" class="btn btn-gray" :class="[{'loading': loading}, mode == 'checkout' && 'btn-wc-login', mode == 'login' && 'btn-arrow']"><UiLoader v-if="loading" /><BaseCmsLabel code="login" /></button>
			<span class="auth-links" :class="[mode == 'checkout' && 'wc-auth-links']">
				<NuxtLink :to="urls.auth_forgotten_password" data-auth_login="forgotten_password" data-siteform_response="show_hide" data-siteform_response_hide="-1">
					<BaseCmsLabel v-if="mode == 'userBox'" code="quick_forgotten_password" tag="div" />
					<BaseCmsLabel v-else code="forgotten_password" tag="div" />
				</NuxtLink>
			</span>
		</div>
	</BaseAuthLoginForm>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>

<style lang="less" scoped>
	//GLOBAL + LOGIN
	.auth-links-cnt{display: flex; align-items: center; width: 100%; float: unset;}
	.btn-gray{flex-shrink: 0; flex-grow: 0;}
	.auth-links{
		float: unset; flex-grow: 1; width: auto; padding: 0; text-align: center;
		a{margin: 0; text-decoration: none;}
		:deep(span){
			display: block; text-decoration: underline;
			&:hover{text-decoration: none;}
		}
	}

	//USERBOX
	.form-label.userBox{
		.auth-links-cnt{padding-top: 6px;}
		.btn-gray{margin: 0; width: auto; padding: 0 33px;}
		.auth-links{font-size: 14px; line-height: 16px;}
		.field-remember_me{
			margin-top: 5px; padding: 0 0 10px;
			input[type=checkbox]+label{font-size: 14px; padding-left: 32px;}
			input[type=checkbox]:checked+label:before{color: var(--yellow); background: var(--gray); border-color: var(--gray);}
		}
	}

	//CHECKOUT
	.form-label.checkout{
		.field-remember_me{margin-top: 10px; padding-bottom: 15px;}
	}
</style>