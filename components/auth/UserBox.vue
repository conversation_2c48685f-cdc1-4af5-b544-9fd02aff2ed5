<template>
	<BaseAuthUser v-slot="{isLoggedIn, urls}">
		<div class="aw" :class="[{'active': userBoxDropdown}]" ref="aw">
			<ClientOnly>
				<NuxtLink v-show="mobileBreakpoint" :to="urls.auth" class="aw-btn" :class="{'aw-btn-loggedin': isLoggedIn}"><span><BaseCmsLabel code="my_account" /></span></NuxtLink>
				<div v-show="!mobileBreakpoint" class="aw-btn" :class="{'aw-btn-loggedin': isLoggedIn}" @click="userBoxDropdown = !userBoxDropdown"><span><BaseCmsLabel code="my_account" /></span></div>

				<div v-if="isLoggedIn" class="aw-dropdown-list aw-loggedin">
					<NuxtLink class="aw-link aw-profile" :to="urls.auth"><BaseCmsLabel tag="span" code="my_profile" /></NuxtLink>
					<NuxtLink class="aw-link aw-orders" :to="urls.auth_my_webshoporder"><BaseCmsLabel tag="span" code="my_orders" /></NuxtLink>
					<NuxtLink class="aw-link coupon" :to="urls.auth_my_webshopcoupon"><BaseCmsLabel tag="span" code="my_coupons" /></NuxtLink>
					<NuxtLink class="aw-link aw-wishlist" :to="urls.auth_wishlist"><BaseCmsLabel tag="span" code="my_wishlist" /></NuxtLink>
					<NuxtLink class="aw-link edit" :to="urls.auth_edit"><BaseCmsLabel tag="span" code="edit_profile" /></NuxtLink>
					<NuxtLink class="aw-link pass" :to="urls.auth_change_password"><BaseCmsLabel tag="span" code="change_password" /></NuxtLink>
					<NuxtLink class="aw-link logout" :to="`${urls.auth_logout}?redirect=/`"><BaseCmsLabel tag="span" code="logout" /></NuxtLink>
				</div>
				<div v-else class="aw-dropdown-list aw-login">
                    <LazyAuthLoginForm mode="userBox" />
					<div class="aw-footer">
						<BaseCmsLabel code="new_user" class="aw-quick-login-title" tag="div" />
						<NuxtLink :to="urls.auth_signup" class="btn btn-aw-signup">
							<BaseCmsLabel code="open_account" />
						</NuxtLink>
					</div>
				</div>
			</ClientOnly>
		</div>
	</BaseAuthUser>
</template>

<script setup>
	const {onClickOutside, onMediaQuery} = useDom();
	const userBoxDropdown = ref(false);
	const aw = ref();

	// close dropdown when clicked outside
	onClickOutside(aw, () => {
		userBoxDropdown.value = false;
	});

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});    

	// close dropdown on route change
	const route = useRoute();
	watch(
		() => route.fullPath,
		() => {
			userBoxDropdown.value = false;
		}
	);
</script>