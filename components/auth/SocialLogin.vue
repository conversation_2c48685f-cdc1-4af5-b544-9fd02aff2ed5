<template>
	<div class="auth-social" :class="{'auth-social-checkout': mode == 'checkout', 'auth-social-user-box': mode == 'userBox', 'auth-social-login': mode == 'login', 'auth-social-register': mode == 'register'}">
		<BaseAuthSocialLogin v-slot="{socialLogin, loadingFacebook, loadingGoogle}">
			<div class="auth-social-icons">
				<button class="btn btn-border btn-facebook" :class="{'loading': loadingFacebook}" @click="socialLogin('Facebook')"><UiLoader v-if="loadingFacebook" /><span>Facebook</span></button>
				<button class="btn btn-border btn-google" :class="{'loading': loadingGoogle}" @click="socialLogin('Google')"><UiLoader v-if="loadingGoogle" /><span>Google</span></button>
			</div>
		</BaseAuthSocialLogin>

		<BaseCmsLabel v-if="mode == 'signup'" class="login-social-title register-social-title" tag="div" code="register_social_title" default="ili se registrirajte putem emaila" />
		<BaseCmsLabel v-else class="login-social-title" tag="div" code="login_social_title" default="ili se prijavite putem emaila" />
	</div>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>

<style lang="less" scoped>
	.auth-social{
		padding-bottom: 16px;
		@media (max-width: @tp){padding-bottom: 12px;}
	}
	.auth-social-icons{
		display: flex; width: 100%;
		.btn{
			font-size: 18px; line-height: 22px; color: var(--textColor); text-transform: uppercase; width: 100%; background: var(--white); padding: 0; min-height: 50px; height: 50px; display: flex; align-items: center; justify-content: center; border-color: var(--borderColor); margin-right: 8px; position: relative;
			span{
				position: relative; padding-left: 21px; display: block;
				&:after{.pseudo(11px,20px); top: 0; left: 0; background: url(assets/images/auth_facebook.svg) top left no-repeat; background-size: contain;}
			}
			&:last-child{margin-right: 0;}
		}
		.btn-google{
			span{
				padding-left: 30px;
				&:after{.pseudo(20px, 20px); background: url(assets/images/auth_google.svg) top left no-repeat; background-size: contain;}
			}
		}
		@media (max-width: @tp){
			.btn{
				font-size: 16px; line-height: 1.1; min-height: 48px; height: 48px;
				span{
					padding-left: 18px;
					&:after{width: 9px; height: 16px;}
				}
			}
			.btn-google{
				span{
					padding-left: 24px;
					&:after{width: 16px; height: 16px;}
				}
			}
		}
	}
	.login-social-title{
		font-size: 16px; line-height: 24px; padding-top: 16px;
		@media (max-width: @tp){font-size: 14px; line-height: 1.2; padding-top: 12px;}
	}
</style>
