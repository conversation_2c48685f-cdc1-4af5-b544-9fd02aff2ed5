<template>
	<BaseSearchForm :fetch="searchFormConfig" v-slot="{searchResults, updateValue, totalSearchResults, searchTerm, loading, handleInput, onReset, onSubmit}" ref="searchForm"  @afterSubmit="onAfterSubmit">
		<Body :class="{'active-autocomplete': totalSearchResults}" />
		<div class="sw" :class="[{'loading': loading}, {'active': showListContainer}]" ref="sw">
			<div class="sw-toggle" @click="onReset(); showListContainer = false;"></div>
			
			<div class="sw-form" id="main_search" method="get">
				<input ref="searchInput" class="swap-value sw-input" name="search_q" id="search_q" type="text" :value="searchTerm" @input="updateValue" autocomplete="off" @keyup="handleInput" @keyup.enter="onMobileSearch" @focus="showListContainer = true" :placeholder="labels.get('enter_search_term') || 'Pretraživanje...'"/>                              		                                        

				<ClientOnly>
					<BaseCmsNav code="search_list" v-slot="{items}">
						<div v-if="items.length" class="sw-list-container">
							<div class="sw-list-title"><BaseCmsLabel code="search_list_title" /></div>
							<ul class="sw-list">
								<li v-for="item in items" :key="item.id">
									<NuxtLink @click="onReset; showListContainer = false;" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
								</li>
							</ul>
						</div>
					</BaseCmsNav>  

					<div class="autocomplete-container sw-autocomplete-container" v-if="totalSearchResults">
						<div class="autocomplete-wrapper">
							<div class="autocomplete-col1" v-if="searchResults?.catalogproduct?.length">
								<div class="autocomplete-cnt catalogproduct">
									<ul class="ui-autocomplete ui-autocomplete1">
										<li class="ui-menu-item" v-for="item in searchResults.catalogproduct" :key="item.id">
											<NuxtLink @click="onReset" :to="item.url_without_domain">
												<span class="image">
													<BaseUiImage loading="lazy" :src="item.image_upload_path" width="64" height="64" default="/no-image-64.jpg" />
												</span>
												<span class="search-cnt">
													<span class="search-title">{{ item.title }}</span>
													<span class="search-code">{{ item.code }}</span>
													<span class="search-price"><BaseUtilsFormatCurrency :price="item.price_custom" /></span>
												</span>												
											</NuxtLink>
										</li>
										<li v-if="searchResults?.catalogproduct_show_all?.url_without_domain" class="ui-menu-item autocomplete-showall">
											<NuxtLink @click="onReset" :to="searchResults?.catalogproduct_show_all.url_without_domain">
												<strong>Prikaži sve proizvode</strong>
											</NuxtLink>
										</li>
									</ul>
								</div>													
							</div>

							<div class="autocomplete-col2">								
								<div v-if="searchResults?.catalogcategory?.length" class="autocomplete-col-row autocomplete-col-category catalogcategory">
									<div class="autocomplete-title"><BaseCmsLabel code="autocomplete_categories" /></div>
									<ul class="ui-autocomplete">
										<li v-for="item in searchResults.catalogcategory" :key="item.id" class="ui-menu-item" role="presentation" data-autocomplete_focus_identificator="all_catalogcategory_825">
											<NuxtLink @click="onReset" :to="item.url_without_domain">
												<span v-html="formatText(item.title, searchTerm)"></span>
											</NuxtLink>											
										</li>
									</ul>
								</div>
											
								<div v-if="searchResults?.catalogmanufacturer?.length" class="autocomplete-col-row autocomplete-col-brands catalogmanufacturer">
									<div class="autocomplete-title"><BaseCmsLabel code="autocomplete_manufacturers" /></div>
									<ul class="ui-autocomplete">
										<li v-for="item in searchResults.catalogmanufacturer" :key="item.id" class="ui-menu-item" role="presentation" data-autocomplete_focus_identificator="all_catalogcategory_825">
											<NuxtLink @click="onReset" :to="item.url_without_domain">
												<span v-html="formatText(item.title, searchTerm)"></span>
											</NuxtLink>											
										</li>							
									</ul>	
								</div>

								<div v-if="searchResults?.publish?.length" class="autocomplete-col-row autocomplete-col-publish publish">
									<div class="autocomplete-title"><BaseCmsLabel code="autocomplete_publish" /></div>
									<ul class="ui-autocomplete">
										<li v-for="item in searchResults.publish" :key="item.id" class="ui-menu-item" role="presentation" data-autocomplete_focus_identificator="all_catalogcategory_825">
											<NuxtLink @click="onReset" :to="item.url_without_domain">
												<span v-html="formatText(item.title, searchTerm)"></span>
											</NuxtLink>											
										</li>							
									</ul>	
								</div>
							</div>
						</div>
					</div>
				</ClientOnly>

				<button @click="onSubmit()" class="sw-btn" type="submit"><BaseCmsLabel code="search_button" default="Traži" /></button>
			</div>
		</div>
	</BaseSearchForm>
</template>

<script setup>
	const labels = useLabels();
	const {onClickOutside} = useDom();

	const searchFormConfig = {
		'allow_models': ['catalogcategory', 'catalogmanufacturer', 'catalogproduct', 'publish'],
		'result_fields': {
			'catalogproduct': ['image', 'price', 'code', 'price_custom', 'basic_price', 'basic_price_custom', 'discount_percent', 'discount_percent_custom'],
		},
		'result_per_page': {
			'_default': 5
		},
		'result_image': '64x64_r'
	}

	const sw = ref(null);
	const searchForm = ref(null);
	const showListContainer = ref(false);

	onClickOutside(sw, () => {
		searchForm.value.resetAutocomplete();
		showListContainer.value = false;
	});

	function onAfterSubmit(){
		showListContainer.value = false;
	}

	function formatText(title, term){
		if (!title || !term) return title;
		const regex = new RegExp(`(${term})`, 'gi');
		return title.replace(regex, '<strong>$1</strong>');
	}
</script>