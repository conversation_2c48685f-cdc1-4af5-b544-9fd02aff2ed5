<template>
	<BaseCatalogCategoriesWidget :fetch="{level_from: 1, level_to: 1, special: true}" v-slot="{items}">
        <!-- FIXME - problem s filter linkovima kategorija u podkategoriji zimsko snizenje -->
        <span @click="back" class="m-nav-title">{{navActive.lvl2?.title ? navActive.lvl2.title : (navActive.lvl1?.title ? navActive.lvl1?.title : labels.get('navigation'))}}</span>
        <span v-if="items.length" class="nc-m-cnt">
            <ul class="nav-categories" ref="categoriesNav">
                <li class="nav-home"><NuxtLink :to="getAppUrl('home')">Početna</NuxtLink></li>
                <template v-for="item in items" :key="item.id">
                    <BaseCmsNav :code="item.code" v-slot="{items: submenuItems}">                    
                        <li :class="['nav-category-'+ item.code, {'has-children': submenuItems.length}, {'active': navActive.lvl1?.id == item.id}, {'hidden': navActive.lvl1?.id && navActive.lvl1?.id != item.id}]" @mouseenter="!tabletBreakpoint && handleMouseEnter(item.id);" @mouseleave="!tabletBreakpoint && handleMouseLeave();"> 
                            <BaseUiLink native :prevent-default="(submenuItems.length && tabletBreakpoint) ? true : false" :href="item.url_without_domain" @click="navActive.lvl1.id = item.id; navActive.lvl1.title = item.title">{{ item.title }}</BaseUiLink> 
                            <div v-if="submenuItems.length" class="submenu-container">
                                <div class="submenu-col submenu-col1">
                                    <ul class="nav-submenu">
                                        <template v-for="submenuItem in submenuItems" :key="submenuItem.id">
                                            <li :class="{'has-children': submenuItem.items?.length, 'active': navActive.lvl2?.id == submenuItem.id, 'hidden': navActive.lvl2?.id && navActive.lvl2.id != submenuItem.id}">
                                                <BaseUiLink native :prevent-default="(submenuItem.items?.length && tabletBreakpoint) ? true : false" :href="submenuItem.url_without_domain" @click="navActive.lvl2.id = submenuItem.id; navActive.lvl2.title = submenuItem.title">{{ submenuItem.title }}</BaseUiLink>
                                                <ul v-if="submenuItem.items?.length">
                                                    <li v-for="lvl2 in submenuItem.items" :key="lvl2.id" :class="lvl2.style">                                                
                                                        <NuxtLink :to="lvl2.url_without_domain">{{ lvl2.title }}</NuxtLink>
                                                    </li>                                      
                                                </ul>
                                            </li>
                                        </template>
                                    </ul>
                                </div>
                                <ClientOnly>
                                    <LazyBaseCatalogProductsWidget v-if="item.code" :fetch="{list_code: ['bestsellers_'+item.code], limit:3, only_available: true, extra_data: ['documents']}" v-slot="{items: menuProducts}">
                                        <div v-if="menuProducts?.length" class="submenu-col submenu-col2">
                                            <div class="nav-bestsellers-cnt">
                                                <div class="nav-bestsellers-items-title"><BaseCmsLabel code="bestsellers" /><NuxtLink class="bs-show-more" :to="item.url_without_domain"><BaseCmsLabel code="show_all" /></NuxtLink></div>
                                                <div class="nav-bestsellers-items">                                    
                                                    <CatalogIndexEntrySmall v-for="product in menuProducts" :item="product" :key="product.id" />
                                                </div>
                                            </div>
                                        </div>
                                    </LazyBaseCatalogProductsWidget>
                                </ClientOnly>                                                           
                            </div>
                        </li>
                    </BaseCmsNav>
                </template>
            </ul>
        </span>
	</BaseCatalogCategoriesWidget>
</template>

<script setup>
    const labels = useLabels();
	const {getAppUrl} = useApiRoutes();
    const route = useRoute();
	const {onClickOutside, onMediaQuery} = useDom();
	const navMenuRef = ref(null);
    const emit = defineEmits(['closeNav']);
	const navActive = ref({lvl1: {}, lvl2: {}});
    const enterTimeout = ref(null);
    const categoriesNav = ref(null);

	const {matches: tabletBreakpoint} = onMediaQuery({
		query: '(max-width: 1250px)',
	});   

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 750px)',
	});   

	onClickOutside(categoriesNav, () => {        
        if(!mobileBreakpoint.value){
            resetNav();
            emit('closeNav');
        }
	});    

    function handleMouseEnter(id){
        if (enterTimeout.value) clearTimeout(enterTimeout.value);
        enterTimeout.value = setTimeout(() => {
            navActive.value.lvl1.id = id;
        }, 200);
    }

    function handleMouseLeave(){
        if (enterTimeout.value) clearTimeout(enterTimeout.value);
        resetNav();
    }

    function resetNav(){
        navActive.value = {lvl1: {}, lvl2: {}}
    }

    function back(){
        if(navActive.value.lvl2?.id){
            return navActive.value.lvl2 = {};
        }
        if(navActive.value.lvl1.id){
            return navActive.value.lvl1 = {};
        }
        emit('closeNav');
    }
	
    watch(
		() => route.fullPath,
		() => {
			resetNav();
            emit('closeNav');
		}
	)    
</script>