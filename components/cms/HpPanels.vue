<template>
    <div class="panel-row">
        <div class="panel panel1">
            <div class="panel-title" v-html="labels.get('hp_panel1_title')"></div>
            <div class="display-f panel-cols" v-interpolation v-html="labels.get('hp_panel1_content')"></div>
            <div class="panel-footer">
                <div class="panel-footer-cnt" v-html="addLazyToImages(labels.get('hp_panel1_footer'))"></div>
            </div>
        </div>
        <div class="panel panel2">
            <div class="panel-title" v-html="labels.get('hp_panel2_title')"></div>
            <div class="display-f panel-cols" v-interpolation v-html="labels.get('hp_panel2_content')"></div>
            <div class="panel-footer">
                <div class="panel-footer-cnt" v-html="addLazyToImages(labels.get('hp_panel2_footer'))"></div>
            </div>					
        </div>
        <div class="panel panel3">
            <div class="panel-title" v-html="labels.get('hp_panel3_title')"></div>
            <div class="display-f panel-cols" v-interpolation v-html="labels.get('hp_panel3_content')"></div>
            <div class="panel-footer">
                <div class="panel-footer-cnt" v-html="addLazyToImages(labels.get('hp_panel3_footer'))"></div>
            </div>						
        </div>
    </div>
</template>

<script setup>
    const labels = useLabels();
</script>