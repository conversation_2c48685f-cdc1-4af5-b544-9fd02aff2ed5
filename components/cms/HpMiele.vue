<template>
    <div class="panel-miele">
        <div class="logo-miele"></div>
        <div class="image-miele" v-html="labels.get('miele_image')"></div>		
        <div class="display-f panel-miele-cnt">
            <BaseCmsNav code="miele_col1" v-slot="{items}">
                <div v-if="items.length" class="flex-col flex-col1">
                    <p v-for="item in items" :key="item.id" :class="[item.style && item.style]">
                        <NuxtLink v-if="item.url_without_domain?.trim()" :to="item.url_without_domain" :target="item.target_blank != 0 ? '_blank' : null"><span v-html="item.title"></span></NuxtLink>
                        <span v-else v-html="item.title"></span>									
                    </p>
                </div>
            </BaseCmsNav>

            <BaseCmsNav code="miele_col2" v-slot="{items}">
                <div v-if="items.length" class="flex-col">
                    <ul>
                        <li v-for="item in items" :key="item.id" :class="[item.style && item.style]">
                            <NuxtLink v-if="item.url_without_domain?.trim()" :to="item.url_without_domain" :target="item.target_blank != 0 ? '_blank' : null"><span v-html="item.title"></span></NuxtLink>									
                            <span v-else v-html="item.title"></span>									
                        </li>
                    </ul>
                </div>
            </BaseCmsNav>

            <BaseCmsNav code="miele_col3" v-slot="{items}">
                <div v-if="items.length" class="flex-col">
                    <ul>
                        <li v-for="item in items" :key="item.id" :class="[item.style && item.style]">
                            <NuxtLink v-if="item.url_without_domain?.trim()" :to="item.url_without_domain" :target="item.target_blank != 0 ? '_blank' : null"><span v-html="item.title"></span></NuxtLink>									
                            <span v-else v-html="item.title"></span>									
                        </li>
                    </ul>
                </div>
            </BaseCmsNav>
        </div>
    </div>
</template>

<script setup>
	const labels = useLabels();
</script>