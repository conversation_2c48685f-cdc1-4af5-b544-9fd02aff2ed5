<template>
	<div v-if="groupedItems.length" :class="['c-pr-cnt', mode == 'hp-promos-dark' && 'c-pr-cnt-dark']">
        <template v-for="(row, rowIndex) in groupedItems" :key="'row-' + rowIndex">
            <div :class="['c-pr-row', rowIndex === 0 ? 'c-pr-row1' : 'c-pr-row-2', getTemplateClass(row[0].template)]">
                <div v-for="item in row" :key="item.id" :class="['c-pr-item', mode === 'hp-promos-dark' && 'c-pr-item-dark', getItemClass(item)]">
                    <component :is="item.link ? NuxtLink : 'span'" class="c-pr-item-img" :to="item.link ? item.url_without_domain : undefined">
                        <BaseUiImage :data="getImage(item)" :default="getNoImage(item)" :alt="item.title" loading="lazy" />
                    </component>

                    <div v-if="item.title" class="c-pr-content">
                        <component :is="item.link ? NuxtLink : 'div'" class="c-pr-title" :to="item.link ? item.url_without_domain : undefined">{{item.title}}</component>
                        <div v-if="item.title2" class="c-pr-subtitle" v-html="item.title2" />
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<script setup>
    import { NuxtLink } from '#components';
    const props = defineProps(['mode', 'items']);
    const {onMediaQuery} = useDom()

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 750px)',
	});   

    const groupedItems = computed(() => {
        const groups = []
        let currentGroup = []
        let lastTemplate = null

        for (const item of props.items) {
            if (lastTemplate !== null && item.template !== lastTemplate) {
                groups.push(currentGroup)
                currentGroup = []
            }
            currentGroup.push(item)
            lastTemplate = item.template
        }

        if (currentGroup.length) {
            groups.push(currentGroup)
        }

        return groups
    });

    function getTemplateClass(template) {
        if (template === 'promo-one_in_row') return 'tmp-row1'
        if (template === 'promo-three_in_row') return 'tmp-row3'
        return 'tmp-row2'
    }

    function getItemClass(item) {
        if (item.template === 'promo-one_in_row') return 'c-pr-item-one'
        if (item.template === 'promo-three_in_row') return 'c-pr-item-three'

        return item.element_size === 'size_bigger' ? 'c-pr-item-two-bigger' : 'c-pr-item-two-smaller';
    }    

    function getImage(item) {
        if (item.template === 'promo-one_in_row') {
            if (item.image_2 && mobileBreakpoint.value) {
                return item.image_2_thumbs?.['width980-height670-crop1']
            }
            return item.image_thumbs?.['width1400-height400-crop1']
        }

        if (item.template === 'promo-three_in_row') {
            return item.image_thumbs?.['width980-height670-crop1']
        }

        if (item.element_size === 'size_bigger') {
            if (item.image_2 && mobileBreakpoint.value) {
            return item.image_2_thumbs?.['width980-height670-crop1']
            }
            return item.image_thumbs?.['width920-height300-crop1']
        }

        return item.image_thumbs?.['width980-height670-crop1']
    }

    function getNoImage(item) {
        if (item.template === 'promo-one_in_row') return '/images/no-image-1400.jpg'
        if (item.template === 'promo-three_in_row') return '/images/no-image-980.jpg'
        if (item.element_size === 'size_bigger') return '/images/no-image-920.jpg'
        return '/images/no-image-980.jpg'
    }
</script>