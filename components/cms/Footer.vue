<template>
	<footer class="footer">
		<div class="wrapper wrapper-footer">
			<div class="footer-row footer-row1">
				<div class="footer-col footer-col1">
					<div class="footer-title" v-html="labels.get('footer_col1_title')"></div>
					<div class="footer-col-cnt">
						<BaseCmsLabel code="footer_col1" tag="div" />
						<BaseCmsLabel code="social" class="social-footer" tag="div" />
					</div>
				</div>
				<div class="footer-col footer-col2" :class="{ active: fCol2 }">
					<div class="footer-title" @click="fCol2 = !fCol2" v-html="labels.get('footer_col2_title')"></div>
					<div class="footer-col-cnt">
						<BaseCmsNav code="footer_col2" v-slot="{items, currentUrl}">
							<ul class="nav-footer-col" v-if="items?.length">
								<li v-for="item in items" :key="item.id">
									<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain" :class="{'active': currentUrl == item.url_without_domain}">{{ item.title }}</NuxtLink>
								</li>
							</ul>
						</BaseCmsNav>
					</div>
				</div>
				<div class="footer-col footer-col3" :class="{ active: fCol3 }">
					<div class="footer-title" @click="fCol3 = !fCol3" v-html="labels.get('footer_col3_title')"></div>
					<div class="footer-col-cnt">
						<BaseCmsNav code="footer_col3" v-slot="{items, currentUrl}">
							<ul class="nav-footer-col" v-if="items?.length">
								<li v-for="item in items" :key="item.id" :class="{'selected': currentUrl == item.url_without_domain}">
									<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain" :class="{'active': currentUrl == item.url_without_domain}">{{ item.title }}</NuxtLink>
								</li>
								<li><a class="footer-gdpr-popup-btn-edit gdpr_configurator_button" id="gdpr_configurator_button" href="javascript:void(0);"><BaseCmsLabel code="gdpr_edit" /></a></li>
							</ul>
						</BaseCmsNav>
					</div>
				</div>
				<div class="footer-col footer-col4" :class="{ active: fCol4 }">
					<BaseCmsLabel code="footer_col4_title" class="footer-title" tag="div" @click="fCol4 = !fCol4" />
					<div class="footer-col-cnt">
						<BaseCmsLabel code="footer_col4" tag="div" />
						<div class="cards" v-html="addLazyToImages(labels.get('cards'))"></div>
					</div>
				</div>
			</div>
			
			<div class="footer-row footer-row2">
				<div class="footer-col footer-col1" v-html="addLazyToImages(labels.get('footer_banner_col1'))"></div>
				<div class="footer-col footer-col2" v-html="addLazyToImages(labels.get('footer_banner_col2'))"></div>
				<div class="footer-col footer-col3" v-html="addLazyToImages(labels.get('footer_banner_col3'))"></div>
				<div class="footer-col footer-col4">
					<div class="footer-col3-safe-purchase" v-html="addLazyToImages(labels.get('safe_purchase'))"></div>
				</div>
			</div>

			<div class="footer-row footer-row3">
				<div class="footer-col footer-col-locations">
					<CmsLocations class="nav-footer nav-footer-locations" />
				</div>
			</div>

			<div class="footer-row footer-row4">
				<div class="footer-col footer-col1">
					<BaseCmsCopyright class="copy" tag="p" label="copyright" />
				</div>
				<div class="footer-col footer-col-dev">
					<p class="dev">
						<CmsSignatureOverride />
					</p>
				</div>
			</div>

			<div class="footer-row footer-row5">
				<BaseCmsLabel code="footer" class="footer-col-cnt" tag="div" />
			</div>
		</div>
	</footer>
</template>

<script setup>
	const {onMediaQuery, insertAfter} = useDom();
	const labels = useLabels();
	let fCol2 = ref(false);
	let fCol3 = ref(false);
	let fCol4 = ref(false);

	onMediaQuery({
		query: '(max-width: 750px)',
		enter: () => {
			insertAfter('.footer-col4 .cards', '.footer-row2 .footer-col4');
		},
	});
</script>