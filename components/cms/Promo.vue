<template>
	<BaseCmsRotator v-if="rotatorData" :fetch="rotatorData" v-slot="{items}">
        <div v-if="items?.length" :class="['promo', 'display-f', modeClass && modeClass]">
			<div :class="['promo-col', 'promo-col-'+item.template, 'promo-col'+index]" v-for="(item, index) in items" :key="item.id">
                <component :is="item.link ? NuxtLink : 'span'" :to="item.link ? item.url_without_domain : undefined">
                    <BaseUiImage :data="item.template == 'promo-half' ? item.image_thumbs?.['width675-height320-crop1'] : item.image_thumbs?.['width435-height300-crop1']" :picture="item.image_2 ? [{maxWidth: '750px', src: item.image_2_thumbs?.['width640-height480-crop1']?.thumb, default: '/no-image-300.jpg'}] : []" loading="lazy" default="/images/no-image-300.jpg" />
                </component>
            </div>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
    import { NuxtLink } from '#components';
    const props = defineProps(['categoryId', 'pageId', 'modeClass']);
    
    const rotatorData = computed(() => {
        const finalData = {code: 'promo', limit: 20, response_fields: ['id','link','url_without_domain','image_upload_path','image_thumbs','image_2','image_2_thumbs','template']}
        if(props.categoryId) finalData.catalogcategory_id = props.categoryId;
        if(props.pageId) finalData.page_id = props.pageId;
        return finalData;
    })
</script>