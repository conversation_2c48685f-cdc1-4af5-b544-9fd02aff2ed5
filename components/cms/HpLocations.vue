<template>
    <div class="display-f locations">
        <div class="flex-col locations-col1">
            <div class="panel-support">
                <div class="panel-support-header" v-html="labels.get('support_panel_title')"></div>
                <div class="panel-support-footer" v-html="labels.get('support_panel_content')"></div>
            </div>
        </div>
        <div class="flex-col locations-col2">
            <div class="panel-stores">
                <div class="panel-stores-title" v-html="labels.get('locations_panel_title')"></div>
                <CmsLocations class="nav-footer nav-stores" />
            </div>
        </div>
    </div>	
</template>

<script setup>
    const labels = useLabels();
</script>