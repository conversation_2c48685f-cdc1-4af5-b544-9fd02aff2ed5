<template>
	<aside class="sidebar">
		<div class="sidebar-container">
			<template v-if="mode == 'auth'">
				<BaseAuthUser v-slot="{urls, currentUrl}">
					<ul class="nav-sidebar nav-auth-sidebar">
						<li class="auth-profile" :class="{'selected': urls.auth == currentUrl}"><NuxtLink :to="urls.auth" ><BaseCmsLabel code="my_profile" tag="span" /></NuxtLink></li>
						<li class="auth-orders" :class="{'selected': urls.auth_my_webshoporder == currentUrl}"><NuxtLink :to="urls.auth_my_webshoporder+'#main'" ><BaseCmsLabel code="my_orders" tag="span" /></NuxtLink></li>
						<li class="auth-coupons" :class="{'selected': urls.auth_my_webshopcoupon == currentUrl}"><NuxtLink :to="urls.auth+'#coupons'"><BaseCmsLabel code="coupons" tag="span" /></NuxtLink></li>
						<li class="auth-wishlist" :class="{'selected': urls.auth_wishlist == currentUrl}"><NuxtLink :to="urls.auth_wishlist"><BaseCmsLabel code="my_wishlist" tag="span" /></NuxtLink></li>
						<li class="auth-edit" :class="{'selected': urls.auth_edit == currentUrl}"><NuxtLink :to="urls.auth_edit"><BaseCmsLabel code="edit_profile" tag="span" /></NuxtLink></li>
						<li class="auth-password" :class="{'selected': urls.auth_change_password == currentUrl}"><NuxtLink :to="urls.auth_change_password"><BaseCmsLabel code="change_password" tag="span" /></NuxtLink></li>
						<li class="auth-logout"><NuxtLink :to="urls.auth_logout+'?redirect=/'"><BaseCmsLabel code="logout" tag="span" /></NuxtLink></li>
					</ul>
				</BaseAuthUser>
			</template>
			<template v-else>
				<BaseCmsNav code="sidebar" v-slot="{items}">
					<ul v-if="items.length" class="nav-sidebar">
						<li v-for="item in items" :key="item.id">
							<NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain"><span>{{ item.title }}</span></NuxtLink>
						</li>
					</ul>
				</BaseCmsNav>
			</template>
		</div>

		<div class="sidebar-cols">
			<div class="sidebar-col sidebar-col1 sidebar-flyer">
				<CmsFlyer />
			</div>
			<BaseCmsLabel code="advisor" class="sidebar-col sidebar-col2 sidebar-advisor" tag="div" />
		</div>
		<div class="sidebar-help" v-if="mode != 'contact'">
			<BaseCmsLabel code="help_and_support_title" class="sidebar-help-title" tag="p" />
			<BaseCmsLabel code="help_and_support" tag="div" />
		</div>
	</aside>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>