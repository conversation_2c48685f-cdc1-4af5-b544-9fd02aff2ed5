<template>
	<BaseCmsShare :networks="['facebook', 'whatsapp', 'viber']" v-slot="{items, onShare}">
		<div class="share" v-if="items?.length" :class="[mode]">
			<BaseCmsLabel code="share_with_friends" class="share-label" tag="div" />
			<div v-for="network in items" :key="network" :class="['item', 'ss_' + network.code]" :title="network.title" @click="onShare(network)"></div>
		</div>
	</BaseCmsShare>
</template>

<script setup>
	const props = defineProps(['mode']);
</script>

<style scoped lang="less">
</style>
