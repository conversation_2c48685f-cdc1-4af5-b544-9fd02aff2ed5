<template>
	<div class="nw">
		<div class="nw-col nw-col1">
			<div class="nw-col1-cnt">
				<BaseCmsLabel code="newsletter_widget_title" class="nw-title" tag="div" />
				<BaseCmsLabel code="newsletter_widget_subtitle" class="nw-subtitle" tag="div" />
				<BaseNewsletterSignupForm class="nw-form" v-slot="{fields, gdprFields, status, loading}">
					<template v-if="!status?.success">
						<div class="nw-form-cnt">
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage}">
								<template v-if="item.type != 'hidden'">
									<BaseFormInput :id="`newsletter-${item.name}`" class="nw-input" :placeholder="labels.get('nw_enter_email')" />
									<span class="nw-error newsletter-error" v-show="errorMessage" v-html="errorMessage" />
								</template>
								<BaseFormInput v-else />
							</BaseFormField>
							<button class="btn-white nw-button g-recaptcha" type="submit"><UiLoader v-if="loading" /><BaseCmsLabel v-if="!loading" code="newsletter_signup_btn" tag="span" /></button>
						</div>

						
						<div class="nw-checkbox" v-if="gdprFields">
							<BaseFormField v-for="field in gdprFields" :key="field.name" :item="field">
								<BaseFormInput v-if="field.type == 'hidden'" />
								<div class="field" v-else v-interpolation>
									<BaseFormInput :id="`nl-${field.name}`" />
									<label :for="`nl-${field.name}`"><span v-html="field.description"></span></label>
								</div>
							</BaseFormField>
						</div>
					</template>
					<BaseCmsLabel v-show="status?.success" tag="div" class="nw-success newsletter_subscribe_success" code="success_subscribe" />
					<BaseCmsLabel code="newsletter_widget_note" class="nw-note" tag="div" />
				</BaseNewsletterSignupForm>
			</div>
		</div>
		<div class="nw-col nw-col2"></div>
	</div>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
	.nw-checkbox{
		:deep(p){padding-bottom: 0;}
	}
	.nw-error{
		@media (max-width: @m){display: block; width: 100%;}
	}
</style>
