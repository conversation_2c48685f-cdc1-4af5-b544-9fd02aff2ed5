<template>
    <BaseCmsRotator :fetch="{code: 'benefits', limit: 4, response_fields: ['id', 'link2', 'title', 'title2', 'url_without_domain']}" v-slot="{items}">
        <BaseUiSwiper class="benefits" :options="sliderOptions" v-if="mobileBreakpoint">
            <BaseUiSwiperSlide v-for="item in items" :key="item.id" class="benefit-item">
				<NuxtLink :class="['benefit', item.link2 && item.link2]" :to="item.url_without_domain">
					{{item.title}}
					<span v-if="item.title2">{{item.title2}}</span>
				</NuxtLink>
			</BaseUiSwiperSlide>
        </BaseUiSwiper>

        <template v-else>
			<div v-if="items?.length" class="benefits">
				<div v-for="item in items" :key="item.id" class="benefit-item">
					<NuxtLink :class="['benefit', item.link2 && item.link2]" :to="item.url_without_domain">
						{{item.title}}
						<span v-if="item.title2">{{item.title2}}</span>
					</NuxtLink>
				</div>
			</div>
		</template>
    </BaseCmsRotator>
</template>

<script setup>
	const {onClickOutside, onMediaQuery} = useDom();
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 980px)',
	});

	
	const sliderOptions = {
		slidesPerView: 1,
		slidesPerGroup: 1,
		speed: 500,
		effect: 'fade',
		fadeEffect: {
			crossFade: true
		},
		loop: true,
		autoplay: {
			delay: 5000,
			disableOnInteraction: true
		},
		navigation: {
			enabled: false,
		},
		breakpoints: {
			980: {
				enabled: false,
			}
		}
	}
</script>
