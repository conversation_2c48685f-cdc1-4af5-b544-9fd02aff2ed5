<template>
    <ClientOnly>
        <LazyCmsHelloBar :hydrate-after="1000" />
    </ClientOnly>
        
    <Body :class="{'active-nav': mMenuActive}" />

    <div v-if="labels.get('corner')" class="corner-info">
        <div class="corner-info-cnt" v-html="labels.get('corner')"></div>
    </div>

    <header class="header">
        <div class="wrapper wrapper-header pos-r">
            <BaseCmsLogo class="logo" />
            <div class="btn-toggle-nav" @click="mMenuActive = !mMenuActive"><span></span></div>
            
            <div class="nav-container">
                <BaseCmsNav code="main" v-slot="{items}">
                    <ul v-if="items.length" class="nav">
                        <li v-for="item in items" :key="item.id">
                            <NuxtLink :target="item.target_blank != 0 ? '_blank' : null" :to="item.url_without_domain">{{ item.title }}</NuxtLink>
                        </li>
                    </ul>
                </BaseCmsNav>
                <div class="social social-top" v-html="labels.get('social')"></div>                                             
            </div>

            <SearchForm />

            <div class="top-contact" v-html="labels.get('top_contact')"></div> 

            <AuthUserBox v-if="!mobileBreakpoint" /> 

            <div class="top-container">
                <div class="top-flyer">
                    <CmsFlyer /> 
                </div>

                <ClientOnly>
                    <BaseCatalogWishlistWidget v-slot="{counter, wishlistUrl}">
                        <div class="wishlist" :class="{'active': counter > 0}">
                            <NuxtLink :to="wishlistUrl">
                                <span class="wishlist_count counter wishlist-counter">{{counter}}</span>
                            </NuxtLink>
                        </div>
                    </BaseCatalogWishlistWidget> 

                    <BaseCatalogCompareWidget v-slot="{counter, compareUrl}">
                        <div class="cw-compare" :class="{'active': counter}">
                            <NuxtLink :to="compareUrl">                                
                                <span class="compare_count cw-compare-counter">{{counter}}</span>
                            </NuxtLink>
                        </div>
                    </BaseCatalogCompareWidget>                    
                    
                    <AuthUserBox v-if="mobileBreakpoint" /> 
                    
                    <WebshopShoppingCartBtn />

                </ClientOnly>
            </div>  
            
            <CmsCategoriesNav @closeNav="mMenuActive = false" />
        
        </div>
    </header>
    <div class="header-placeholder"></div>
    
</template>

<script setup>
    const labels = useLabels();
    const mMenuActive = ref(false);
    const {onMediaQuery} = useDom();

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 750px)',
	});
</script>
