<template>
	<BaseCmsRotator :fetch="{code: 'flyer', limit: 1, response_fields: ['id','code', 'link','url_without_domain','title','title2','image_upload_path','image_thumbs']}" v-slot="{items}">
		<template v-if="items?.length">
			<template v-for="item in items" :key="item.id">
				<NuxtLink :to="item.url_without_domain">
					<div v-html="item.title"></div>
					<span v-if="item?.title2">{{item.title2}}</span>
					<BaseUiImage v-if="item?.image_upload_path" :data="item.image_thumbs?.['width100-height300']" loading="lazy" default="/images/no-image-100.jpg" />
				</NuxtLink>
			</template>
		</template>
	</BaseCmsRotator>
</template>