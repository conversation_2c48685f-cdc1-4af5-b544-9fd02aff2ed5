<template>
	<span>
		<template v-if="mode == 'standard'">
			<a href="https://marker.hr/izrada-web-stranica/" rel="nofollow" :title="title" target="_blank" class="dev-signature">{{ title }} </a>:
		</template>
		<template v-else>
			<a href="https://marker.hr/izrada-web-shopa/" rel="nofollow" :title="title" target="_blank" class="dev-signature">{{ title }} </a>:
		</template>

		<a href="https://marker.hr/" rel="nofollow" target="_blank">Marker.hr</a>
		<BaseUtilsVersion />
	</span>
</template>

<script setup>
	const lang = useLang();
	const currentLang = ref(lang.get());
	const props = defineProps(['mode']);

	const title = computed(() => {
		// web sites
		if (props.mode == 'standard') {
			if (currentLang.value == 'si') {
				return 'Izdelava spletnih strani';
			} else if (currentLang.value == 'en') {
				return 'Website development';
			} else if (currentLang.value == 'de') {
				return 'Website Entwicklung';
			} else {
				return 'Izrada web stranica';
			}
		}

		// webshop
		if (currentLang.value == 'si') {
			return 'Izdelava spletne strani';
		} else if (currentLang.value == 'en') {
			return 'E-commerce development';
		} else if (currentLang.value == 'de') {
			return 'E-commerce Entwicklung';
		} else {
			return 'Izrada web shopa';
		}
	});
</script>
