<template>
	<div v-if="items" id="mapContainer" class="map-contact" :class="[mode]">
		<BaseLocationGoogleMap pin="/images/icons/pin1.svg" :locations="items">
			<template v-slot="{item}">
				<div class="infoBox">
					<span class="infoBox-cnt">
						<span class="image" v-if="item.main_image">
							<BaseUiImage :data="item.main_image_thumbs?.['width250-height135-crop1']" loading="lazy" default="/images/no-image-50.jpg" />
						</span>
						<span class="infoBox-body">
							<span v-if="item.title" class="title" v-html="item.title" />
							<span v-if="item.address" class="address" v-html="item.address" />
							<span v-if="item.contact" class="contact" v-html="item.contact" />
							<span v-if="item.business_hour" class="business-hour">
								<div class="working-hours"><BaseCmsLabel code="working_hours" tag="strong" />:</div>
								<span v-html="item.business_hour"></span>
							</span>
						</span>
					</span>
				</div>
			</template>
		</BaseLocationGoogleMap>
	</div>
</template>

<script setup>
	const props = defineProps(['items','mode']);
	let items = props.items;
</script>

<style lang="less" scoped>
	.map{height: 100%; margin-top: 0;}
	.map-contact{height: 550px;}
	:deep(.gm-style-iw-chr){display: none!important;}
	.gm-style-iw{width:100%!important;}

	//Locations map
	.locations-map{height: 450px; margin-top: 20px;}
	:deep(.infoBox-window){
		position: relative; left: 25px; top: -65px;
		.infoBox-close{
			position: absolute; display: flex; align-items: center; justify-content: center; top: 30px; right: 30px; width: 20px; height: 20px; cursor: pointer; z-index: 100; font-size: 0;
			&:before{content: ""; width: 100%; height: 100%; background: url(assets/images/close.png) no-repeat; background-size: contain;}
		}
		@media (max-width: @tp){left: 15px; top: -35px;}
	}
</style>