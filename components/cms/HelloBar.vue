<template>
	<BaseCmsRotator @loadRotatorItems="onLoad" :fetch="{code: 'hello_bar', limit: 1}" v-slot="{items}">
			<Body :class="!isHidden && 'hellobar-active'" />
		<div v-if="items?.length && !isHidden" class="hello-bar">
			<div class="hello-bar-wrapper wrapper" :class="{'no-counter': !item.date_active_to}" v-for="item in items" :key="item.id">
				<div class="hello-bar-image" v-if="item.image_upload_path">
					<BaseUiImage :data="item.image_thumbs?.['width118-height100-crop1']" default="/images/no-image-100.jpg" loading="lazy" />
				</div>
				<div class="hello-bar-content">
					<div class="hello-bar-desc" v-html="item.content" v-interpolation v-if="item?.content"></div>
					<div class="hello-bar-subtitle" v-html="item.title2" v-interpolation v-if="item?.title2"></div>
				</div>
				
				<BaseAuthCoupons v-slot="{onActivate, activatedCoupons}">
					<div class="hello-bar-coupon-cnt" :class="{active: activatedCoupons?.length && activatedCoupons.includes(item.coupon_code)}" v-if="item?.coupon_code">
						
						<div class="activated-coupon-note" v-if="item?.element_hellobar_message_content">
							{{ item.element_hellobar_message_content }}
						</div>					

						<div
							class="hellobar-coupon-btn"
							@click="activatedCoupons?.length && activatedCoupons.includes(item.coupon_code) ? '' : onActivate(item.coupon_code)"
							:data-list_link="item.element_hellobar_message_button_link || ''"
						>
							<BaseCmsLabel code="hellobar_coupon_title_inactive" class="i" tag="span" />
							<BaseCmsLabel code="hellobar_coupon_title_active" class="a" tag="span" />
						</div>
					</div>

					<div class="hello-bar-timer-cnt" :class="{'active-coupon': activatedCoupons?.length && activatedCoupons.includes(item.coupon_code)}" v-if="item?.date_active_to">
						<BaseUiCountdown :end="item.date_active_to" v-slot="{days, hours, minutes, seconds, ended}">
							<div class="hello-bar-timer" v-if="!ended">
								<div v-if="days && days > 1" class="days">
									<span>{{ days }}</span> d
								</div>
								<div class="hour">
									<span>{{ hours }}</span> h
								</div>
								<div class="min">
									<span>{{ minutes }}</span> m
								</div>
								<div class="sec">
									<span>{{ seconds }}</span> s
								</div>
							</div>
						</BaseUiCountdown>
					</div>
				</BaseAuthCoupons>
			</div>
			<div class="hello-bar-close" @click="close"></div>
		</div>
	</BaseCmsRotator>
</template>

<script setup>
	const isHidden = ref(true);
	let rotatorItemId = 0;

	function onLoad(data) {
		rotatorItemId = data?.items?.[0]?.id;
		const cookie = useCookie('hello_bar_'+rotatorItemId);
		if(!cookie.value) {
			isHidden.value = false;
		}
	}

	function close() {
		const cookie = useCookie('hello_bar_'+rotatorItemId, {
			maxAge: 60 * 60 * 24 * 30 // 1 month
		});
		cookie.value = 1;
		isHidden.value = true;
	}
</script>

<style lang="less" scoped>
	.hello-bar{
		background: var(--gray); display: flex; overflow: hidden; position: relative; z-index: 1; height: 96px;
		@media (max-width: @t){height: 80px;}
		@media (max-width: @tp){height: auto;}
	}
	.no-counter{
		justify-content: center;
		.hello-bar-content{margin: auto; text-align: center;}
		@media (max-width: @tp){
			.hello-bar-wrapper{padding: 12px 0 10px;}
		}
	}
	
	.hello-bar-wrapper{
		display: flex; align-items: center; min-height: 96px; position: relative; width: 100%; justify-content: space-between;
		@media (max-width: 1680px){justify-content: unset;}
		@media (max-width: @t){width: 100%; min-height: 80px;}
		@media (max-width: @tp){padding: 10px 0; min-height: 0; flex-flow: column;}
	}
	.hello-bar-image{
		display: block; position: absolute; left: -145px;
		:deep(img){display: block; width: 100%; height: auto; max-width: 118px; max-height: 100px;}
		@media (max-width: 1680px){position: relative; left: unset;}
		@media (max-width: 1440px){
			:deep(img){max-height: 90px; max-width: 106px;}
		}
		@media (max-width: @t){
			:deep(img){max-height: 80px; max-width: 94px;}
		}
		@media (max-width: @tp){
			position: absolute; left: -30px; top: 12px;
			:deep(img){max-height: 56px; max-width: 66px;}
		}
		@media (max-width: @m){left: -25px;}
	}
	.hello-bar-content{
		font-size: 16px; line-height: 1.3; color: var(--white); font-weight: bold; display: flex; flex-flow: column; padding-left: 40px;
		:deep(p){padding-bottom: 0;}
		:deep(a){
			font-weight: bold; color: var(--white); text-underline-offset: 3px; .transition();
			&:hover{color: var(--white); text-decoration-color: transparent;}
		}
		@media (max-width: 1680px){padding-left: 20px;}
		@media (max-width: 1440px){padding-left: 15px;}
		@media (max-width: @t){font-size: 14px;}
		@media (max-width: @tp){margin-right: unset; width: 100%; align-items: center; order: 2; font-size: 12px; padding: 0 20px 10px; text-align: center;}
		@media (max-width: @m){padding-right: 45px; padding-left: 45px;}
	}
	.hello-bar-desc{
		font-size: 28px; line-height: 40px; font-weight: 300;
		:deep(strong){color: var(--yellow); font-weight: bold;}
		@media (max-width: @t){font-size: 20px; line-height: 22px;}
		@media (max-width: @tp){font-size: 18px; line-height: 19px; padding-bottom: 5px;}
	}

	//Coupon
	.hello-bar-coupon-cnt{
		display: flex; align-items: center; flex-shrink: 0; position: relative;
		&.active{
			.activated-coupon-note{display: block;}
			.hellobar-coupon-btn{
				background: #0EAD69; padding: 0 59px 0 18px; box-shadow: none; color: var(--white);
				&:before{left: auto; right: 5px; .icon-check; background: var(--white); color: var(--textColor); font-weight: bold; font-weight: normal; text-indent: 1px; padding-top: 0; font-size: 13px; line-height: 13px; .rotate(0); z-index: 1;}
				.i{display: none;}
				.a{display: block;}
				@media (max-width: @t){
					padding: 0 50px 0 21px;
					&:before{font-size: 9px; line-height: 9px; left: auto; right: 4px;}
				}
			}
		}
		@media (max-width: 1680px){margin-left: auto;}
		@media (max-width: @tp){order: 3; padding: 5px 0 0; flex-flow: column; margin: 0;}
	}
	.hellobar-coupon-btn{
		font-size: 18px; line-height: 21px; font-weight: bold; color: var(--textColor); text-transform: uppercase; background: var(--white); box-shadow: 0 5px 20px 0 rgba(43, 63, 33, 0.3); flex-grow: 0; flex-shrink: 0; text-decoration: none; position: relative; top: auto; right: auto; width: auto; height: 55px; padding: 0 28px 0 63px; display: flex; align-items: center; justify-content: center; border-radius: 28px; cursor: pointer;
		&:before{.pseudo(45px,45px); background: #0EAD69; top: 5px; left: 5px; border-radius: 200px; display: flex; align-items: center; justify-content: center; .icon-arrow-right; font: 15px/15px var(--fonti); text-indent: 4px; color: var(--white); font-weight: bold;}
		.a{display: none;}
		@media (max-width: @t){
			font-size: 14px; height: 40px; padding: 0 30px 0 52px;
			&:before{width: 32px; height: 32px; font-size: 10px; line-height: 10px; text-indent: 1px; top: 4px; left: 4px;}
		}
		@media (max-width: @tp){line-height: 20px;}
	}
	.activated-coupon-note{
		display: none; font-size: 14px; line-height: 18px; color: var(--white); margin-right: 16px; text-align: right; max-width: 160px;
		//@media (max-width: @l){margin-right: 12px;}
		@media (max-width: @t){font-size: 12px; line-height: 16px; margin-right: 10px;}
		@media (max-width: @tp){order: 2; max-width: unset; padding-top: 6px; margin-right: 0;}
	}

	.hello-bar-timer-cnt{
		flex-shrink: 0; width: auto; position: relative;
		@media (max-width: 1680px){margin-left: auto;}
		@media (max-width: 1515px){padding-right: 20px;}
		@media (max-width: 1440px){padding-right: 10px;}
		@media (max-width: @tp){width: 100%; order: 1; padding: 0 0 5px; margin: 0;}
	}
	.hello-bar-timer{
		font-size: 18px; line-height: 1; color: var(--white); display: flex; flex: 0 0 auto; margin-left: auto;
		@media (max-width: @t){font-size: 14px;}
		@media (max-width: @tp){justify-content: center;}
		span{
			display: inline-block; font-size: 40px; font-weight: bold; text-align: right;
			@media (max-width: @t){font-size: 28px;}
			@media (max-width: @tp){font-size: 24px;}
		}
		.sec span{
			width: 40px;
			@media (max-width: @t){width: 28px;}
		}
		&>div:first-child{
			@media (max-width: @tp){margin-left: 0;}
		}
		&>div{
			margin-left: 24px;
			@media (max-width: 1440px){margin-left: 15px;}
			@media (max-width: @t){margin-left: 12px;}
		}
	}

	.hello-bar-close{
		display: flex; align-items: center; justify-content: center; position: absolute; right: 39px; top: 48px; flex-shrink: 0; cursor: pointer; z-index: 1; .transition(opacity);
		&:before{.icon-close; font: 18px/18px var(--fonti); color: var(--white); font-weight: bold; position: absolute;}
		@media (max-width: 1515px){right: 10px;}
		@media (max-width: 1440px){right: 17px;}
		@media (max-width: @t){
			top: 39px;
			&:before{font-size: 14px; line-height: 14px;}
		}
		@media (max-width: @tp){
			top: 10px; right: 10px; width: 12px; height: 12px;
			&:before{font-size: 12px; line-height: 12px;}
		}
	}
</style>
