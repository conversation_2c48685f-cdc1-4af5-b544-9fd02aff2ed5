<template>
	<div class="cp-groupp">
		<figure class="cp-groupp-image">
			<NuxtLink :to="item.url_without_domain">
				<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width180-height180']" default="/images/no-image-100.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : ''" />
			</NuxtLink>	
		</figure>

		<div class="cp-groupp-cnt">
			<ClientOnly>
				<div v-if="item.feedback_rate_widget?.rates_status > 1 && item.feedback_rate_widget?.rates_votes > 0" class="cp-rating-container">
					<FeedbackRates :rates="item.feedback_rate_widget.rates" />
				</div>
			</ClientOnly>

			<h2 v-if="item.title" class="cp-groupp-title"><NuxtLink :to="item.url_without_domain">{{item.title}}</NuxtLink></h2>

			<div v-if="item?.type == 'groupproduct' && item?.groupproducts_ids" class="cp-groupp-items-cnt">
				<ClientOnly>
					<BaseCatalogProductsWidget :fetch="{id: item.groupproducts_ids.split(',').filter(id => id.trim() !== ''), limit: 20}" v-slot="{items: groupProducts}">
						<div v-if="groupProducts?.length" class="cp-groupp-items-title"><BaseCmsLabel code="group_product_title" /></div>
						<div v-if="groupProducts?.length" class="cp-groupp-items">
							<div v-for="item in groupProducts" :key="item.id" class="cp-groupp-item">
								<NuxtLink :to="item.url_without_domain">{{item.title}}</NuxtLink>
							</div>
						</div>
					</BaseCatalogProductsWidget>
				</ClientOnly>
			</div>
		</div>

		<div class="cp-groupp-third">
			<div class="cp-groupp-third-top">
				<div class="cp-groupp-price-cnt">
					<div v-if="item.discount_percent_base > 0 || item.price_custom < item.basic_price_base" class="cp-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_base" /></div>
					<div v-if="item?.price_custom && item.price_custom > 0" class="cp-groupp-price" :class="{'red': item.discount_percent_base > 0 || item.price_custom < item.basic_price_base}"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></div>
				
					<span v-if="item.extra_price_lowest && item.extra_price_lowest > 0" class="cp-extra-price-lowest">
						<BaseCmsLabel code="extra_price_lowest" />:
						<strong>
							<BaseUtilsFormatCurrency :price="item.extra_price_lowest" />
						</strong>
					</span>
					<div v-if="item?.price_custom < item.basic_price_base" class="cp-groupp-save"><BaseCmsLabel code="price_save" /> <BaseUtilsFormatCurrency :price="item.basic_price_base - item.price_custom" /></div>
				</div>
			</div>

			<div class="cp-groupp-third-bottom">

					<CatalogSetWishlist v-if="item.wishlist_widget" :item="item" />

					<CatalogSetCompare v-if="item.compare_widget" :item="item" />

					<BaseWebshopAddToCart v-if="item.is_available && item.available_qty > 0" v-slot="{onAddToCart, loading}" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code}">
						<button type='button' :class="['btn btn-cp-add-detail cp-btn-addtocart', loading && 'loading']" @click="onAddToCart"><UiLoader v-if="loading" /><span v-if="!loading" v-html="labels.get('add_to_shopping_cart')"></span></button>
					</BaseWebshopAddToCart>
					<NuxtLink v-else class="btn btn-cp-add-detail cp-btn-details" :to="item.url_without_domain" :title="labels.get('more_info')"><BaseCmsLabel code="more_info" /></NuxtLink>                

			</div>


		</div>
	</div>
</template>

<script setup>
	const props = defineProps(['item']);
	const labels = useLabels();
</script>

<style lang="less" scoped>
	.cp-groupp{
		position: relative; display: flex; border: 1px solid var(--borderColor); width: calc(~'50% - 10px'); padding: 20px 15px 20px 15px;
		@media (max-width: @tp){width: 100%; padding: 17px 16px;}
		@media (max-width: @m){margin: -1px 0 0 0; flex-wrap: wrap;}
	}
	.cp-groupp-image{
		flex: 1 1 180px; margin-right: 15px; max-width: 180px;
		
		@media (max-width: @m){flex: 1 0 104px; max-width: 104px; margin-right: 12px;}
		:deep(img){display: block; max-width: 100%; width: auto; height: auto; max-height: 180px;}
	}

	.cp-groupp-cnt{
		flex: 1 1 250px; margin-right: 14px; display: flex; flex-flow: column;
		@media (max-width: @m){flex: 1 1 calc(~"100% - 116px"); margin-right: 0;}
	}
	.cp-groupp-title{
		font-size: 22px; line-height: 26px; padding: 0; text-transform: initial;
		@media (max-width: @tp){font-size: 16px; line-height: 18px;}
		a{color: var(--textColor);}
	}
	.cp-groupp-items-cnt{padding: 5px 0 0; flex: 1 1 auto;}
	.cp-groupp-items-title{font-size: 13px; line-height: 18px; font-weight: bold; padding-bottom: 3px;}
	.cp-groupp-item{
		font-size: 13px; line-height: 18px; font-weight: normal; margin-bottom: 5px;
		a{
			display: inline-block;
			@media (min-width: @h){
				&:hover{text-decoration: none;}
			}
		}
	}

	.cp-groupp-third{
		display: flex; flex-flow: column; flex: 0 0 204px; position: relative;
		@media (max-width: @t){flex: 0 0 145px;}
		@media (max-width: @tp){flex: 0 0 204px;}
		@media (max-width: @m){flex: unset; width: 100%; margin-top: 20px;}
	}
	.cp-groupp-price{
		font-size: 30px; font-weight: bold; position: relative;
		@media (max-width: @m){font-size: 26px;}
	}
	.cp-groupp-save{
		font-size: 15px; line-height: 15px; font-weight: bold; color: var(--red); padding: 8px 10px; border: 1px solid var(--borderColor); display: inline-flex; position: absolute; top: 7px; right: 0;
		@media (max-width: @t){position: relative; top: 0; right: 0; margin-top: 5px;}
		@media (max-width: @tp){position: absolute; top: 0; right: 0; margin-top: 0px;}
		@media (max-width: @m){top: 6px;}
	}
	.cp-extra-price-lowest{padding-top: 4px;}
	.cp-groupp-third-bottom{
		display: flex; position: relative; margin-top: auto;
		@media (max-width: @m){margin-top: 9px;}
	}
	.cp-btn-addtocart{
		:deep(b){
			display: none;
			@media (max-width: @m){display: initial;}
		}
	}
	.btn-cp-add-detail{padding: 0 5px;}
</style>
