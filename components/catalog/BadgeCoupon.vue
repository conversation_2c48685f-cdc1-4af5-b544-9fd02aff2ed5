<template>
	<template v-for="(coupon, id) in coupons" :key="id">
		<div v-if="coupon.image || (coupon.description && coupon.color_text && coupon.color_bg)" class="badge-coupon-cnt" :class="[mode]">
			<div class="cp-badge cp-badge-coupon-image" v-if="coupon.image">
				<BaseUiImage loading="lazy" :src="'/upload/'+coupon.image" default="/images/no-image-50.jpg" :alt="coupon.description" />
			</div>
			<template v-if="mode != 'details'">
				<div v-if="coupon.description && coupon.color_text && coupon.color_bg" class="cd-badge-coupon-text" :style="{'background-color': coupon.color_bg, 'color': coupon.color_text}">{{coupon.description}}</div>
				<div v-else-if="coupon.description" class="cd-badge-coupon-text">{{coupon.description}}</div>
			</template>
		</div>
	</template>
</template>

<script setup>
	const props = defineProps(['coupons', 'mode']);
</script>

<style lang="less" scoped>
	.badge-coupon-cnt{
		position: absolute; bottom: 8px; left: 20px; display: flex; flex-flow: column; z-index: 1;
		@media (max-width: @t){bottom: 5px; left: 15px;}
		@media (max-width: @tp){left: 10px;}
		@media (max-width: @m){left: 0; bottom: 15px;}
	}
	.cp-badge-coupon-image{
		display: flex; align-items: center; justify-content: center; width: 70px; height: 70px; aspect-ratio: 1; bottom: unset; background: unset; left: 0; position: relative;
		:deep(img){opacity: 1;}
		@media (max-width: @t){width: 45px; height: 45px;}
	}
	.cd-badge-coupon-text{
		background-color: var(--yellow); color: var(--textColor); font-size: 13px; line-height: 16px; font-weight: bold; height: 24px; display: flex; align-items: center; padding: 0 8px 0 31px; white-space: nowrap; margin-top: 8px; position: relative;
		&:before{.icon-discount(); font: 17px/1 var(--fonti); position: absolute; left: 8px; color: var(--textColor); top: 3px;}
		@media (max-width: @t){
			font-size: 12px; line-height: 14px; height: 22px; margin-top: 4px;
			&:before{font-size: 16px; top: 2px;}
		}
		@media (max-width: @m){
			height: 18px; padding: 0 4px 0 21px;
			&:before{font-size: 13px; top: 2px; left: 4px;}
		}
	}


	.cp-badge.new + .badge-coupon-cnt{
		bottom: 65px; left: 15px;
		@media (max-width: @t){bottom: 55px;}
		@media (max-width: @tp){bottom: 50px;}
		@media (max-width: @m){left: 0px; bottom: 45px;}
	}
	.cp-not-available{
		.cp-badge-coupon-cnt{display: none;}
	}

	//DETAILS
	.badge-coupon-cnt.details{
		position: initial; bottom: unset; left: unset;
		.cp-badge-coupon-image{left: -5px; margin-bottom: 5px;}
		@media (max-width: @t){
			.cp-badge-coupon-image{width: 55px; height: 55px;}
		}
	}


	//LIST
	.badge-coupon-cnt.listentry{
		left: 10px; bottom: 15px;
		@media (max-width: @t){left: 10px;}
	}
	.cp-badge.new + .badge-coupon-cnt.listentry{
		bottom: 80px;
		@media (max-width: @t){bottom: 65px;}
		@media (max-width: @tp){bottom: 60px;}
	}
</style>
