<template>
    <BaseUiSwiper class="cw-slider speciallist-items" :options="sliderOptions">
        <BaseUiSwiperSlide v-for="item in items" :key="item.id">
            <CatalogIndexEntry :item="item" mode="slider" />
        </BaseUiSwiperSlide>
    </BaseUiSwiper>
</template>

<script setup>
	const props = defineProps(['items', 'mode']);

	const sliderOptions = {
		watchSlidesProgress: false,
		spaceBetween: -1,
		enabled: false,
        breakpoints: {
			750: {
				enabled: true,
                slidesPerView: props.mode == 'cdRelated' ? 2 : 4,
		        slidesPerGroup: props.mode == 'cdRelated' ? 2 : 4,
			},
            1400: {
                enabled: true,
                slidesPerView: props.mode == 'cdRelated' ? 2 : 5,
                slidesPerGroup: props.mode == 'cdRelated' ? 2 : 5
            }
		}        
	}   
</script>

<style lang="less" scoped>
	.speciallist-items{position: relative; display: block;}

    :deep(.cp){
        width: 100%; margin: 0;
        @media (max-width: @m){height: 100%;}
    }

    :deep(.swiper-wrapper){
        @media (max-width: @m){
            display: flex; position: relative; width: 100%; box-sizing: border-box; overflow-x: auto;
            &::-webkit-scrollbar {-webkit-appearance: none; height: 0;}
            &::-webkit-scrollbar-thumb {background-color: transparent;}
        }
    }        
    :deep(.swiper-slide){
        display: flex; height: auto;
        &:hover{z-index: 1;}
        @media (max-width: @m){display: block; width: 82%!important; flex-grow: 0; flex-shrink: 0; opacity: 1; visibility: visible;}
    }
    :deep(.swiper-button){       
        position: absolute; left: -90px; top: 40%; z-index: 40; width: 60px; height: 60px; min-width: 0; background: #fff; box-shadow: inset 0 0 0 5px var(--yellow); font-size: 0; padding: 0; border-radius: 100%; cursor: pointer; .transition(background-color);
        @media (max-width: 1400px){width: 50px; height: 50px; top: 25%;}
        @media (max-width: @t){box-shadow: inset 0 0 0 3px var(--yellow); width: 40px; height: 40px;}
        @media (max-width: @tp){top: 14%;}
        &:before{
            .icon-arrow-right; font: 16px/60px var(--fonti); position: absolute; left: 0; top: 0; width: 100%; text-align: center; color: var(--textColor);
            @media (max-width: 1400px){line-height: 50px;}
            @media (max-width: @t){line-height: 40px; font-size: 12px;}
        }
        @media (min-width: @h){
            &:hover{background: var(--yellow);}
        }
        &.swiper-button-disabled{
            cursor: default; background: #fff; box-shadow: inset 0 0 0 5px rgba(252,208,2,0.2);
            @media (max-width: @t){box-shadow: inset 0 0 0 3px rgba(252,208,2,0.2);}
            &:before{color: darken(#E0E2DB,10%)!important;}
        }
        &.swiper-button-lock{display: none;}
    }
    :deep(.swiper-button-prev){
        @media (max-width: 1400px){left: -25px;}
        @media (max-width: @t){left: -20px;}
        @media (max-width: @tp){left: -15px;}
        &:before{transform: rotate(180deg);}
    }
    :deep(.swiper-button-next){
        left: auto; right: -90px;
        @media (max-width: 1400px){right: -25px;}
        @media (max-width: @t){right: -20px;}
        @media (max-width: @tp){right: -15px;}
    }
</style>