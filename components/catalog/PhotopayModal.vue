<template>
	<BaseThemeUiModal  name="photopayModal" v-slot="{active}">
		<div v-if="active">
			<div class="cd-photopay-cnt">
				<BaseCmsLabel code="photo_pay" class="cd-photopay-title" tag="span" />
				<div class="cd-photopay-desc">
					<BaseCmsLabel code="photo_pay_description" tag="span" />
				</div>
			</div>
			<div class="cd-photopay-shipping-payment-cnt">
				Photopay
			</div>
		</div>
	</BaseThemeUiModal >
</template>

<script setup>

</script>

<style lang="less" scoped>
	:deep(.base-modal-cnt){
		width: 530px; border-radius: unset; box-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.1);
		@media (max-width: @m){width: 100vw; max-width: 100vw; height: 100vh; box-shadow: unset;}
	}
	:deep(.base-modal-cnt-close){
		top: -12px; right: -13px; box-shadow: 0 5px 40px 0 rgba(0, 0, 0, 0.15); z-index: 1;
		&:before{font-size: 15px; line-height: 15px; color: var(--red); font-weight: bold;}
		svg{display: none;}
		@media (max-width: @m){top: 16px; right: 10px;}
	}

	.cd-photopay-cnt{
		display: block; width: 100%; color: var(--textColor); padding: 24px 30px 24px 94px; background: var(--yellow); position: relative; cursor: pointer;
		&:before{.icon-barcode(); font: 22px/1 var(--fonti); color: var(--textColor); position: absolute; left: 50px; top: 50%; transform: translateY(-50%); z-index: 1;}
		@media (max-width: @m){
			padding: 18px 55px 19px 57px;
			&:before{left: 16px;}
		}
	}
	.cd-photopay-title{
		display: block; font-size: 22px; line-height: 26px; font-weight: bold; text-transform: uppercase; z-index: 2; position: relative;
		@media (max-width: @m){font-size: 16px; line-height: 19px;}
	}
	.cd-photopay-desc{
		display: block; font-size: 16px; line-height: 20px; padding-top: 0; position: relative; z-index: 2;
		@media (max-width: @m){font-size: 12px; line-height: 14px; padding-top: 1px;}
	}
</style>