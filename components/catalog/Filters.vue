<template>
	<div class="cf" :class="['cf-' + contentType]" v-if="searchFields.length">
		<div class="cf-sidebar-title">
            <BaseCmsLabel code="filter_title" />
            <div class="btn-close-filter" @click="$emit('closeFilters');">Zatvori</div>
        </div>	
        <div class="cf-items-wrapper">
            <BaseCatalogFilterItem v-for="filter in searchFields" :key="filter.id" :item="filter" v-slot="{fields, onFilter, active, onToggle}">
                <div class="cf-item" :class="['cf-item-' + filter.code, filter.layout ? 'cf-layout-' + filter.layout : '', {'active': active}]" v-if="filter.label && filter.options && filter.options_total_available">
                    <div class="cf-title" @click="onToggle">{{filter.label}} <span class="nav-sidebar-arrow"></span></div>
                    <div class="cf-item-wrapper">
                        
                        <div class="cf-row" :class="[field.level && 'cf-row-level' + field.level, {'not-available': field.total_available < 1 && !field.selected}]" v-for="field in fields" :key="field.id">
                            <input type="checkbox" :name="filter.filter_url" :id="field.unique_code" :value="field.filter_url" :checked="field.selected" @click="onFilter" />
                            <label :for="field.unique_code">
                                <span>{{field.title}}</span>
                                <span class="cf-counter"> ({{field.total_available}})</span>
                                <template v-if="field.description">
                                    <span class="cf-row-detail"></span>
                                    <div class="cf-tooltip">
                                        <span class="btn-close-tooltip btn-close-cf-tooltip"></span>
                                        <BaseUiImage v-if="field.image_upload_path" :src="field.image_upload_path" loading="lazy" width="240" height="170" default="/images/no-image-100.jpg" :alt="field.title" />
                                        <div class="cf-tooltip-title">{{field.title}}</div>
                                        <div class="cf-tooltip-desc">{{field.description}}</div>
                                        <div class="cf-tooltip-footer"><BaseCmsLabel code="filter_tooltip_footer" default="" /></div>
                                    </div>
                                </template>
                            </label>
                        </div>
                    </div>
                </div>
            </BaseCatalogFilterItem>
        </div>

		<BaseCatalogActiveFilters v-slot="{items, onRemove}">
            <div :class="['cf-btns', items?.length && 'has-active-filters']">
				<div v-if="items?.length" class="btn btn-border btn-cf-m-active-clear" @click="onRemove(), $emit('closeFilters');" v-html="labels.get('clear_filtering')"></div>
				<div class="btn btn-confirm-filters" :class="{'active': items?.length}" @click="$emit('closeFilters');" v-html="labels.get('confirm_filters')"></div>
			</div>
		</BaseCatalogActiveFilters>
	</div>
</template>

<script setup>
	const props = defineProps(['totalProducts', 'searchFields', 'contentType']);
	const emit = defineEmits(['closeFilters']);
	const labels = useLabels();
</script>
