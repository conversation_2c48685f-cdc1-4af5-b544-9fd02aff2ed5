<template>
	<ClientOnly>
		<template v-if="mode && mode == 'detail'">
			<BaseCatalogSetCompare :item="item" v-slot="{onToggleCompare, active, message, loading, compareUrl}">
				<div class="cp-list-compare cd-compare">
					<div class="cp-compare-container">
						<div class="cp-list-btn-compare cd-btn-compare" :class="{'loading': loading, 'compare_active': active && !loading}" @click="onToggleCompare">
							<span class="loader" v-show="loading" />
							<template v-if="mode != 'compare'">
								<span v-if="active"><BaseCmsLabel code="remove_compare_product" class="l2" tag="small" /></span>
							</template>
							<span v-if="!active"><BaseCmsLabel code="add_compare_product" class="l1" tag="small" /></span>
						</div>
						<span :class="['cp-compare-info', 'compare_message', message]" v-show="message">
							<span v-if="message == 'compare_error_limit'" v-html="labels.get('compare_error_limit').replace('%view_compare_url%', compareUrl)" />
							<template v-else>
								<span v-if="active" v-html="labels.get('compare_ok_added').replace('%view_compare_url%', compareUrl)" />
								<span v-if="!active" v-html="labels.get('compare_ok_removed').replace('%view_compare_url%', compareUrl)" />
							</template>
						</span>
					</div>
				</div>
			</BaseCatalogSetCompare>
		</template>
		<template v-else>
			<div class="cp-compare-container">
				<BaseCatalogSetCompare :item="item" v-slot="{onToggleCompare, active, message, loading, compareUrl}">
					<div :class="[{'loading': loading, 'compare_active': active && !loading}, mode ? mode : 'cp-btn-compare']" @click="onToggleCompare">
						<span class="loader" v-show="loading" />
						<template v-if="mode != 'compare'">
							<span v-if="active"><BaseCmsLabel code="remove_compare_product" class="l2" tag="small" /></span>
						</template>
						<span v-if="!active"><BaseCmsLabel code="add_compare_product" class="l1" tag="small" /></span>
					</div>
					<span :class="['cp-compare-info', 'compare_message', message]" v-show="message">
						<span v-if="message == 'compare_error_limit'" v-html="labels.get('compare_error_limit').replace('%view_compare_url%', compareUrl)" />
						<template v-else>
							<span v-if="active" v-html="labels.get('compare_ok_added').replace('%view_compare_url%', compareUrl)" />
							<span v-if="!active" v-html="labels.get('compare_ok_removed').replace('%view_compare_url%', compareUrl)" />
						</template>
					</span>
				</BaseCatalogSetCompare>
			</div>
		</template>
	</ClientOnly>
</template>

<script setup>
	const labels = useLabels();
	const props = defineProps(['item', 'mode']);
</script>

<style lang="less" scoped>
</style>
