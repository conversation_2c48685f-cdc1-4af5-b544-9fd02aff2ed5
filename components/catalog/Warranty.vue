<template>    
    <div :class="mode == 'detail' ? 'cd-services' : 'modal-services'">
        <BaseCmsLabel v-if="mode == 'detail'" class="cd-services-title" code="additional_services" tag="div"/>
        <BaseCmsLabel v-else-if="addToCartStatus?.data?.label_name == 'success_added'" class="modal-title" code="modal_warranty_success" tag="div"/>
        <BaseCmsLabel v-else class="modal-title" code="need_extra_warranty" default="Trebate li dodatno jamstvo za proizvod?" tag="div" />
        <div v-if="addToCartStatus?.data?.label_name != 'success_added'" class="cd-services-items">
            <p v-for="warrantyProduct in warrantyProducts" :key="warrantyProduct.id">
                <input type="radio" :name="(mode == 'detail' ? 'detail-' : 'modal-')+'select-product'" :id="(mode == 'detail' ? 'detail-' : 'modal-')+'service-'+warrantyProduct.id" @change="onWarrantyChange" v-model="selectedWarranty" :value="warrantyProduct.shopping_cart_code" :disabled="!warrantyProduct.is_available ? true : false">
                <label :for="(mode == 'detail' ? 'detail-' : 'modal-')+'service-'+warrantyProduct.id">
                    {{warrantyProduct.title}}
                    <template v-if="!warrantyProduct.is_available"> - <strong><BaseCmsLabel code="unavailable" default="nije dostupno" /></strong></template>
                    - 
                    <span class="cd-services-price"><BaseUtilsFormatCurrency :price="warrantyProduct.price_custom" /></span>
                </label>
            </p>
            <p><input type="radio" :name="(mode == 'detail' ? 'detail-' : 'modal-')+'select-product'" checked :id="(mode == 'detail' ? 'detail-' : 'modal-')+'service-none'" @change="onWarrantyChange" v-model="selectedWarranty" value=""><label :for="(mode == 'detail' ? 'detail-' : 'modal-')+'service-none'"><BaseCmsLabel code="additional_services_reject" /></label></p>
        </div>
        <div v-if="selectedWarranty && mode != 'detail' && addToCartStatus?.data?.label_name != 'success_added'">
            <BaseWebshopAddToCart v-slot="{onAddToCart, loading}" :data="{shopping_cart_code: selectedWarranty}" @addedToCart="addToCartStatus = $event.status" :add-to-cart-modal="false">
                <button type='button' :class="['btn modal-warranty-add', loading && 'loading']" @click="onAddToCart"><UiLoader v-if="loading" /><BaseCmsLabel code="modal_add_warranty" /></button>
            </BaseWebshopAddToCart>
        </div>
    </div>    
</template>

<script setup>
    const props = defineProps(['mode', 'warrantyProducts']);
    const emit = defineEmits(['warrantyChanged']);
    const selectedWarranty = ref('');
    const addToCartStatus = ref(null)

    function onWarrantyChange() {
        emit('warrantyChanged', selectedWarranty.value);
    }    
</script>