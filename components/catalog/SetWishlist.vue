<template>
	<ClientOnly>
		<template v-if="mode && mode == 'detail'">
			<BaseCatalogSetWishlist v-slot="{active, onToggleWishlist, loading, message}" :item="item">
				<div class="cp-list-wishlist cd-wishlist" :class="{'active': active, 'wishlist-detail-buttons': mode == 'wishlist'}">
					<div class="cp-list-wishlist-btn" 
						:class="[{
							'wishlist-detail-btn': mode == 'wishlist',
							'wishlist-detail-btn-remove': mode == 'wishlist' && active,
							'cd-wishlist-add': !active,
							'cp-wishlist-remove': active,
							'loading': loading
						}]" 
						@click="onToggleWishlist"
					>
						<BaseCmsLabel v-if="!active" code="add_to_wishlist" tag="span" />
						<BaseCmsLabel v-if="active" code="remove_from_wishlist" tag="span" />
						<BaseCmsLabel class="product-in-wishlist wishlist_message wishlist-message wishlist-message-title" v-show="message" :code="message" tag="span" />
					</div>
				</div>
			</BaseCatalogSetWishlist>
		</template>
		<template v-else>
			<BaseCatalogSetWishlist v-slot="{active, onToggleWishlist, loading, message}" :item="item">
				<div :class="[{'active': active || mode == 'wishlist'}, mode == 'list' ? 'cp-list-wishlist cp-list-wishlist-list' : 'cp-wishlist', mode == 'small' && 'cp-small-wishlist']" >
					<div  
						:class="[{
							'cp-wishlist-add': !active,
							'cp-wishlist-remove': active || mode == 'wishlist',
							'loading': loading
						}, mode == 'list' ? 'cp-list-wishlist-btn' : 'cp-wishlist-btn']" 
						@click="onToggleWishlist"
					>
						<BaseCmsLabel v-if="!active" code="add_to_wishlist" tag="span" />
						<BaseCmsLabel v-if="active" code="remove_from_wishlist" tag="span" />
						<BaseCmsLabel class="product-in-wishlist wishlist_message wishlist-message wishlist-message-title" v-show="message" :code="message" tag="span" />
					</div>
				</div>
			</BaseCatalogSetWishlist>
		</template>
	</ClientOnly>
</template>

<script setup>
	const props = defineProps(['item','mode']);
</script>

<style lang="less" scoped>
</style>
