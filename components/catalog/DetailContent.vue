<template>
	<template v-if="item.content || item.documents?.length || item.attributes?.length || item.element_attributes?.length || (item?.feedback_comment_widget && item.feedback_comment_widget.comments_status > 1)">
		<div class="tabs-container" id="c-tabs">
			<ul class="tabs">
				<!-- 
					FIXME samo info ako bude trebalo za loadbee
					ovdje nema nicega http://localhost:3000/apple-iphone-16-pro-max-8gb-256gb-natural-titanium-bez-proizvod-40741/
					ovdje ima loadbee http://localhost:3000/pecnica-miele-h-2851-b-edst-proizvod-44508/ (vecina miele proizvoda ima)
				-->
				<li v-if="item.content || item.element_attributes || hasLoadbee" class="tab-nav-desc" :class="{active: activeTabs[1]}">
					<span @click="handleTab(1)"><BaseCmsLabel code="product_description" /></span>
				</li>
				<li v-if="item.attributes?.length" class="tab-nav-specs" :class="{active: activeTabs[2]}"><span @click="handleTab(2)"><BaseCmsLabel code="product_specs" /></span></li>								
				<li v-if="item?.feedback_comment_widget?.comments_status > 1" class="tab-nav-comments" :class="{active: activeTabs[3]}">
					<span @click="handleTab(3)"><BaseCmsLabel code="comments" /> <span v-if="item?.feedback_comment_widget?.comments > 0" class="yellow tab-counter">({{item?.feedback_comment_widget?.comments}})</span></span>
				</li>	
				<li v-if="item.documents?.length" class="tab-nav-download" :class="{active: activeTabs[4]}"><span @click="handleTab(4)"><BaseCmsLabel code="product_download" /></span></li>
			</ul>	
			<div class="tabs-content">
				<div class="tab tab-desc" :class="{active: activeTabs[1]}">
					<span v-if="item.content || item.element_attributes || hasLoadbee" class="btn-tab-toggle" @click="handleTab(1)"><span class="toggle-icon"></span><BaseCmsLabel code="product_description" /></span>
					<div class="tab-content">
						<div class="clear cd-desc">
							<div v-html="item.content"></div>
							<div v-html="item.element_attributes"></div>
						</div>
				
						<div class="loadbeeTabContent">
							<div class="loadbeeTab" 
								data-loadbee-manufacturer="EAN"
								:data-loadbee-product="item.code"
								data-loadbee-language="hr_HR"
								data-loadbee-css="default"
								data-loadbee-button="default"
								data-loadbee-template="default">
							</div>
						</div>									
					</div>
				</div>	
				<div v-if="item?.attributes?.length" class="tab tab-specs" :class="{active: activeTabs[2]}">
					<span class="btn-tab-toggle" @click="handleTab(2)"><span class="toggle-icon"></span><BaseCmsLabel code="product_specs" /></span>
					<div class="tab-content">
						<div class="cd-attributes">
							<template v-for="(attributeItems, category) in attributesGrouped" :key="category">
								<div v-for="([title, values], index) in Object.entries(attributeItems)" :key="title" class="cd-attribute">
									<div :class="['cd-attribute-group', index === 0 ? '' : 'empty']">{{ index === 0 ? category : '' }}</div>
									<div class="cd-attribute-title">{{ title }}</div>
									<div class="cd-attribute-value">{{ values.join(', ') }}</div>
								</div>
							</template>
							<div v-if="item.warranty > 0" class="cd-attribute">
								<div class="cd-attribute-group empty"></div>
								<div class="cd-attribute-title"><BaseCmsLabel code="warranty" /></div>
								<div class="cd-attribute-value">{{Number(item.warranty)}} mj.</div>
							</div>						
						</div>
					</div>
				</div>	

				<div v-if="item?.feedback_comment_widget?.comments_status > 1" class="tab tab-comments" :class="{active: activeTabs[3]}">
					<span class="btn-tab-toggle" @click="handleTab(3)"><span class="toggle-icon"></span><BaseCmsLabel code="comments" /></span>
					<div class="tab-content">
						<ClientOnly>
							<FeedbackCommentsForm :item="item" />
						</ClientOnly>
					</div>
				</div>

				<div v-if="item.documents?.length" class="tab tab-specs" :class="{active: activeTabs[4]}">					
					<span class="btn-tab-toggle" @click="handleTab(4)"><span class="toggle-icon"></span><BaseCmsLabel code="product_download" /></span>
					<div class="tab-content">
						<table class="cd-documents">
							<thead>
								<tr>
									<td class="cd-documents-title"><BaseCmsLabel code="file_description" default="Opis datoteke" /></td>
									<td class="cd-documents-format"><BaseCmsLabel code="file_format" default="Format" /></td>
									<td class="cd-documents-dl"><BaseCmsLabel code="file_download" default="Preuzimanje" /></td>
								</tr>
							</thead>
							<tbody>
								<tr v-for="document in item.documents" :key="document.id">
									<td class="cd-documents-title">{{document.title}}</td>
									<td class="cd-documents-format"><BaseCmsLabel code="file_format" class="table-row-title" tag="span" />: {{getFileExtension(document.url)}}</td>
									<td class="cd-documents-dl"><a :href="document.file?.startsWith('http') ? document.file : document.url" target="_blank" class="btn-documents-dld" :title="document.title"><BaseCmsLabel code="download_file" default="Preuzmi datoteku" tag="span" /></a></td>												
								</tr>
							</tbody>
						</table>
					</div>
				</div>			
			</div>
		</div>
	</template>
</template>

<script setup>
	const props = defineProps(['item']);
	const {onMediaQuery} = useDom()
	const hasLoadbee = ref(false);

	const checkInitialActiveTab = () => {
		const hasContent = props.item.content || props.item.element_attributes || hasLoadbee.value;
		if (hasContent) return { '1': true };
		if (props.item.attributes?.length) return { '2': true };
		if (props.item?.feedback_comment_widget?.comments_status > 1) return { '3': true };
		return { '4': true };
	};

	const activeTabs = ref(checkInitialActiveTab());


	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 990px)',
	});   

	function handleTab(index){
		if(!mobileBreakpoint.value) activeTabs.value = {};
		activeTabs.value[index] = !activeTabs.value[index];
	}

	function getFileExtension(url) {
		const match = url.match(/\.([a-zA-Z0-9]+)(?=($|\?|#))/);
		return match ? match[1].toLowerCase() : null;
	}

	function groupAttributes(attributes) {
		const result = {}

		for (const attr of attributes) {
			if (['posebne_oznake', 'poklon'].includes(attr.attribute_code)) continue

			if (!result[attr.category_title]) {
			result[attr.category_title] = {}
			}

			if (!result[attr.category_title][attr.attribute_title]) {
			result[attr.category_title][attr.attribute_title] = []
			}

			result[attr.category_title][attr.attribute_title].push(attr.title)
		}

		return result
	}

	const attributesGrouped = computed(() => {
		const final = {}

		for (const attr of props.item.attributes) {
			if(attr.attribute_code === 'posebne_oznake' || attr.attribute_code === 'poklon') continue;

			const cat = attr.category_title
			const title = attr.attribute_title

			if (!final[cat]) final[cat] = {}
			if (!final[cat][title]) final[cat][title] = []

			final[cat][title].push(attr.title)
		}

		return final
	})			
	
	onMounted(() => {
		window.loadbeeApiKey= '5GKgxrTEtEkX2592UAbuHDMeReCFPR7J';

		window.loadbeeProductFoundCallback = () => {
			hasLoadbee.value = true;
			activeTabs.value = checkInitialActiveTab();
		}

		if (typeof window.loadbeeService === 'undefined') {
			const script = document.createElement('script')
			script.src = '//button.loadbee.com/js/v2/loadbee.js'
			script.async = true
			document.body.appendChild(script)
		}
		else if(window.loadbeeService?.requestAll){
			window.loadbeeService.requestAll()
		}		
	})

</script>