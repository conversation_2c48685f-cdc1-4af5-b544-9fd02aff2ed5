<template>
    <div class="hp-categories-cnt">
        <div class="hp-categories-title" v-html="labels.get('hp_categories_offer')"></div>
        <BaseCatalogCategoriesWidget :fetch="{level_from: 1, level_to: 1, special: true, response_fields: ['id','link','url_without_domain','title']}" v-slot="{items}">
            <span v-if="items.length" class="nc-m-cnt">
                <ul class="hp-categories">
                    <li v-for="item in items" :key="item.id" :class="['nav-category-'+ item.code]">
                        <NuxtLink :to="item.url_without_domain">{{item.title}}</NuxtLink>
                    </li>
                </ul>
            </span>
        </BaseCatalogCategoriesWidget>
    </div>
</template>

<script setup>
	const labels = useLabels();
</script>