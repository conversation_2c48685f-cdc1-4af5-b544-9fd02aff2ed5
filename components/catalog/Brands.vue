<template>
	<BaseCatalogManufacturers :fetch="{special: 1, limit: limitBrands}" v-slot="{items}">
		<div class="mw">
			<div class="mwp" v-for="manufacturer in items" :key="manufacturer.id">
				<NuxtLink :to="manufacturer.url_without_domain">
					<BaseUiImage :data="manufacturer.main_image_thumbs?.['width100-height30']" default="/images/no-image-50.jpg" :alt="manufacturer.title" loading="lazy" />
				</NuxtLink>
			</div>
		</div>
	</BaseCatalogManufacturers>
</template>

<script setup>
	const props = defineProps(['mode']);
	const limitBrands = props.mode == 'hp_brands' ? 18 : 10;
</script>