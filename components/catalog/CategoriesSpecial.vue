<template>
	<BaseCatalogCategoriesWidget :fetch="{level_to: category?.level ? (category?.level > 1 ? 3 : 2) : 1, mode: 'widget', start_position: category?.level > 2 ? category.parent_position_h : category.position_h, response_fields: ['id', 'code', 'title', 'url_without_domain']}" v-slot="{items}">
		<div class="c-categories-m-wrapper">
			<div v-if="items?.length" class="c-categories-m">
				<div v-for="item in items" :key="item.id" class="c-category-m">
					<NuxtLink :to="item.url_without_domain" :class="{'active-item': item.url_without_domain === category.url_without_domain}">
						{{ item.title }}
					</NuxtLink>
				</div>
			</div>
		</div>
		</BaseCatalogCategoriesWidget>
</template>

<script setup>
	const props = defineProps(['category']);
</script>

<style lang="less" scoped>
	.c-categories-m-wrapper{
		position: relative; display: none; margin-top: 16px;
		@media (max-width: @m){display: block;}
	}
	.c-categories-m{
		display: flex; align-items: unset; justify-content: unset; list-style: none; width: calc(~"100% - -30px"); margin-left: -15px; overflow-y: hidden; white-space: normal; -webkit-overflow-scrolling: touch; position: relative;
		&::-webkit-scrollbar{display: none;}
		&::-webkit-scrollbar-thumb{display: none;}
	}
	.c-category-m{
		display: block; margin-right: 8px; position: relative;
		&:first-child{padding-left: 15px;}
		&:last-child{margin-right: 0; padding-right: 15px;}
		a{display: flex; align-items: center; height: 40px; border: 1px solid var(--borderColor); font-size: 14px; line-height: 1.2; white-space: nowrap; text-decoration: none; position: relative; padding: 0 12px;}
		.router-link-active{border-color: var(--textColor);}
	}
</style>