<template>
    <article class="cp-small" :class="{'cp-not-available': !item.is_available}">
        <div class="cp-small-col cp-small-col1">
            <CatalogListBadges :listInfo="item.lists_info" :status="item.status" mode="small" />

            <figure class="cp-small-image">
				<NuxtLink :to="item.url_without_domain">
					<span><BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width144-height144']" default="/images/no-image-144.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : ''" /></span>
				</NuxtLink>
			</figure>
        </div>
        <div class="cp-small-col cp-small-col2">
            <div class="cp-small-header">
                <div v-if="item.manufacturer_main_image" class="cp-small-brand" :class="[item.manufacturer_code && 'cp-brand-'+item.manufacturer_code]">
                    <BaseUiImage :data="item.manufacturer_main_image_thumbs?.['width95-height30']" default="/images/no-image-50.jpg" loading="lazy" :alt="item.manufacturer_title" />
                </div>     

                <div class="cp-small-title"><NuxtLink :to="item.url_without_domain">{{item.title}}</NuxtLink></div>       
                <div class="cp-small-code">{{item.code}}</div>   
                
                <ul v-if="item.attributes_special?.length" class="cp-attrs cp-small-attrs">
                    <li class="cp-attr" v-for="attr in item.attributes_special.filter(attr => !['posebne_oznake', 'poklon'].includes(attr.attribute_code)).slice(0, 4)" :key="attr.id">{{attr.title}}</li>
                </ul>
            </div>

            <div v-if="item.price_custom > 0" class="cp-small-col2-bottom">
                <div class="cp-price cp-small-price">
                    <div class="cp-price-discount">
                        <div class="cp-prices-small">
                            <div v-if="item.discount_percent_base > 0 || item.price_custom < item.basic_price_base" class="cp-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_base" /></div>
                            <div class="cp-current-price" :class="{'red': item.discount_percent_base > 0 || item.price_custom < item.basic_price_base}">
                                <span data-currency_format="full_price_currency"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></span>
                            </div>
                        </div>

                        <CatalogEnergyBadge :item="item" modeClass="cp-small-attr-energy" />
                        
                        <span v-if="item.extra_price_lowest && item.extra_price_lowest > 0" class="cp-extra-price-lowest">
                            <BaseCmsLabel code="extra_price_lowest" />:
                            <strong>
                                <BaseUtilsFormatCurrency :price="item.extra_price_lowest" />
                            </strong>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="cp-small-col cp-small-col3">
            <CatalogSetWishlist v-if="item.wishlist_widget" :item="item" mode="small" />

            <CatalogSetCompare v-if="item.compare_widget" :item="item" />

            <BaseWebshopAddToCart v-if="item.is_available && item.available_qty > 0" v-slot="{onAddToCart, loading}" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code}">
                <button type='button' :class="['btn btn-cp-add-detail cp-small-add cp-btn-addtocart', loading && 'loading']" @click="onAddToCart"><UiLoader v-if="loading" /><span v-if="!loading" v-html="labels.get('add_to_shopping_cart')"></span></button>
            </BaseWebshopAddToCart>
        </div>
    </article>
</template>

<script setup>
    const props = defineProps(['item']);
    const labels = useLabels();

    const installmentsMaxPrice = computed(() => {
		if(props.item?.installments_info?.max_price){
			return props.item?.installments_info?.max_price;
		}
		return props.item?.basic_price_base    
    });  

    const cardPrice = computed(() => {
		if(props.item.installments_info?.items?.length){
			return props.item.installments_info.items[props.item.installments_info.items.length - 1]
		}
		return null    
    });         
</script>