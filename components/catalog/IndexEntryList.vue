<template>
	<article class="cp-list" :class="{'cp-not-available': !item.is_available}">
		<div class="cp-list-col cp-list-col1">
			<CatalogListBadges :listInfo="item.lists_info" :status="item.status" />

			<div v-if="item.manufacturer_main_image" class="cp-list-brand" :class="['cp-brand', item.manufacturer_code && 'cp-brand-'+item.manufacturer_code]">
				<BaseUiImage :data="item.manufacturer_main_image_thumbs?.['width95-height30']" default="/images/no-image-50.jpg" loading="lazy" :alt="item.manufacturer_title" />
			</div>     

			<template v-if="(specialBadge = item.attributes_special?.find(attr => attr.attribute_code === 'posebne_oznake'))">
				<div class="cp-list-badge-special">{{specialBadge.title}}</div>
			</template>

			<template v-if="(gift = item.attributes_special?.find(attr => attr.attribute_code === 'poklon'))">
				<div :title="gift.title" class="cp-badge cp-badge-gift cp-list-badge-gift"></div>
			</template>

			<template v-if="Object.keys(item?.badge_coupons || {}).length">
                <CatalogBadgeCoupon :coupons="item?.badge_coupons" mode="listentry" />
            </template>
			<span v-else-if="item.discount_percent == 0 && item?.priority_details?.code == 'new'" class="cp-badge cp-list-badge cp-list-badge-list new">{{item?.priority_details.title}}</span>			        

			
			<figure class="cp-list-image">
				<NuxtLink :to="item.url_without_domain">
					<span><BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width265-height265']" default="/images/no-image-265.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : ''" /></span>
				</NuxtLink>
			</figure>         
		</div>
		<div class="cp-list-col cp-list-col2">
			<div v-if="!item.is_available">
				<span class="cp-list-unavailable"><span><BaseCmsLabel code="not_available_2" /></span></span>
			</div>  

			<div class="cp-list-code">{{item.code}}</div>   
			
			<h2 class="cp-list-title">
			<NuxtLink :to="item.url_without_domain">{{item.title}}</NuxtLink>
			</h2>       

			<div class="cp-list-rating">
				<ClientOnly>
					<template v-if="item.feedback_rate_widget?.rates_status > 1 && item.feedback_rate_widget?.rates_votes > 0">
						<FeedbackRates :rates="item.feedback_rate_widget.rates" />
					</template>

				</ClientOnly>   

				<NuxtLink v-if="item.feedback_comment_widget?.comments_status > 1 && item.feedback_comment_widget?.comments > 0" :to="item.url_without_domain" class="cp-list-comments"><span class="label"><BaseCmsLabel code="comments_num" /></span> ({{item.feedback_comment_widget.comments}})</NuxtLink>
			</div>

			<ul v-if="item.attributes_special?.length" class="cp-list-attrs">
				<li class="cp-list-attr" v-for="attr in item.attributes_special.filter(attr => !['posebne_oznake', 'poklon'].includes(attr.attribute_code)).slice(0, 3)" :key="attr.id">
					<NuxtLink v-if="attr.attribute_code == 'energetski-razred'" :to="item.url_without_domain+'?mode=energy'" class="btn-attr-energy">
						<div class="cp-list-attr-image">
							<BaseUiImage :src="attr.image" default="/images/no-image-50.jpg" loading="lazy" width="60" height="60" />
						</div>
						<div v-if="attr.attribute_title" class="cp-list-attr-title">{{attr.attribute_title}}</div>
						<div v-if="attr.title" class="cp-list-attr-value">{{attr.title}}</div>
					</NuxtLink>
					<div v-else>
						<div class="cp-list-attr-image">
							<BaseUiImage :src="attr.attribute_image" default="/images/no-image-50.jpg" loading="lazy" width="50" height="50" />
						</div>
						<div v-if="attr.attribute_title" class="cp-list-attr-title">{{attr.attribute_title}}</div>
						<div v-if="attr.title" class="cp-list-attr-value">{{attr.title}}</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="cp-list-col cp-list-col3">
			<div class="cp-list-col3-top">
				<div v-if="item.price_custom > 0" class="cp-price cp-list-price">
					<div class="cp-price-discount">
						<div v-if="item.discount_percent_base > 0 || item.price_custom < item.basic_price_base" class="cp-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_base" /></div>
						<div class="cp-current-price" :class="{'red': item.discount_percent_base > 0 || item.price_custom < item.basic_price_base}">
							<span data-currency_format="full_price_currency"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></span>
						</div>
					</div>

					<span v-if="item.extra_price_lowest && item.extra_price_lowest > 0" class="cp-extra-price-lowest">
						<BaseCmsLabel code="extra_price_lowest" />:
						<strong>
							<BaseUtilsFormatCurrency :price="item.extra_price_lowest" />
						</strong>
					</span>
				</div>

				<CatalogEnergyBadge :item="item" modeClass="cp-list-attr-energy" />
			</div>
			<div class="cp-list-col3-bottom">
				<div v-if="item.compare_widget" class="cp-list-compare cp-list-compare-list">
					<CatalogSetCompare :item="item" mode="cp-list-btn-compare" />
				</div>
				
				<CatalogSetWishlist v-if="item.wishlist_widget" mode="list" :item="item" />
			</div>
		</div>
	</article>
</template>

<script setup>
	const props = defineProps(['item']);
	const labels = useLabels();

	const installmentsMaxPrice = computed(() => {
		if(props.item?.installments_info?.max_price){
			return props.item?.installments_info?.max_price;
		}
		return props.item?.basic_price_base    
	});  

	const cardPrice = computed(() => {
		if(props.item.installments_info?.items?.length){
			return props.item.installments_info.items[props.item.installments_info.items.length - 1]
		}
		return null    
	});    
</script>