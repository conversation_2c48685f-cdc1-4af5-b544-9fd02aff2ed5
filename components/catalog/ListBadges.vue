<template>
    <div v-if="listInfo" :class="['cp-badges', mode == 'small' && 'cp-small-badges']">
        <template v-for="(attr, index) in listInfo" :key="index">
            <NuxtLink v-if="attr.list_main_image" :to="attr.url_without_domain" :class="[mode == 'small' ? 'cp-special-small-badge' : 'cp-list-badge cp-list-badge-list cp-special-list-badge']">
                <BaseUiImage loading="lazy" :data="mode == 'landing' ? attr.image_thumbs?.['width60-height200'] : attr.list_main_image_thumbs?.['width60-height200']" default="/images/no-image-50.jpg" :alt="attr.title ? attr.title : ''" />
            </NuxtLink>
        </template>
        <template v-if="status?.length && status == '1' || status == '2'">
            <div class="cp-list-badge cp-list-badge-list cp-special-list-badge cp-badge-status" :class="'cp-badge-status-' + status">{{status}}</div>
        </template>
    </div> 
</template>

<script setup>
    const props = defineProps(['listInfo', 'mode', 'status']);
</script>

<style lang="less" scoped>
    .cp-badge-status{
        width: 60px; height: 60px!important; font-size: 0;
        @media (max-width: @t){width: 32px!important; height: 32px!important;}
    }
    .cp-badge-status-1{background: url(assets/images/status1.png) no-repeat left top; background-size: contain;}
    .cp-badge-status-2{background: url(assets/images/status2.png) no-repeat left top; background-size: contain;}

    //MENU
    .cp-small-badges{
        .cp-badge-status{width: 30px!important; height: 30px!important;}
    }
</style>