<template>
	<article class="cp" :class="{'cp-not-available': !item.is_available, 'cp-compare': mode == 'compare', 'cp-cart': mode == 'cart', 'cp-landing': mode == 'landing'}">
		<CatalogListBadges :listInfo="item.lists_info" :status="item.status" :mode="mode" />
		<ClientOnly>
			<CatalogCompareSearch v-if="mode == 'compare'" />
		</ClientOnly> 

		<NuxtLink class="cp-header-cnt" :to="item.url_without_domain">
			<div class="cp-image">
				<template v-if="(gift = item.attributes_special?.find(attr => attr.attribute_code === 'poklon'))">
					<div :title="gift.title" class="cp-badge cp-badge-gift"></div>
				</template>

				<template v-if="Object.keys(item?.badge_coupons || {}).length">
					<CatalogBadgeCoupon :coupons="item?.badge_coupons" />
				</template>
				<span v-else-if="item.discount_percent == 0 && item?.priority_details?.code == 'new'" class="cp-badge new">{{item?.priority_details.title}}</span>


				<figure>
					<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width265-height265']" default="/images/no-image-265.jpg" :title="item.main_image_title" :alt="item.main_image_description ? item.main_image_description : ''" />                   
				</figure>
			</div>
		</NuxtLink>
		<div class="clear cp-cnt">
			<NuxtLink :to="item.url_without_domain">
				<ClientOnly>
					<div v-if="item.feedback_rate_widget?.rates_status > 1 && item.feedback_rate_widget?.rates_votes > 0" class="cp-rating-container">
						<FeedbackRates :rates="item.feedback_rate_widget.rates" />
					</div>
				</ClientOnly>                
				<div class="cp-cnt-header">
					<span v-if="!item.is_available" class="cp-unavailable">
						<span class="cp-unavailable-btn"><span><BaseCmsLabel code="not_available_2" /></span></span>
					</span>
					
					<div v-if="item.manufacturer_main_image" class="cp-brand" :class="['cp-brand', item.manufacturer_code && 'cp-brand-'+item.manufacturer_code]">
						<BaseUiImage v-if="mode != 'landing' && mode != 'wishlist'" :data="item.manufacturer_main_image_thumbs?.['width95-height30']" default="/images/no-image-50.jpg" loading="lazy" :alt="item.manufacturer_title" />
						<BaseUiImage v-else :src="item.manufacturer_main_image_upload_path" default="/images/no-image-50.jpg" loading="lazy" :alt="item.manufacturer_title" />
					</div>

					<h2 class="cp-title">{{item.title}}</h2>
					<div class="cp-code">{{item.code}}</div>

					<ul v-if="item.attributes_special?.length" class="cp-attrs">
						<li v-for="attr in item.attributes_special.filter(attr => !['posebne_oznake', 'poklon'].includes(attr.attribute_code)).slice(0, 4)" :key="attr.id">{{attr.title}}</li>
					</ul>
				</div>
			</NuxtLink>

			<div class="cp-price-container">
				<CatalogEnergyBadge :item="item" />

				<div class="cp-price">
					<div class="cp-price-discount">
						<div v-if="item.discount_percent_base > 0 || item.price_custom < item.basic_price_base" class="cp-old-price"><BaseUtilsFormatCurrency :price="item.basic_price_base" /></div>
						<div class="cp-current-price" :class="{'red': item.discount_percent_base > 0 || item.price_custom < item.basic_price_base}">
							<span data-currency_format="full_price_currency"><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></span>
						</div>
					</div>
				</div>
			</div>

			<ClientOnly>
				<span v-if="item.extra_price_lowest && item.extra_price_lowest > 0" class="cp-extra-price-lowest">
					<BaseCmsLabel code="extra_price_lowest" />:
					<strong>
						<BaseUtilsFormatCurrency :price="item.extra_price_lowest" />
					</strong>
				</span>  

				<div class="cp-btns">
					<CatalogSetWishlist v-if="item.wishlist_widget" :item="item" />

					<CatalogSetCompare v-if="item.compare_widget" :item="item" />

					<BaseWebshopAddToCart v-if="item.is_available && item.available_qty > 0" v-slot="{onAddToCart, loading}" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code}">
						<button type='button' :class="['btn btn-cp-add-detail cp-btn-addtocart', loading && 'loading']" @click="onAddToCart"><UiLoader v-if="loading" /><span v-if="!loading" v-html="labels.get('add_to_shopping_cart')"></span></button>
					</BaseWebshopAddToCart>
					<NuxtLink v-else class="btn btn-cp-add-detail cp-btn-details" :to="item.url_without_domain" :title="labels.get('more_info')"><BaseCmsLabel code="more_info" /></NuxtLink>                
				</div>
			</ClientOnly> 
		</div>
	</article>
</template>

<script setup>
	const props = defineProps(['item', 'mode']);
	const labels = useLabels();

	const installmentsMaxPrice = computed(() => {
		if(props.item?.installments_info?.max_price){
			return props.item?.installments_info?.max_price;
		}
		return props.item?.basic_price_base    
	});  

	const cardPrice = computed(() => {
		if(props.item.installments_info?.items?.length){
			return props.item.installments_info.items[props.item.installments_info.items.length - 1]
		}
		return null    
	});      
</script>

<style lang="less" scoped>
	.cp-landing{
		width: 100%; margin: 0; min-height: 600px;
		@media (max-width: 1400px){
			.btn-cp-add-detail{padding: 0 10px;}
			.btn-cp-add-detail.cp-btn-addtocart{
				padding: 0 5px;
				span{
					padding: 0;
					&:before{display: none;}
				}
			}
		}
		@media (max-width: @t){min-height: 557px;}
		@media (max-width: @tp){min-height: 470px;}
		@media (max-width: @m){
			min-height: unset;
			.btn-cp-add-detail.cp-btn-addtocart{
				span{
					padding-left: 25px;
					&:before{display: block;}
				}
			}
		}
	}
</style>