<template>
    <div v-if="badge" :class="['cp-attr-energy', modeClass && modeClass]">
        <span v-if="modeClass == 'cd-attr-energy'" @click="$emit('clickedEnergy');">
            <BaseUiImage :src="badge.image" default="/images/no-image-50.jpg" loading="lazy" width="75" height="40" />
        </span>
        <NuxtLink v-else :to="item.url_without_domain+'?mode=energy'">
            <BaseUiImage :src="badge.image" default="/images/no-image-50.jpg" loading="lazy" width="75" height="40" />
        </NuxtLink>
        <a v-if="document?.url" target="_blank" :href="document.url" class="cp-attr-energy-link">{{document.title}}</a>
    </div>
</template>

<script setup>
    const props = defineProps(['item', 'modeClass']);
    const emit = defineEmits(['clickedEnergy']);
    const badge = computed(() => {
        return props.item.attributes_special?.find(attr => attr.attribute_code === 'energetski-razred');
    });
    const document = computed(() => {
        return props.item.documents?.find(doc => doc.title === 'Informacijski list');
    });
</script>
