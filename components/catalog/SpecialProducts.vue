<template>
	<BaseCatalogLists :fetch="{code: 'best_buy', response_fields: ['code', 'url_without_domain']}" v-slot="{items: list}">
		<BaseCatalogProductsWidget v-if="list?.length" :fetch="{list_code: list[0].code, sort: 'list_position', limit: 15}" v-slot="{items}" @loadProductsWidget="onLoadedProducts">
			<div class="cw">
                <div class="wrapper wrapper-promo">
                    <div class="cw-title" v-html="labels.get('special_products')"></div>
                    <CmsPromo v-if="pageId" :pageId="pageId" />
                </div>	

				<div v-if="items?.length" class="wrapper cw-items-wrapper">			
					<div class="cw-items">
                        
                        <CatalogSpeciallists :items="items" />	

                        <div class="cw-btns">
                            <NuxtLink :to="list[0].url_without_domain" class="btn btn-yellow btn-cw-all"><BaseCmsLabel code="show_all" /></NuxtLink>
                        </div>
					</div>
				</div>
			</div>
		</BaseCatalogProductsWidget>
	</BaseCatalogLists>    
</template>

<script setup>
    const props = defineProps(['pageId']);
    const labels = useLabels();

	function onLoadedProducts(products){
		if(products?.items.length){
			sendProductImpressions(products.items, "Proizvodi");
		}
	}
</script>