<template>    
    <BaseCatalogActiveFilters v-slot="{items, onRemove}">
        <div class="fz0 cf-item cf-active" v-if="items?.length">
            <div class="cf-title cf-active-title"><BaseCmsLabel code="selected_filters" /></div>
            <span v-for="filter in items" :key="filter.id" class="cf-active-item" @click="onRemove(filter)">{{ filter.title }}</span>
            <span class="btn-cf-active-clear" @click="onRemove()"><span v-html="labels.get('clear_filtering')"></span></span>
        </div>
    </BaseCatalogActiveFilters> 
</template>

<script setup>
    const labels = useLabels();
</script>
