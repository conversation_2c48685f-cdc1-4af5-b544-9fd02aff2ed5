<template>
	<BaseCmsPage v-slot="{page}">
	<Body :class="['whitebg']" />
		<ClientOnly>
			<BaseWebshopCart v-slot="{counter, parcels, urls, onFinishShopping}" :remarketing="true">
				<BaseWebshopFreeShipping v-slot="{item: toFree}">
					<div v-if="labels.get('webshop_disabled')" class="webshop-disabled-note">{{labels.get('webshop_disabled')}}</div>
					<template v-else>
						<main class="main" v-if="parcels?.length">
							<div class="wrapper main-wrapper">
								<div v-if="parcels[0]?.items?.length" class="w-cart" id="view_cart">
									<div class="w-col w-col1">
										<h1 class="w-title" v-if="page?.seo_h1">{{ page.seo_h1 }}<span v-if="counter" class="w-counter active">(<span>{{counter}}</span>)</span></h1>
										<div v-if="page?.content" v-html="page.content"></div>

										<BaseWebshopCartErrors v-slot="{errorsItems, warningsItems}">
											<template v-if="errorsItems?.length">
												<div class="global-error" v-for="error in errorsItems" :key="error"><BaseCmsLabel :code="error.label_name" /></div>
											</template>
											<template v-if="warningsItems?.length">
												<div class="global-warning" v-for="warning in warningsItems" :key="warning"><BaseCmsLabel :code="warning.label_name" /></div>
											</template>
										</BaseWebshopCartErrors>

										<div class="w-table" id="items_shoppingcart">
											<WebshopCartItem v-for="item in parcels[0].items" :data="item" :key="item.shopping_cart_code" />
										</div>
										
										<WebshopCouponForm mode="webshop" />

										<WebshopCrossSale v-if="parcels[0]?.items?.length" :cartItems="parcels[0].items" />
									</div>
									<div class="w-col w-col2">

										<div class="w-totals box-shadow">
											<BaseCmsLabel code="shopping_cart_total" class="w-totals-title" tag="div" />
											<div class="w-totals-wrapper">
												<WebshopTotal />

												<BaseWebshopCartErrors v-slot="{errors, warnings}">
													<NuxtLink @click="onFinishShopping" v-if="urls.webshop_customer && !errors && !warnings" :to="urls.webshop_customer" class="btn btn-yellow btn-arrow btn-m w-btn-finish cart-finish-shopping"><BaseCmsLabel code="finish_shopping" tag="span" /></NuxtLink>
												</BaseWebshopCartErrors>
											</div>
										</div>

										<div class="ww-shipping">
											<WebshopFreeDeliveryProgressBar :toFree="toFree" />
											<BaseCmsLabel code="free_delivery" class="ww-preview-free-delivery" tag="div" />
										</div>
										<BaseCmsLabel code="payment_note" class="payment-note" tag="div" />
									</div>
								</div>
								<BaseCmsLabel v-else code="empty_shopping_cart" class="empty-cart" tag="div" />
							</div>
						</main>
					</template>
				</BaseWebshopFreeShipping>
			</BaseWebshopCart>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
</style>
