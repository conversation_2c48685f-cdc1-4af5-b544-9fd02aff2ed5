<template>
	<BaseCmsPage v-slot="{page}">
		<BaseWebshopCheckout v-slot="{user}">
				<main class="main">
					<div class="wrapper main-wrapper">
						<CmsSidebar />

						<div class="main-content">
							<div class="cms-content">
								<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
								<h1 v-if="page?.seo_h1">{{ page.seo_h1 }} <span v-if="user?.first_name">{{ user.first_name }}</span></h1>

								<ClientOnly>
									<BaseWebshopOrder v-slot="{order, shipping, termsPdf, invoicePdf, paymentTransferUrl}">
										<div class="checkout-cnt" v-if="order">
											<div v-if="page?.content" v-html="page.content" v-interpolation />

											<p v-if="termsPdf">
												<a :href="termsPdf" target="_blank" class="btn btn-download btn-download-pdf">
													<BaseCmsLabel code="thank_you_download_terms_pdf" tag="span" />
												</a>
											</p>

											<div class="invoice-container">
												<p><BaseCmsLabel code="choosen_shipping" /> "<strong>{{ shipping?.title }}</strong>".<br><BaseCmsLabel code="order_email" /> <strong>{{order?.customer?.email}}</strong>.</p>
												
												<p v-if="invoicePdf">
													<a :href="invoicePdf" target="_blank" class="btn btn-download btn-download-pdf btn-download-invoice"><BaseCmsLabel :code="order?.cart?.payment_transfer_data ? 'order_download_invoice_payment' : 'order_download_invoice'" tag="span" /></a>
												</p>
											</div>

											<div class="thank-you-wrapper" v-if="!user">
												<BaseCmsLabel code="thank_you_signup" class="thank-you-content" tag="div" />
												<div class="thank-you-login">
													<BaseAuthQuickSignupForm  :show-password="true" class="form-label" v-slot="{fields, status, loading}">
														<div class="thank-you-login" v-if="!status?.success">
															<div v-interpolation>
																<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
																	<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
																		<BaseFormInput :id="'quicksignup-' + item.name" />
																		<BaseCmsLabel tag="label" :for="'quicksignup-' + item.name" :code="item.name" />
																		<span class="error" v-show="errorMessage" v-html="errorMessage" />
																	</p>
																</BaseFormField>
															</div>
															<p><button type="submit" class="btn" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="save" /></button></p>
														</div>
														<div v-if="status?.success && status?.data.label_name" id="thankyou_signup_success" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
													</BaseAuthQuickSignupForm>
												</div>
												<BaseCmsLabel code="safe_purchase_thankyou" class="thank-you-safe" tag="div" />
											</div>


											<template v-if="order?.cart?.payment_transfer_data">
												<BaseWebshopPaymentTransfer :data="order.cart.payment_transfer_data" />

												<p><a :href="paymentTransferUrl+'&mode=pdf'" target="_blank" class="btn btn-m btn-print"><BaseCmsLabel code="print_payment" tag="span" /></a></p>
											</template>
										</div>
									</BaseWebshopOrder>
									<template #fallback>
										<BaseThemeUiLoading />
									</template>
								</ClientOnly>
							</div>
						</div>
					</div>
				</main>
		</BaseWebshopCheckout>
	</BaseCmsPage>
</template>


<script setup>
	const {addScript} = useMeta();

	onMounted(async () => {
		addScript({
			key: 'SmdObject',
			innerHTML: `
				var smdObject = {
				Key: "Cro_858",
				Type: "order",
				OrderId: "",
				Products: []
				};
				var smdWrapper = document.createElement("script");
				smdWrapper.id = "_cpxTag";
				smdWrapper.type = "text/javascript";
				smdWrapper.src = "https://cpx.smind.hr/Log/LogData?data=" + encodeURIComponent(JSON.stringify(smdObject));
				var smdScript = document.getElementsByTagName("script")[0];
				setTimeout(function() {
					smdScript.parentNode.insertBefore(smdWrapper, smdScript);
				},3000);
			`,
		});
	});
</script>

<style lang="less" scoped>
</style>
