<template>
	<BaseCmsPage v-slot="{page}">
	<Body :class="['page-auth page-checkout page-checkout-step1']" />
		<ClientOnly>
			<main class="main">
				<div class="wrapper main-wrapper">
					<!-- <?php foreach($products as $product_code => $product_data):?>
						<div>
							<span data-tracking_gtm_products="Checkout|<?php echo $product_data['code']; ?>">
								<span style="display: none;" data-product_code="<?php echo $product_code; ?>"><?php echo $product_data['code']; ?></span>
								<span style="display: none;" data-product_title="<?php echo $product_code ?>"><?php echo $product_data['title'] ?></span>
								<span style="display: none;" data-product_price="<?php echo $product_code;; ?>"><?php echo Utils::currency_format($product_data['price']); ?></span>
								<span style="display: none;" data-product_manufacturer_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'manufacturer_title')); ?></span>
								<span style="display: none;" data-product_category_title="<?php echo $product_code; ?>"><?php echo trim(Arr::get($product_data, 'category_title')); ?></span>
							</span>
						</div>
					<?php endforeach;?> -->

					<div class="auth-wrapper">
						<div v-if="labels.get('webshop_disabled')" class="webshop-disabled-note">{{labels.get('webshop_disabled')}}</div>
						<template v-else>
							<BaseWebshopCheckout>
								<div class="a-col a-col1 a-login-col1 wc-step-col1">
									<BaseCmsLabel code="guest_checkout" class="a-subtitle" tag="h2" />
									<div v-if="page?.content" class="wc-cnt" v-html="page.content"></div>
									
									<div class="wc-col-cnt">
										<BaseWebshopGuestForm class="form-label wc-guest-form" v-slot="{loading, fields}">
											<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
												<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}]">
													<BaseFormInput id="guest-email" />
													<BaseCmsLabel tag="label" for="guest-email" :code="item.name" />
													<span class="field_error error" v-show="errorMessage" v-html="errorMessage" />
												</p>
											</BaseFormField>
											<button type="submit" class="btn btn-gray btn-wc-guest" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="continue_without_signup" /></button>
										</BaseWebshopGuestForm>
									</div>
								</div>
							</BaseWebshopCheckout>
							<div class="a-col a-col2 a-login-col2 wc-step-col2">
								<BaseCmsLabel code="have_account" class="a-subtitle" tag="h2" />
								<BaseCmsLabel code="login_to_buy" class="wc-cnt" tag="div" />
								<div class="wc-col-cnt">
									<AuthLoginForm mode="checkout" />
								</div>
							</div>
						</template>
					</div>
				</div>
			</main>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
</script>

<style scoped lang="less">
</style>
