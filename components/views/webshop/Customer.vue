<template>
	<BaseCmsPage v-slot="{page}">
	<Body class="page-checkout" />
		<WebshopCheckoutLayout class="checkout-container">
			<template #wcCol1>
				<BaseWebshopCheckout v-slot="{cart}">
					<div v-if="labels.get('webshop_disabled')" class="webshop-disabled-note">{{labels.get('webshop_disabled')}}</div>
					<template v-else>
						<BaseWebshopCustomerForm submit-url="webshop_payment" class="step2 form-label form-cart" v-slot="{loading, fields, errors}" data-autofocus>
							
							<BaseWebshopCart v-slot="{parcels}">
								<div class="steps">
									<WebshopStep :step="2" :title="true" />
									<WebshopStep :step="3" />
								</div>

								<div class="wc-col wc-col1 wc-step2-col1">
									<div class="col-cont">
										<BaseCmsLabel code="customer_details" class="wc-subtitle" tag="h2" />
										<div v-html="page?.content" v-interpolation />
										<template v-if="Object.keys(errors)?.length">
											<BaseCmsLabel tag="div" class="global_error global-error" code="form_validation_error" data-scroll-to-error />
										</template>
										
										<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel, isTouched, value}">
											<div class="field" :class="['field-' + field.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
												<BaseFormInput :readonly="loading" />
												<label :for="field.name" v-html="(labels.get('checkout_' + field.name)) ? labels.get('checkout_' + field.name) : labels.get(field.name)" />
												<BaseCmsLabel v-if="['phone', 'b_phone'].includes(field.name)" tag="span" class="phone-tooltip" code="phone_tooltip" />
												<BaseCmsLabel v-if="['location', 'b_location'].includes(field.name)" tag="span" class="phone-tooltip location-tooltip" code="zipcode_city_tooltip" />
												<span class="field_error error" v-show="errorMessage" v-html="errorMessage" />
												
												<!-- FIXME samo info: ograničenje dostave: silba, ilovik, lopud;  -->
												<div v-if="field.name == 'location' && parcels?.length && parcels[0]?.shipping?.selected?.id > 2 && parcels[0]?.shipping?.selected?.description" class="shipping-restriction-note" v-html="parcels[0].shipping.selected.description"></div>
											</div>
										</BaseFormField>
									</div>

									<div class="wc-btns">
										<button class="btn btn-checkout" type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="goto_step3_button" tag="span" /></button>
										<BaseCmsLabel code="step2_note" class="wc-step-note" tag="div" />
									</div>
								</div>
							</BaseWebshopCart>
						</BaseWebshopCustomerForm>
					</template>

					<WebshopCheckoutTracking v-if="cart?.parcels[0]?.items?.length" step="2" :cart="cart" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
	:deep(.autocomplete-container){top: 53px; border: 0;}
	.field-zipcode,.field-city,.field-b_zipcode,.field-b_city{display: none!important;}
	:deep(.field-group-shipping){display: flex; flex-wrap: wrap;}
	.field-b_first_name{order: 1;}
	.field-b_last_name{order: 2;}
	.field-b_address{order: 3;}
	.field-b_house_number{order: 4;}
	.field-b_location{order: 5;}
	.field-b_phone{order: 6;}
	:deep(.field-group-r1){width: 100%;}
</style>