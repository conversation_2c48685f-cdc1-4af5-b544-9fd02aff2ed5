<template>
	<BaseCmsPage>
	<Body class="page-checkout-step3" />
		<WebshopCheckoutLayout class="checkout-container">
			<template #wcCol1>
				<BaseWebshopCheckout v-slot="{customer, urls, cart}">
					<!-- FIXME kekspay i kreditna kartica. otvoren task za BE https://markerdoo.eu.teamwork.com/app/tasks/26636672 -->
					<div v-if="labels.get('webshop_disabled')" class="webshop-disabled-note">{{labels.get('webshop_disabled')}}</div>
					<template v-else>
						<div class="step3 form-label form-cart">
							<div class="steps">
								<WebshopStep :step="2" />
								<WebshopStep :step="3" :title="true" />
							</div>

							<div class="wc-col wc-col1 wc-step3-col1">
								<BaseCmsLabel code="choose_payment_type" class="wc-subtitle" tag="h2" />

								<BaseWebshopPaymentForm v-slot="{fields, status, onPaymentUpdate, selectedField, loading, meta}">
									<div class="payment-options">
										<div v-if="status?.data?.errors">
											<p class="error" v-for="error in status.data.errors" :key="error">{{ error.error }}</p>
										</div>

										<div class="field-payment" :class="{loading: loading}">
											<template v-for="field in fields" :key="field.value">
												<BaseFormField :item="field" v-slot="{errorMessage}">													
													<BaseFormInput :id="field.code" option-class="payment-row">
														<template #default="{option, updateFormValue}">
															<input type="radio" :name="field.name" :id="'payment-'+option.id" :value="option.id" :checked="option.selected" @change="updateFormValue" @click="onPaymentUpdate(option)" />
															<label :for="'payment-'+option.id">
																{{option.title}}
																<div v-if="option.description" class="payment_info" v-html="option.description"></div>
																<!-- FIXME postaviti neki error kad nije odabrana niti 1 kartica, da se ne moze zavrsiti narudzba -->
																<BaseThemeWebshopCreditCardInstallmentsForm :field="option" :selected-field="selectedField" :onPaymentUpdate="onPaymentUpdate" />												
															</label>
														</template>	
													</BaseFormInput>
												

													<span class="error" v-show="errorMessage" v-html="errorMessage" />
												</BaseFormField>
											</template>
										</div>
									</div>
								</BaseWebshopPaymentForm>

								<BaseCmsLabel code="shipping_type" class="wc-subtitle" tag="h2" />

								<BaseWebshopShippingForm :only-zipcode-allowed="true" v-slot="{fields, onShippingUpdate, status}">
									<div class="section-bill">
										<div class="section-shipping">
											<div v-if="status?.data?.errors">
												<p class="global-error" v-for="error in status.data.errors" :key="error">{{ error.error }}</p>
											</div>

											<div v-for="field in fields" :key="field.value">
												<BaseFormField :item="field" v-slot="{errorMessage}">
													<BaseFormInput :id="field.code" :option-class="'shipping-row' + (fields?.length <= 1 ? ' single-shipping' : '')">
														<template #default="{option, updateFormValue}">
															<input type="radio" :name="field.name" :id="'shipping-'+option.id" :value="option.id" :checked="option.selected" @change="updateFormValue" @click="onShippingUpdate(option)" />
															<label :for="'shipping-'+option.id">
																<template v-if="option.code == 'osobno_preuzimanje' || option.code == 'dostavna_sluzba'">
																	{{option.title}}
																	<div v-if="option.description" class="shipping-data shipping_info">
																		<div v-html="option.description"></div>
																		<template v-if="option.code == 'dostavna_sluzba'">
																			<br><div>
																				<div>{{customer.first_name}} {{customer.last_name}}</div>
																				<div>{{customer.address}} {{customer.house_number}}</div>
																				<div>{{customer.zipcode}} {{customer.city}} {{customer.country_name}}</div>
																				<div>{{customer.phone}}</div>
																				<div>{{customer.email}}</div>
																				<NuxtLink :to="urls.webshop_customer" class="btn-change-address"><BaseCmsLabel code="change_shipping_address" /></NuxtLink>
																			</div>
																		</template>
																	</div>
																</template>
																<template v-else>
																	<div class="shipping-restriction-note">
																		<div v-if="option.description" v-html="option.description"></div>
																		<NuxtLink :to="urls.webshop_customer" class="btn-change-address"><BaseCmsLabel code="change_shipping_address" /></NuxtLink>
																	</div>
																</template>
															</label>
														</template>
													</BaseFormInput>
													<span class="error" v-show="errorMessage" v-html="errorMessage" />
												</BaseFormField>
											</div>
										</div>

										<div class="wc-bill-address" v-if="customer?.b_first_name">
											<p><BaseCmsLabel code="bill_address" tag="strong" /></p>
											<p v-if="customer?.b_first_name || customer?.b_last_name">{{ customer.b_first_name }} {{ customer.b_last_name }}</p>
											<p v-if="customer?.b_oib"><BaseCmsLabel code="oib" />: {{ customer.b_oib }}</p>
											<p v-if="customer?.b_address || customer?.b_house_number">{{ customer.b_address }} {{ customer.b_house_number }}</p>
											<p v-if="customer?.b_zipcode || customer?.b_city">{{ customer.b_zipcode }} {{ customer.b_city }}</p>
										</div>

										<div class="wc-r1" v-if="customer?.b_company_oib">
											<p><BaseCmsLabel code="r1" tag="strong" /></p>
											<p v-if="customer?.b_company_name"><BaseCmsLabel code="company_name" />: {{ customer.b_company_name }}</p>
											<p v-if="customer?.b_company_oib"><BaseCmsLabel code="company_oib" />: {{ customer.b_company_oib }}</p>
											<p v-if="customer?.b_company_address"><BaseCmsLabel code="company_address" />: {{ customer.b_company_address }}</p>
										</div>
									</div>
								</BaseWebshopShippingForm>

								<WebshopCouponForm mode="checkout" />

								<BaseWebshopCart v-slot="{parcels}">
									<div class="wc-cart-title" @click="activeCart = !activeCart">
										<BaseCmsLabel code="cart_totals_title" default="Ukupno u košarici" />:
										<BaseWebshopTotal v-slot="{items}">
											<strong v-if="items?.total" class="cart_info_total"><BaseUtilsFormatCurrency :price="items.total" /></strong>
										</BaseWebshopTotal>
										<span class="wc-btn-toggle wc-btn-toggle-cart" :class="{'active': activeCart}"><span class="o"><BaseCmsLabel code="toggle_cart_open" /></span><span class="c" v-html="labels.get('toggle_cart_close')"></span><span class="toggle-icon"></span></span>

										<BaseWebshopFreeShipping v-slot="{item: toFree}">
											<template v-if="toFree?.amount">
												<div class="wc-shipping-remaining cart_info_total_extra_shipping_to_free_box" v-show="toFree?.amount != 0" >
													<span>
														<BaseCmsLabel code="min_total_missing" /> <span class="wc-shipping-remaining-value cart_info_total_extra_shipping_to_free"><BaseUtilsFormatCurrency :price="toFree.amount" /></span>
													</span>
												</div>
											</template>
										</BaseWebshopFreeShipping>
									</div>
									<div class="wc-cart-container" :class="{'active': activeCart}" >
										<div v-if="parcels?.length && parcels[0].items?.length" class="shoppingcart_items_small cart-small">
											<div class="w-table-small">
												<WebshopCartItemSmall v-for="item in parcels[0].items" mode="checkout" :data="item" :key="item.shopping_cart_code" />
											</div>
										</div>

										<div class="w-totals-cnt">
											<WebshopTotal />
											<BaseUtilsAppUrls v-slot="{items: appUrls}">
												<NuxtLink :to="appUrls.webshop_shopping_cart" class="w-btn-change btn-arrow"><BaseCmsLabel code="change_cart" default="Promijeni sadržaj košarice" tag="span" /></NuxtLink>
											</BaseUtilsAppUrls>
										</div>
									</div>
								</BaseWebshopCart>

								<BaseWebshopReviewOrderForm class="form-label" v-slot="{fields, meta, loading, orderErrors}">
									<div class="wc-section-finish">
										<div class="global_error global-error" v-if="orderErrors?.payment && !loading"><BaseCmsLabel :code="orderErrors.payment" /></div>
										
										<template v-if="fields?.length">

											<div class="wc-step3-btns-container" v-interpolation>
												<div class="wc-terms">
													<div class="webshop-alert-terms" v-show="acceptTermsAlert"><BaseCmsLabel code="terms_error" /></div>
													
													<div v-for="field in fields" :key="field.name" class="webshop_accept_terms webshop-accept-terms" :class="[{'webshop-newsletter': field.name == 'newsletter'},{'webshop-message step3-field-message': field.name == 'message'}]">
														<BaseFormField :item="field" v-slot="{errorMessage, floatingLabel}">
															<template v-if="field.name == 'newsletter'">
																<input type="checkbox" id="field-newsletter" mode="custom" />
																<label for="field-newsletter"><BaseCmsLabel tag="span" for="newsletter" code="newsletter" /></label>
															</template>
															<template v-else-if="field.name == 'message'">
																<p class="field" :class="['field-' + field.name, {'ffl-floated': floatingLabel}]">
																	<BaseFormInput />
																	<BaseCmsLabel tag="label" :for="field.name" :code="field.name+'_webshop'" />
																</p>
															</template>
															<template v-else>
																<BaseFormInput @click="checkAlert" />
																<BaseCmsLabel tag="label" :for="field.name" :code="field.name+'_webshop'" />
															</template>
															<span class="error" v-show="errorMessage" v-html="errorMessage" />
														</BaseFormField>
													</div>
												</div>
												<div class="wc-btns step3-wc-btns">
													<button class="btn btn-checkout btn-finish" :class="[{'accepted': !acceptTermsAlert},{'loading': loading}]" type="submit" :disabled="!meta.valid || loading" v-if="!orderErrors">
														<UiLoader v-if="loading" />
														<BaseCmsLabel code="confirm_order" />
													</button>
												</div>
											</div>
										</template>
									</div>
								</BaseWebshopReviewOrderForm>
							</div>
						</div>
					</template>
					<!-- FIXME pregled detalja narudžbe; gledati na produkciji; 
						kolona šifra vanjskog dobavljaca, popunjeno na narudzba 483032;
						ispis dost: kod količina 483032;
						ispis Broj praćenja pošiljke:  483023, 484082;
						napomena za kupca: 483023;
						poruka/komentar kupca: 483023;
						u footeru polja za ispisao, pripremio, pakirao..., Ispisao: popunilo mi se samo s podacima u adminu i s vremenom 483023;
						neka polja su vidljiva samo adminu
						BE task s podacima: https://markerdoo.eu.teamwork.com/app/tasks/26639324
					-->

					<WebshopCheckoutTracking v-if="cart?.parcels[0]?.items?.length" step="3" :cart="cart" />
				</BaseWebshopCheckout>
			</template>
		</WebshopCheckoutLayout>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const activeCart = ref(false);

	const acceptTerms = ref(false);
	const finishOrderBtn = ref(false);
	function checkAlert(e) {
		if (e.target.name == 'accept_terms') acceptTerms.value = e.target.checked;
	}

	// show/hide alert
	const acceptTermsAlert = computed(() => {
		return acceptTerms.value ? false : true;
	});

	// show/hide alert on page load
	onMounted(() => {
		const acceptTermsCheckbox = document.querySelector('[name="accept_terms"]');
		if (acceptTermsCheckbox) acceptTerms.value = acceptTermsCheckbox?.checked;
	});
</script>

<style lang="less" scoped>
	:deep(.field-payment){
		input[type=radio]:checked + label{
			.payment_info{display: block; white-space: pre-line;}
		}
	}
	:deep(.shipping-row){
		input[type=radio]:checked + label{
			.shipping_info{display: block; white-space: pre-line; font-weight: normal;}
		}
	}

	.webshop-accept-terms{
		order: 3; padding-left: 15px; max-width: 340px;
		@media (max-width: @t){max-width: unset;}
	}
	.webshop-alert-terms{order: 2;}
	.webshop-newsletter{order: 4;}
	
	.webshop-message{
		order: 1; padding-left: 0; max-width: unset; margin-bottom: 15px;
		.field-message{
			padding-bottom: 0;
			:deep(textarea){margin: 0;}
		}
	}

	.step3-wc-btns{
		position: absolute; right: 0; bottom: 20px;
		@media (max-width: @t){position: relative; right: unset; bottom: unset;}
	}
	.shipping-restriction-note{width: 100%; font-weight: normal!important;}
</style>
