<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" :id="'position' + position" class="ln-section-promo ln-section-promo-small">
				<div :class="['ln-promo ln-promo-small']">
					<component :is="item.url_without_domain ? NuxtLink : 'span'" :to="item.url_without_domain ? item.url_without_domain : item.url_without_domain" :id="'position' + position" :class="['ln-promo-img']">
						<BaseUiImage
							:data="item.image_thumbs?.['width980-height670-crop1']"
							default="/images/no-image-980.jpg" />
					</component>

					<div v-if="item.title" class="ln-promo-content">
						<component :is="item.url_without_domain ? NuxtLink : 'div'" class="ln-promo-title" :to="item.url_without_domain"><span v-html="item.title"></span></component>
						<div v-if="item.subtitle" class="ln-promo-subtitle" v-html="item.subtitle" />
					</div>
				</div>
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<script setup>
	import { NuxtLink } from '#components';
</script>

<style lang="less" scoped>
	.ln-section-promo{
		display: inline-block; vertical-align: top; position: relative; margin: 48px 20px 0;
		@media (max-width: @t){margin: 35px 10px 0;}
		@media (max-width: @tp){margin: 32px 0 0;}
	}
	.ln-section-promo-small{
		width: calc(~"33.333% - 40px");
		@media (max-width: @t){width: calc(~"33.333% - 29px");}
		@media (max-width: @tp){width: calc(~"100% - 20px");}
		@media (max-width: @m){width: 100%;}
	}
	.ln-promo{position: relative; text-align: center;}
	.ln-promo-img{
		position: relative; display: block;
		:deep(img){display: block; width: 100%; height: auto; max-width: 100%;}
		&:after{.pseudo(auto, 8px); background: var(--yellow); bottom: -4px; right: 80px; left: 80px;}
	}
	.ln-promo-content{
		padding: 24px 10px 0; display: block;
		@media (max-width: @t){padding: 16px 15px 0;}
	}
	.ln-promo-title{
		font-size: 32px; line-height: 40px; font-weight: 300; text-decoration: none;
		@media (max-width: @t){font-size: 28px; line-height: 35px;}
		@media (max-width: @tp){font-size: 24px; line-height: 26px;}
	}
	.ln-promo-subtitle{
		font-size: 16px; line-height: 21px; font-weight: bold; padding-top: 4px;
		@media (max-width: @t){font-size: 14px; line-height: 18px;}
		@media (max-width: @tp){font-size: 16px; line-height: 20px; padding-top: 2px;}
	}
</style>