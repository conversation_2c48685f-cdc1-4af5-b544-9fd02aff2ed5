<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" :id="'position' + position" class="ln-section-title">
				<h2 class="ln-title" v-html="item.title"></h2>
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<style lang="less" scoped>
	.ln-section-title{
		margin: 100px 0 34px;
		@media (max-width: 1400px){margin: 80px 20px 34px;}
		@media (max-width: @t){margin: 60px 10px 20px;}
		@media (max-width: @tp){margin: 40px 0 15px;}
	}
	.ln-title{
		font-size: 48px; line-height: 50px; text-align: center; padding: 0; font-weight: 300; text-transform: initial;
		@media (max-width: @t){font-size: 36px; line-height: 44px;}
		@media (max-width: @tp){font-size: 30px; line-height: 36px;}
		@media (max-width: @m){font-size: 28px; line-height: 32px;}
	}
</style>
