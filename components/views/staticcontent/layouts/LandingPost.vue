<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" :id="'position' + position" class="ln-section-publish ln-section-items">
				<PublishIndexEntry v-for="item in items" :key="item.id" :item="item" mode="landing" />
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<style lang="less" scoped>
	.ln-section-publish{
		width: calc(~"100% / 3 - 44px"); margin: 0 22px; display: inline-block; vertical-align: top;
		@media (max-width: @t){margin: 0 10px; width: calc(~"100% /3 - 26px");}
		@media (max-width: @tp){width: calc(~"100% /3 - 20px");}
		@media (max-width: @m){width: 100%; margin: 0 0 14px;}
	}
</style>