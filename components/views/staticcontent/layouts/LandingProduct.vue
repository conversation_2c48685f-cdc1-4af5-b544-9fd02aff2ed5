<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" :id="'position' + position" class="ln-section-catalog ln-section-items">
				<CatalogIndexEntry v-for="(item, index) in items" :key="item.id" :item="item" :index="index" mode="landing" />
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<style lang="less" scoped>
	.ln-section-catalog{
		width: calc(~"100% / 5 - 7px"); margin: -1px 0 0 -1px; display: inline-block; vertical-align: top; text-align: left;
		@media (max-width: @t){width: calc(~"100% / 4 - 10px");}
		@media (max-width: @tp){width: calc(~"100% / 4 - 4px");}
		@media (max-width: @m){width: calc(~"100% - -30px"); margin-left: -15px;}
	}
</style>