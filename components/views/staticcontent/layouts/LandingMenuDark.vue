<template>
	<BaseStaticcontentElements v-slot="{menu}">
		<div class="ln-nav-container-special ln-nav-container-special-dark">
			<div class="ln-nav-wrapper">
				<div class="logo-wrapper">
					<BaseCmsLogo class="logo landing-logo logo-light" id="logo" />
				</div>

				<ul class="ln-nav ln-nav-special ln-nav-light">
					<li v-for="menuItem in menu" :key="menuItem.id">
						<a href="javascript:void(0)" @click="scrollTo('#position' + menuItem.position, {offset: 120})"
							><span>{{ menuItem.title }}</span></a
						>
					</li>
				</ul>
			</div>
		</div>
	</BaseStaticcontentElements>
</template>

<script setup>
	const {scrollTo} = useDom();
</script>

<style lang="less" scoped>
</style>