<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" :id="'position' + position" class="ln-section-content">
				<div class="ln-content lists" v-html="item.content"></div>
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<style lang="less" scoped>
	.ln-section-content{
		margin: -15px 0 0; display: flex; justify-content: center;
		@media (max-width: 1400px){margin-right: 20px; margin-left: 20px;}
		@media (max-width: @t){margin-right: 10px; margin-left: 10px;}
		@media (max-width: @tp){margin: -10px 10px 0;}
		@media (max-width: @m){margin: -5px 0 0; justify-content: flex-start}
	}
	.ln-content{
		width: 760px; font-size: 16px; line-height: 28px; text-align: center;
		:deep(iframe){width: 100%;}
		:deep(.btn){margin: 15px 5px 0; min-width: 200px;}
		@media (max-width: @t){font-size: 14px; line-height: 22px;}
		@media (max-width: @tp){max-width: 650px; width: auto; margin: 0 auto;}
		@media (max-width: @m){
			max-width: 100%; text-align: left; font-size: 18px; line-height: 25px;
			:deep(.btn){
				margin: 0 0 10px; min-width: unset; display: inline-flex; width: 100%;
				&:last-child{margin-bottom: 0;}
			}
		}
	}
</style>