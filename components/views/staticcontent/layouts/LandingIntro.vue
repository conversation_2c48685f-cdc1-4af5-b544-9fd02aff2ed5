<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" class="ln-hero-main">
				<div class="ln-hero-container">
					<component :is="item.url_without_domain ? NuxtLink : 'div'" :to="item.url_without_domain ? item.url_without_domain : item.url_without_domain" :id="'position' + position" :class="['ln-hero-promo']">
						<div class="ln-hero-wrapper" v-if="item.image">
							<BaseUiImage
							:data="item.image_thumbs?.['width1400-height600']"
							loading="eager"
							default="/images/no-image-920.jpg"
							:picture="[{maxWidth: '750px', src: item.image2_thumbs?.['width980-height1225'].thumb, default: '/images/no-image-980.jpg'}]" />

							<div class="ln-hero-container-content">
								<div v-if="item.subtitle" class="ln-hero-headline" v-html="item.subtitle" />
								<div v-if="item.title" class="ln-hero-title" v-html="item.title" />
								<div v-if="item.content" class="ln-hero-content" v-html="item.content" />
								<div v-if="item.url_text && item.url_without_domain" class="ln-hero-btns">
									<span class="btn btn-yellow ln-hero-btn">{{ item.url_text }}</span>
								</div>
							</div>
						</div>
					</component>
				</div>
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<script setup>
	import { NuxtLink } from '#components';
</script>

<style lang="less" scoped>
	.ln-hero-container{
		display: flex; width: 1400px; margin: 0 auto; position: relative; margin-top: 16px;
		@media (max-width: 1400px){width: auto; margin-left: 20px; margin-right: 20px;}
		@media (max-width: @tp){margin-top: 9px; margin-left: 10px; margin-right: 10px;}
		@media (max-width: @m){width: calc(~"100% - -30px"); margin-left: -15px; margin-top: 0;}
	}
	.ln-hero-promo{
		min-height: 600px; max-height: 600px; position: relative; margin: 0; width: 100%; background-size: cover !important;
		&:after{.pseudo(auto, 8px); left: 80px; right: 80px; bottom: -4px; background: var(--yellow);}
		@media (max-width: 1400px){min-height: unset; max-height: unset;}
		@media (max-width: @m){
			&:after{left: 24px; right: 24px; height: 6px; bottom: -3px;}
		}
	}
	.ln-hero-wrapper{
		min-height: 600px; padding: 0; position: relative; height: 600px;
		&:before{.pseudo(100%, 100%); background: linear-gradient(90deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%); opacity: 0.7;}
		:deep(picture){display: flex; align-items: center; justify-content: center; overflow: hidden; height: 100%;}
		:deep(img){object-fit: cover; display: block;}
		@media (max-width: 1400px){min-height: unset; height: unset;}
		@media (max-width: @m){
			&:before{background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%); opacity: 0.7;}
		}
	}
	.ln-hero-container-content{
		position: absolute; display: flex; flex-flow: column; text-align: left; align-items: flex-start; justify-content: flex-end; top: 0; max-height: 600px; min-height: 600px; min-width: 520px; max-width: 520px; left: 0; padding-bottom: 80px; margin-left: 80px; font-size: 20px; line-height: 28px; color: var(--white);
		@media (max-width: 1400px){min-height: unset; bottom: 0;}
		@media (max-width: @t){min-width: 440px; max-width: 440px; padding-bottom: 50px; margin-left: 60px; font-size: 18px; line-height: 24px;}
		@media (max-width: @tp){min-width: 390px; max-width: 390px; padding-bottom: 40px; margin-left: 40px; font-size: 16px; line-height: 22px;}
		@media (max-width: @m){max-height: 100%; min-height: 450px; min-width: 100%; max-width: 100%; padding: 0 24px 27px; margin-left: 0;}
	}
	.ln-hero-headline{
		font-size: 18px; line-height: 23px; text-transform: uppercase; color: var(--yellow); font-weight: bold;
		@media (max-width: @t){font-size: 16px; line-height: 20px;}
		@media (max-width: @m){line-height: 19px;}
	}
	.ln-hero-title{
		font-size: 56px; line-height: 73px; font-weight: bold; text-transform: uppercase; color: var(--white);
		@media (max-width: @t){font-size: 48px; line-height: 65px;}
		@media (max-width: @tp){font-size: 32px; line-height: 38px;}
		@media (max-width: @m){text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);}
	}
	.ln-hero-content{
		@media (max-width: @m){text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);}
	}
	.ln-hero-btns{
		margin-top: 20px; display: block;
		@media (max-width: @t){margin-top: 15px;}
		@media (max-width: @m){margin-top: 5px;}
	}
	.ln-hero-btn{
		padding: 0 35px;
		@media (max-width: @tp){padding: 0 24px; height: 48px;}
	}
</style>