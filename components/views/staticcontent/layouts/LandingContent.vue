<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" :id="'position' + position" class="ln-section-content">
				<div class="ln-content lists" v-html="item.content"></div>
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<style lang="less" scoped>
	.ln-section-content{
		margin: 84px 0 0; display: flex; justify-content: center;
		@media (max-width: 1400px){margin: 84px 20px 0;}
		@media (max-width: @t){margin: 70px 10px 0;}
		@media (max-width: @tp){margin-top: 55px;}
		@media (max-width: @m){margin: 35px 0 0; justify-content: flex-start;}
	}
	.ln-content{
		width: 760px; font-size: 22px; line-height: 32px; text-align: center;
		:deep(iframe){width: 100%;}
		@media (max-width: @t){font-size: 18px; line-height: 25px;}
		@media (max-width: @tp){max-width: 650px; width: auto; margin: 0 auto; font-size: 16px;}
		@media (max-width: @m){max-width: 100%; text-align: left;}
	}
</style>