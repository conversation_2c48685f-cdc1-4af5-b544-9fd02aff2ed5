<template>
	<BaseStaticcontentElements v-slot="{items, position}">
		<template v-if="items?.length">
			<div v-for="item in items" :key="item.id" :id="'position' + position" class="ln-section-button">
				<NuxtLink v-if="item.url_without_domain && item.url_text" :to="item.url_without_domain" class="btn btn-yellow ln-button">{{ item.url_text }}</NuxtLink>
			</div>
		</template>
	</BaseStaticcontentElements>
</template>

<style lang="less" scoped>
	.ln-section-button{
		text-align: center; margin: 40px 0 0;
		@media (max-width: @tp){margin-top: 25px;}
		@media (max-width: @m){margin: 16px 0 10px;}
	}
	.ln-button{
		padding: 0 58px;
		@media (max-width: @m){width: 100%; height: 48px;}
	}
</style>
