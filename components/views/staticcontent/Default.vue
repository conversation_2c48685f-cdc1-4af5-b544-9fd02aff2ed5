<template>
	<Body :class="{
		'page-landing-menu': (landingMenu && (!landingMenuSpecial || !landingMenuDark)),
		'page-landing-menu-custom': landingMenuDark || landingMenuSpecial,
		'page-landing-menu-dark': landingMenuDark && !landingMenu && item.items[0].layout != 'landing_menu_dark',
		'page-landing-menu-light': landingMenuSpecial && !landingMenu && item.items[0].layout != 'landing_menu'
	}" />
	<BaseMetaSeo :data="item" />
	<div v-if="item?.items" class="landing-main" :style="{backgroundColor: item.color_1 ? item.color_1 : null, color: item.color_2 ? item.color_2 : null}">
		<div :class="{'landing-wrapper': item?.items?.some(obj => obj.layout != 'landing_widget' && obj.layout != 'landing_widget_content')}">
			
			<div v-if="item.items?.length && ((item.items[0].layout != 'landing_menu' && landingMenuSpecial) || (item.items[0].layout != 'landing_menu_dark' && landingMenuDark))">
				<div class="ln-nav-container-special ln-nav-logo-center" :class="{'ln-nav-logo-center-dark': item.items[0].layout != 'landing_menu_dark' && landingMenuDark}">
					<div class="ln-nav-wrapper">
						<div class="logo-wrapper">
							<BaseCmsLogo class="logo landing-logo" id="logo" />
						</div>
					</div>
				</div>
			</div>

			<template v-for="item in item.items" :key="item.layout">
				<BaseStaticcontentLayout :item="item" />
			</template>
		</div>
	</div>
</template>

<script setup>
	const nuxtApp = useNuxtApp();
	const {onMediaQuery} = useDom();
	const route = nuxtApp._route;
	const router = useRouter();
	const {emit} = useEventBus();
	const endpoints = useEndpoints();
	
	const item = ref([]);
	let landingMenuSpecial = ref(false);
	let landingMenu = ref(false);
	let landingMenuDark = ref(false);

	//rwd
	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 750px)',
	});

	await useApi('/api/nuxtapi/staticcontent/pages/', {
		method: 'POST',
		body: {
			slug: route.path
		}
	}).then(res => {
		if(res?.success && res?.data[0]) {
			let page = res?.data[0];

			//special slider
			let landingProductsSliderCount = 0
			for (let index = 0; index < page?.items?.length; index++) {
				const currentItem = page.items[index];

				if (currentItem?.layout === 'landing_products_slider') {
					landingProductsSliderCount++;
				}

				if (landingProductsSliderCount % 2 !== 0 && landingProductsSliderCount > 0) {
					let lastIndex = -1;
					for (let i = page?.items?.length - 1; i >= 0; i--) {
						if (page?.items[i]?.layout === 'landing_products_slider') {
							lastIndex = i;
							break;
						}
					}

					if (lastIndex !== -1) {
						page.items[lastIndex]['odd'] = true;
					}
				}
			}

			item.value = page;
		}
	})

	onMounted(async () => {
		const hasLandingMenu = item?.value?.items?.some(obj => obj.layout === 'landing_menu');
		const hasLandingMenuDark = item?.value?.items?.some(obj => obj.layout === 'landing_menu_dark');
		const menuVisibleItems = item?.value?.items?.filter(item => item?.elements[0]?.menu_visible === "1")
			.map(item => ({
				title: item?.elements[0]?.menu_title || item?.elements[0]?.title,
				position: item?.elements[0]?.position_h
			}));


		if (hasLandingMenu) {
			landingMenuSpecial.value = true;
		} else if (hasLandingMenuDark) {
			landingMenuDark.value = true;
		} else if (menuVisibleItems?.length) {
			landingMenu.value = true;
		}
	})

	// generate menu from page items
	const menu = computed(() => {
		if (!item.value) return [];

		let menuItems = [];
		if (item.value?.items) {
			item.value.items.forEach(item => {
				if (item.elements) {
					item.elements.forEach(element => {
						if (!element.menu_visible || element.menu_visible == '0') return;
						menuItems.push({
							title: element.menu_title ? element.menu_title : element.title,
							id: element.id,
							position: item.position,
							layout: element.layout_code,
						});
					});
				}
			});
		}

		// append menu items to page object
		item.menu = menuItems;
		emit('data', item.value);

		return menuItems;
	});

	// provide data to child components
	provide('baseStaticcontentPageData', {
		menu: menu.value,
	});

	onUnmounted(() => {
		landingMenuSpecial.value = false;
		landingMenuDark.value = false;
		landingMenu.value = false;
	})
</script>

<style lang="less" scoped>
	.landing-wrapper{
		width: auto; max-width: 1440px; margin: 0 auto; text-align: center;

		@media (max-width: 1400px){width: auto; max-width: unset; margin: 0 10px;}
		@media (max-width: @m){margin: 0 15px;}
	}
</style>
