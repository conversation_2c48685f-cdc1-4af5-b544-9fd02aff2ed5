<template>
	<Body class="page-search" />
    <BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish'}, {module: 'cms'}]" v-slot="{items, searchContent, searchTerm}">
        <div class="c-header s-header">
            <div class="pos-r wrapper s-header-wrapper">
                <SearchTitleContainer :extraterm="searchTerm" />
            </div>
        </div>
        <div class="wrapper">                           
            <template v-if="searchContent == 'publish'">
                <BasePublishPosts v-slot="{items, nextPage, loading, loadMore}">
                    <template v-if="items?.length">
                        <div class="p-items">
                            <PublishIndexEntry v-for="post in items" :key="post.id" :item="post" mode="search" />
                        </div>
                        <ClientOnly>
							<div class="load-more-container" v-if="nextPage">
								<UiLoader v-if="loading" />
								<button v-else type="button" class="btn btn-yellow load-more btn-load-more" @click="loadMore"><BaseCmsLabel code="load_more_publish" /></button>
							</div>
							<BaseUiPagination class="pagination" />
                        </ClientOnly>
                    </template>
                    <div v-else class="s-no-results"><BaseCmsLabel code="nothing_found" default="Nema rezultata za traženi pojam"/></div>
                </BasePublishPosts>
            </template>
            <template v-if="searchContent == 'cms' && items?.cms">
                <BaseCmsPage :fetch="{mode: 'search', 'search_q': searchTerm}" v-slot="{page}" :seo="false">
                    <div v-if="page.items?.length" class="s-items">
                        <article class="s-item" v-for="item in page.items" :key="item.id">
                            <h2 class="s-item-title"><NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink></h2>
                            <div v-if="item?.content" class="s-item-cnt" v-html="limitWords(stripHtml(item.content), 50)" />
                        </article>
                    </div>
                    <div v-else class="s-no-results"><BaseCmsLabel code="nothing_found" default="Nema rezultata za traženi pojam"/></div>
                </BaseCmsPage>
            </template>        
        </div>         		
    </BaseSearchResults>


</template>

<script setup>
    const {stripHtml, limitWords} = useText();
</script>