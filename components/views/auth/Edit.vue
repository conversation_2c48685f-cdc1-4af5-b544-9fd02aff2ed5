<template>
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<ClientOnly>
					<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
					<h1 v-if="page?.seo_h1" class="a-auth-title">{{ page.seo_h1 }}</h1>
					<div v-if="page?.content" v-html="page.content"></div>

					<BaseAuthEditForm class="ajax_siteform form-label auth-form auth-edit-profile-form" v-slot="{fields, loading, status}" >
						<template v-if="status?.data?.errors?.length">
							<div class="global_error global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
						</template>
						<div v-if="status?.success" class="global-success"><BaseCmsLabel :code="status.data.label_name" /></div>
						<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
							<p class="field" :class="['field-' + item.name, {'field-autocomplete': item.name == 'zipcode'}, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]">
								<BaseFormInput :id="item.name" />
								<label v-if="item.name == 'newsletter'" :for="item.name" v-html="labels.get(item.name)"></label>
								<label v-else :for="item.name"><BaseCmsLabel :code="item.name == 'location' ? 'zipcode_city' : item.name" :default="item.name" /></label>
								<BaseCmsLabel v-if="['location'].includes(item.name)" tag="span" class="phone-tooltip location-tooltip" code="zipcode_city_tooltip" />
								<span :id="'field-error-'+item.name" class="field_error error" v-show="errorMessage" v-html="errorMessage" />
							</p>
						</BaseFormField>
						<p class="submit"><button type="submit" :class="{'loading': loading}" :disabled="loading"><UiLoader v-if="loading" /><BaseCmsLabel code="save" /></button></p>
					</BaseAuthEditForm>
				</ClientOnly>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const {onMediaQuery, insertBefore} = useDom();
	onMediaQuery({
		query: '(max-width: 750px)',
		enter: () => {
			insertBefore('.a-auth-title', '.sidebar-container');
		},
	});
</script>

<style lang="less" scoped>
	.field-zipcode, .field-city{display: none;}
	:deep(.autocomplete-container){top: 53px; border: 0;}
	.global-success{width: 100%;}
</style>