<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseAuthUser v-slot="{user, urls, currentUrl}">
				<AuthMainLayout>
					<template #authContent>
						<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
						<h1 v-if="user?.first_name && page?.title" class="a-auth-title"><BaseCmsLabel code="welcome" /> {{user.first_name}}</h1>

						<div v-if="page?.content" v-html="page.content"></div>

						<div class="a-intro">
							<div class="a-intro-left">
								<BaseCmsLabel code="you_can" class="a-intro-title" tag="p" />
								<ul class="a-menu">
									<li v-html="labels.get('auth_view_orders').replace('%LINK%', urls.auth_my_webshoporder)"></li>
									<li v-html="labels.get('auth_view_coupons').replace('%LINK%', urls.auth)"></li>
									<li v-html="labels.get('auth_view_wishlist').replace('%LINK%', urls.auth_wishlist)"></li>
									<li v-html="labels.get('auth_edit_profile').replace('%LINK%', urls.auth_edit)"></li>
									<li v-html="labels.get('auth_change_password').replace('%LINK%', urls.auth_change_password)"></li>
									<li v-html="labels.get('auth_logout').replace('%LINK%', urls.auth_logout+'?redirect=/')"></li>
								</ul>
							</div>

							<div class="a-intro-user">
								<p v-if="user?.first_name && user?.last_name" class="a-intro-title a-intro-user-title"><strong>{{user.first_name}} {{user.last_name}}</strong></p>
								<p>
									<span class="a-intro-email" v-if="user?.email">{{user.email}}</span><br>
									{{user.phone}}<br>
									<template v-if="user?.address">
										{{user.address}} {{user.house_number}}<br>
										{{user.zipcode}} {{user.city}}
									</template>
								</p>
								<p>
									<NuxtLink class="btn btn-medium btn-yellow a-btn-edit" :to="urls.auth_edit" :class="{'active': urls.auth_edit == currentUrl}"><BaseCmsLabel code="edit_profile" /></NuxtLink>
								</p>
							</div>
						</div>

						<BaseAuthOrders v-slot="{items: orders}">
							<div class="auth-box orders-container" id="orders">
								<h2 class="a-section-title"><NuxtLink :to="urls.auth_my_webshoporder"><BaseCmsLabel code="latest_orders" tag="span" /></NuxtLink></h2>
								<AuthWebshopOrders v-if="orders?.length" :orders="orders" mode="dashboard" />
								<BaseCmsLabel v-else code="auth_no_orders" class="auth-no-orders" tag="div" />
							</div>
						</BaseAuthOrders>

						<div class="auth-box auth-box-coupons" id="coupons">
							<BaseCmsLabel code="coupon_dashboard_title" class="a-section-title a-section-coupons-title" tag="h2" />
							
							<div class="a-section-coupons-cnt">
								<WebshopCouponForm mode="auth" />
							</div>
						</div>

						<!-- FIXME
						<?php if (Kohana::$environment === 1 AND $message_type == 'success' AND $message == 'confirm_signup'): ?>
							<script>
								dataLayer.push({
									'event': 'trackEvent',
									'category': 'Registration'
									'action': 'Process'
									'label': 'Success'
								});
							</script>
						<?php endif; ?> -->
					</template>
				</AuthMainLayout>
			</BaseAuthUser>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const {onMediaQuery, insertBefore} = useDom();
	onMediaQuery({
		query: '(max-width: 750px)',
		enter: () => {
			insertBefore('.a-auth-title', '.sidebar-container');
		},
	});
</script>

<style lang="less" scoped>
</style>
