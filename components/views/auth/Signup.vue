<template>
	<BaseCmsPage v-slot="{page}">
	<Body class="page-auth" />
		<main class="main">
			<div class="wrapper main-wrapper">
				<div class="auth-wrapper">
					<div class="a-col a-col1 a-signup-col1 box-shadow">
						<BaseAuthSignupForm class="form-label auth-form auth-signup-form" v-slot="{fields, errors, apiErrors, status, contentType, loading}">
							<template v-if="apiErrors?.length">
								<div class="global-error" v-for="apiError in apiErrors" :key="apiError">{{ apiError.field }}: {{ apiError.error }}</div>
							</template>
							<template v-if="Object.keys(errors)?.length">
								<BaseCmsLabel tag="div" class="global-error" code="form_validation_error" data-scroll-to-error />
							</template>
							
							<BaseCmsLabel code="signup" tag="h2" class="register-title" />
							<AuthSocialLogin mode="signup" />
							
							<BaseAuthConfirmSignup v-if="contentType == 'confirmSignup'" v-slot="{status}">
								<BaseCmsLabel tag="div" class="confirm-info-message" :code="status.label_name" />
							</BaseAuthConfirmSignup>
							<template v-else>
								<template v-if="!status?.success">
									<BaseCmsLabel code="login_information" class="a-subtitle a-signup-login" tag="h2" />
									<div class="form-wrapper">
										<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel, isTouched, value}">
											<BaseCmsLabel v-if="item.name == 'first_name'" code="personal_information" class="a-subtitle a-form-title-personal" tag="h2" />
											<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}, {'success': !errorMessage && isTouched && value}]" v-interpolation>
												<BaseFormInput :id="item.name" />
												<BaseCmsLabel v-if="item.name == 'accept_terms'" v-interpolation tag="label" :for="item.name" :code="item.name" />
												<label v-else :for="item.name" :class="'label-'+ item.name"><BaseCmsLabel :code="label(item.name)" /></label>
												<span :id="'field-error-'+item.name" class="field_error error" v-show="errorMessage" v-html="errorMessage" />
											</p>
										</BaseFormField>
										<button class="btn btn-gray btn-signup" type="submit" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="signup_confirm" /></button>
									</div>
								</template>
								<div v-else>
									<BaseCmsLabel tag="div" class="global-success" :code="status.data.label_name" />
								</div>
							</template>
						</BaseAuthSignupForm>
					</div>
					<div class="a-col a-col2 a-signup-col2">
						<div v-if="page?.content" v-html="page?.content" v-interpolation></div>
						<BaseAuthUser v-slot="{urls}">
							<NuxtLink :to="urls.auth_login" class="btn a-btn-signup"><BaseCmsLabel code="login" tag="span" /></NuxtLink>
						
							<p class="auth-links">
								<NuxtLink :to="urls.auth_forgotten_password" class="btn-forgotten"><span v-html="labels.get('forgotten_password')"></span></NuxtLink>
							</p>
						</BaseAuthUser>
					</div>
				</div>
			</div>
		</main>
	</BaseCmsPage>
</template>

<script setup>
	const {getAppUrl} = useApiRoutes();
	const labels = useLabels();
	function label(field) {
		let l = labels.get(field + '_signup');
		if(!l) l = labels.get(field);
		return l;
	}
</script>

<style lang="less" scoped>
	.auth-links{
		a{margin: 0; text-decoration: none;}
		:deep(span)>span{
			display: block; text-decoration: underline;
			&:hover{text-decoration: none;}
		}
	}
	.register-title{
		padding: 0 0 12px; font-size: 24px; line-height: 1.5; color: var(--black);
		@media (max-width: @tp){padding-bottom: 8px; font-size: 16px;}
	}
</style>
