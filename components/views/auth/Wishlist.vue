<template>
	<BaseCmsPage>
	<Body :class="'page-wishlist'" />
		<ClientOnly>
			<BaseCatalogWishlist v-slot="{items, onRemove, loading}">
				<div class="wrapper">
					<div class="wishlist-header">
						<h1 class="wishlists-title">
							<BaseCmsLabel code="wishlists" /> <span v-if="items?.length > 0" class="wishlist-title-counter wishlist_count">{{items?.length}}</span>
						</h1>
					</div>


					<div v-if="items?.length > 0" id="view_wishlist">
						<div @click="onRemove" class="btn btn-border btn-wishslit-delete" :class="{'loading': loading}" :disabled="loading">
							<UiLoader v-if="loading" color="black" /><BaseCmsLabel code="wishlist_delete" tag="span" />
						</div>

						<div class="c-items wishlist-items">
							<template v-for="item in items" :key="item.id">
								<CatalogIndexEntry :item="item" mode="wishlist" />
							</template>
						</div>
					</div>
					<div v-else class="c-empty wishlist-empty" id="empty_wishlist">
						<BaseCmsLabel code="wishlist_no_products_title" class="no-wishlist-title" tag="div" />
					</div>
				</div>
			</BaseCatalogWishlist>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
</script>

<style lang="less" scoped>
</style>