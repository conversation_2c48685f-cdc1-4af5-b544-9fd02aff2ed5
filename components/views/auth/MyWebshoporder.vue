<template>
	<BaseCmsPage v-slot="{page}">
	<Body :class="'page-auth page-orders'" />
		<ClientOnly>
			<AuthMainLayout>
				<template #authContent>
					<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
					<h1 v-if="page?.seo_h1" class="a-auth-title">{{ page.seo_h1 }}</h1>
					<div v-if="page?.content" v-html="page.content"></div>

					<BaseAuthOrders v-slot="{items: orders}">
						<div id="items_widgetlist_webshop_layout" v-if="orders?.length">
							<AuthWebshopOrders :orders="orders" mode="orders" />
						</div>
						<BaseCmsLabel v-else code="auth_no_orders" class="auth-no-orders" tag="div" />
					</BaseAuthOrders>
				</template>
			</AuthMainLayout>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const {onMediaQuery, insertBefore} = useDom();
	onMediaQuery({
		query: '(max-width: 750px)',
		enter: () => {
			insertBefore('.a-auth-title', '.sidebar-container');
		},
	});
</script>

<style lang="less" scoped>
</style>