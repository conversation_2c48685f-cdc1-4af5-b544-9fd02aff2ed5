<template>
	<BaseCmsPage v-slot="{page}">
	<Body class="page-auth" />
		<main class="main">
			<div class="wrapper main-wrapper">
				<div class="auth-wrapper">
					<div class="a-col a-col1 a-login-col1">
						<h1 style="display: none;" v-if="page?.seo_h1">{{ page?.seo_h1 }}</h1>
						<AuthLoginForm mode="login" />
					</div>
					<BaseAuthUser v-slot="{urls}">
						<div class="a-col a-col2 a-login-col2">
							<div v-if="page?.content" v-html="page?.content" v-interpolation></div>
							<NuxtLink :to="urls.auth_signup" class="btn a-btn-signup"><BaseCmsLabel code="signup" tag="span" /></NuxtLink>
						</div>
					</BaseAuthUser>
				</div>
			</div>
		</main>
	</BaseCmsPage>
</template>

<script setup>
</script>

<style lang="less" scoped>
</style>
