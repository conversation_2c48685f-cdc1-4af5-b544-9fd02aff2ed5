<template>
	<BaseCmsPage v-slot="{page}">
		<main class="main">
			<div class="wrapper main-wrapper">
				<div class="forgotten-password-wrapper">
					<h1 v-if="page?.seo_h1" class="forgotten-password-title">{{ page.seo_h1 }}</h1>
					<div v-if="page?.content" v-html="page.content"></div>

					<ClientOnly>
						<BaseAuthForgottenPasswordForm class="form-label auth-form auth-forgotten-password-form" v-slot="{fields, loading, status, urls}">
							<div v-if="status && status?.success" class="global-success"><BaseCmsLabel :code="status.data?.label_name" /></div>

							<template v-if="!status || (status && !status.success)">
								<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
									<p class="field" :class="[{'ffl-floated': floatingLabel}, {'err': errorMessage}]">
										<BaseFormInput />
										<BaseCmsLabel tag="label" :for="item.name" :code="item.name" />
										<span class="field_error error" v-show="errorMessage" v-html="errorMessage" />
									</p>
								</BaseFormField>
								<p class="submit"><button type="submit" class="g-recaptcha" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="send" /></button></p>
								<p class="auth-links">
									<NuxtLink class="back" :to="urls.auth_login"><span><BaseCmsLabel code="back_to_login" /></span></NuxtLink>
									<NuxtLink class="signup" :to="urls.auth_signup"><span><BaseCmsLabel code="add_new_account" /></span></NuxtLink>
								</p>
							</template>
						</BaseAuthForgottenPasswordForm>
						<template #fallback>
							<BaseThemeUiLoading />
						</template>
					</ClientOnly>
				</div>
			</div>
		</main>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
</style>