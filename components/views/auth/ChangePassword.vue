<template>
	<BaseCmsPage v-slot="{page}">
		<AuthMainLayout>
			<template #authContent>
				<ClientOnly>
					<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
					<h1 v-if="page?.seo_h1" class="a-auth-title">{{ page.seo_h1 }}</h1>
					<div v-if="page?.content" v-html="page.content"></div>
					
					<BaseAuthChangePasswordForm v-slot="{fields, status, loading}" class="ajax_siteform form-label auth-form auth-change-password-form">
						<template v-if="status?.data?.errors?.length">
							<div class="global_error global-error" v-for="error in status.data.errors" :key="error">{{ error.field }}: {{ error.error }}</div>
						</template>
						<div v-if="status && status?.success" class="global-success"><BaseCmsLabel :code="status.data?.label_name" /></div>

						<template v-if="!status?.success">
							<BaseFormField v-for="item in fields" :key="item.name" :item="item" v-slot="{errorMessage, floatingLabel}">
								<p class="field" :class="['field-' + item.name, {'ffl-floated': floatingLabel}, {'err': errorMessage}]">
									<label :for="item.name" :class="'label' + item.name" v-html="(labels.get('reset_' + item.name)) ? labels.get('reset_' + item.name) : labels.get(item.name)" />
									<BaseFormInput />
									<span :id="'field-error-'+item.name" class="field_error error" v-show="errorMessage" v-html="errorMessage" />
								</p>
							</BaseFormField>
							<button type="submit" class="g-recaptcha" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="save" /></button>
						</template>
					</BaseAuthChangePasswordForm>
				</ClientOnly>
			</template>
		</AuthMainLayout>
	</BaseCmsPage>
</template>

<script setup>
	const labels = useLabels();
	const {onMediaQuery, insertBefore} = useDom();
	onMediaQuery({
		query: '(max-width: 750px)',
		enter: () => {
			insertBefore('.a-auth-title', '.sidebar-container');
		},
	});
</script>

<style lang="less" scoped>
</style>