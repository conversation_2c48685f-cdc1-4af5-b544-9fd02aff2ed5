<template>
	<BaseCmsPage v-slot="{page}">
		<BaseLocationPoints v-slot="{items: points}">
			<main class="main">
				<div class="wrapper main-wrapper">
					<CmsSidebar />

					<div class="main-content">
						<div class="cms-content">
							<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
							<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
							<div v-if="page?.content" v-html="page.content" v-interpolation />

							<template v-if="points?.length">
								<CmsLocations class="nav-locations" />
							</template>

							<ClientOnly>
								<CmsMap :items="points" mode="locations-map" />
							</ClientOnly>
						</div>
					</div>
				</div>
			</main>
		</BaseLocationPoints>
	</BaseCmsPage>
</template>

<script setup>
</script>

<style lang="less" scoped>
</style>