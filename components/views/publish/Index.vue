<template>
	<BasePublishCategory v-slot="{item: category}" :seo="true">
		<BasePublishPosts v-slot="{items, nextPage, loadMore, loading}">
			<div class="publish">
				<div class="wrapper">
					<div class="p-header">
						<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" />
						<h1 v-if="category?.seo_h1">{{category.seo_h1}}</h1>
						<div v-if="category?.content" v-html="category.content" v-interpolation />
					</div>
				

					<div v-if="items?.length" :id="'items_' + category.code + '_layout'">
						<div class="p-items p-items-blog" :id="'items_' + category.code">
							<div v-if="items?.length >= 1" class="p-featured">
								<BaseCmsLabel code="special_articles" class="hp-header-label blog-label" tag="div" />
								<PublishIndexEntry v-for="(post, index) in items.slice(0, 3)" :key="post.id" :item="post" :index="index" />
							</div>
							<PublishIndexEntry v-for="(post, index) in items.slice(3)" :key="post.id" :item="post" :index="index + 3" />

							<div class="load-more-container" v-if="nextPage">
								<UiLoader v-if="loading" />
								<button v-else type="button" class="btn btn-yellow load-more btn-load-more" @click="loadMore"><BaseCmsLabel code="load_more_publish" /></button>
							</div>
							<BaseUiPagination class="pagination" />
						</div>
					</div>
					<div v-else><BaseCmsLabel code="no_publish" /></div>
				</div>
			</div>
		</BasePublishPosts>
	</BasePublishCategory>
</template>

<style lang="less" scoped>
</style>