<template>
	<BasePublishDetail :root-category="true" v-slot="{item}">
		<div class="wrapper">
			<div class="pd-header">
				<CmsBreadcrumbs v-if="item?.breadcrumbs" :items="item.breadcrumbs" />
				<h1 class="pd-title">{{ item.seo_h1 }}</h1>
				<div class="pd-info">
					<div class="pd-date"><BaseUtilsFormatDate :date="item.datetime_published" format="DD.MM.YYYY." /></div>

					<template v-if="item?.feedback_rate_widget">
						<FeedbackRates :rates="item.feedback_rate_widget.rates" />
					</template>

					<template v-if="item?.feedback_comment_widget && item.feedback_comment_widget.comments_status != 1">
						<div class="pd-comments">
							<div @click="scrollTo('#comments', {offset: 80})" class="value">
								<BaseCmsLabel code="comments" class="label" tag="span"/>
								({{item.feedback_comment_widget.comments ? item.feedback_comment_widget.comments : 0}})
							</div>
						</div>
					</template>
				</div>
			</div>

			<div v-if="item?.main_image_upload_path" class="pd-hero-image">
				<BaseUiImage :data="item.main_image_thumbs?.['width1400-height800-crop1']"  default="/images/no-image-1400.jpg" :alt="item.main_image_description" />
			</div>

			<div class="cms-content pd-content">
				<div v-if="item.short_description" class="extra pd-short-desc" v-html="item.short_description" v-interpolation></div>

				<div class="pd-desc">
					<div v-if="item.content" v-html="item.content" v-interpolation></div>

					<ul v-if="item.documents?.length" class="pd-documents">
						<li v-for="document in item.documents" :key="document.id">
							<a :href="document.url" target="_blank">{{document.title}}</a>
						</li>
					</ul>
				</div>

				<div v-if="item?.images?.length > 1" class="pd-thumbs">
					<template v-for="image in item.images" :key="image.id">
						<a :href="image.url" class="fancybox" rel="gallery">
							<BaseUiImage loading="lazy" :data="image.file_thumbs?.['width920-height480-crop1']" :alt="image.description" />
						</a>
					</template>
				</div>

				<ClientOnly>
					<CmsShare />
				</ClientOnly>

				<div v-if="item.feedback_comment_widget" id="comments">
					<ClientOnly>
						<FeedbackCommentsForm :item="item" mode="pd" />
					</ClientOnly>
				</div>
			</div>
		</div>

		<BasePublishPostsWidget :fetch="{category_code: 'blog', limit: 3, id_exclude: item?.id, extra_fields: ['short_description'], response_fields: ['id', 'url_without_domain', 'title', 'main_image_thumbs', 'short_description']}" v-slot="{items}">
			<div class="pd-related" v-if="items.length">
				<div class="wrapper">
					<h2 class="pd-related-title"><BaseCmsLabel code="publish_related" /></h2>
					<div class="p-items">
						<PublishIndexEntry v-for="post in items" :item="post" :key="post.id" />
					</div>
					<div class="pw-related-btns">
						<NuxtLink :to="item.category_url_without_domain" class="btn btn-yellow btn-all"><BaseCmsLabel code="all_articles" /></NuxtLink>
					</div>
				</div>
			</div>
		</BasePublishPostsWidget>
	</BasePublishDetail>
</template>

<script setup>
	const {scrollTo} = useDom();
</script>

<style lang="less" scoped>
</style>