<template>
	<BaseCmsPage v-slot="{page}">
		<main class="main">
			<div class="wrapper main-wrapper">
				<CmsSidebar />

				<div class="main-content">
					<div class="cms-content">
						<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
						<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
						<div v-if="page?.content" v-html="page.content" v-interpolation />

						<BaseCmsRotator :fetch="{code: 'faq', limit: 0, response_fields: ['id','code','title','title2','content']}" v-slot="{items}">
							<template v-if="items?.length">
								<template v-for="(item, index) in items" :key="item.id">
									<CmsFaq :item="item" :index="index" />
								</template>
							</template>
						</BaseCmsRotator>

						<CmsShare />
					</div>
				</div>
			</div>
		</main>
	</BaseCmsPage>
</template>