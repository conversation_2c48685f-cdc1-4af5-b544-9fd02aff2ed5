<template>
	<BaseCmsPage v-slot="{page}">
	<Body class="page-contact" />
		<!-- FIXME - nakon refresha na devu ovdje /lokacije-trgovina/ i ovdje kontakt/ baci error; provjeriti nakon builda nakon cratis problema s uploadom -->
		<BaseLocationPoints v-slot="{items: points}">
			<main class="main">
				<div class="wrapper main-wrapper">
					<CmsSidebar mode="contact" />

					<div class="main-content">
						<div class="cms-content">
							<CmsBreadcrumbs v-if="page?.breadcrumbs" :items="page.breadcrumbs" />
							<h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>

							<div class="contact-row">
								<div v-if="page?.content" class="contact-col contact-col1" v-html="page.content" v-interpolation />


								<div class="contact-col contact-col2">
									<div v-if="page?.element_extra" v-html="page.element_extra" v-interpolation />
									<BaseCmsLabel code="social" class="social-footer" tag="div" />

									<template v-if="points?.length">
										<BaseCmsLabel code="store_locations" class="locations-title" tag="h3" />
										<CmsLocations class="nav-locations nav-locations-contact" />
									</template>
									<BaseCmsLabel code="contact_locations_link" tag="div" />
								</div>
							</div>
						</div>
					</div>
				</div>
			</main>
			
			<ClientOnly>
				<CmsMap :items="points" />
			</ClientOnly>
		</BaseLocationPoints>
	</BaseCmsPage>
</template>

<style lang="less" scoped>
	
</style>