<template>
	<BaseCmsPage v-slot="{page}">
	<Body class="page-wide page-flyer" />
        <main class="main">
            <div class="wrapper main-wrapper main-wrapper-flyer">
		        <div v-if="labels.get('flyer_pdf_download')" class="flyer-download" v-html="labels.get('flyer_pdf_download')"></div>

                <BaseCmsRotator :fetch="{code: 'flyer', limit: 1, response_fields: ['id','title','title2','content']}" v-slot="{items}">
                    <template v-if="items?.length">
                        <template v-for="item in items" :key="item.id">
                            <h1 v-if="item?.title" v-html="item.title"></h1>
			                <div v-if="item?.title2" class="page-subtitle">{{item.title2}}</div>
			                <div v-if="item?.content" class="flyer-content" v-html="item.content"></div>                            
                        </template>
                    </template>	
                </BaseCmsRotator>
            </div>
        </main>
	</BaseCmsPage>
</template>

<script setup>
    const labels = useLabels();
</script>