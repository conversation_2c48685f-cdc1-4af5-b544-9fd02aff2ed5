<template>
	<BaseCmsPage v-slot="{page}">
		<BaseCmsRotator :fetch="{code: 'homepage', limit: 1, cached: false, response_fields: ['id','link','url_without_domain','title','title2','content','image_upload_path','image_thumbs','image_2_thumbs','element_button','element_background','element_position']}" v-slot="{items}">
			<div v-if="items?.length" class="hp-header">
				<div class="hero-slider-container">
					<div class="hp-header-label hp-header-label1" v-html="labels.get('rotator_text_left')"></div>
					<div class="hp-header-label hp-header-label2" v-html="labels.get('rotator_text_right')"></div>
					<div class="hero-slider">
						<template v-for="item in items" :key="item.id">
							<component :is="item.link ? NuxtLink : 'div'" :to="item.link ? item.url_without_domain : undefined" :class="['c-promo', item.element_background == 'light' && 'c-promo-light', item.element_position == 'position_center' && 'c-promo-center', 'c-promo-'+item.id]">
								<div class="wrapper-hp-promo">			
									<BaseUiImage
									:data="item.image_thumbs?.['width1400-height600']"
									loading="eager"
									default="/images/no-image-920.jpg"
									:picture="[{maxWidth: '750px', src: item.image_2_thumbs?.['width980-height1225'].thumb, default: '/images/no-image-980.jpg'}]" />

									<div class="c-promo-container">
										<div v-if="item.title2" class="c-promo-headline" v-html="item.title2" />
										<div v-if="item.title" class="c-promo-title" v-html="item.title" />
										<div v-if="item.content" class="c-promo-content" v-html="item.content" />
										<div v-if="item.element_button" class="c-promo-btns">
											<span class="btn c-promo-btn" :class="{ 'btn-yellow': item.element_background === 'dark' }">{{ item.element_button }}</span>
										</div>
									</div>
								</div>							
							</component>
						</template>
					</div>
				</div>
			</div>			
		</BaseCmsRotator>

		<BaseCmsRotator :fetch="{code: 'popular_categories', limit: 1, response_fields: ['id','catalogcategory_ids']}" v-slot="{items}">
			<div v-if="items?.length" class="popular-categories-section">
				<div class="wrapper">
					<div class="popular-categories-cnt">
						<div class="pop-hp-categories-title" v-html="labels.get('hp_categories_title')"></div>
						<div class="pop-hp-categories">
							<template v-for="item in items" :key="item.id">
								<BaseCatalogCategoriesWidget v-if="item.catalogcategory_ids?.length" :fetch="{id: item.catalogcategory_ids, limit: 12, response_fields: ['id','url_without_domain','title','main_image_thumbs']}" v-slot="{items: categories}">
									<template v-if="categories?.length">
										<NuxtLink v-for="category in categories" :key="category.id" class="cat-item hp-cat-item" :to="category.url_without_domain">
											<BaseUiImage loading="lazy" :data="category.main_image_thumbs?.['width288-height288']" default="/images/no-image-144.jpg" :alt="category.title"/>
											<span>{{category.title}}</span>
										</NuxtLink>
									</template>
								</BaseCatalogCategoriesWidget>
							</template>
						</div>
					</div>
				</div>
			</div>
		</BaseCmsRotator>
		
		<LazyCmsHpPromosLight hydrate-on-visible />

		<div class="hp-brands">
			<div class="wrapper">
				<LazyCatalogBrands mode="hp_brands" hydrate-on-visible />
			</div>
		</div>

		<div class="section-gray section-promo">
			<div class="wrapper">
			
				<LazyCmsHpBenefits hydrate-on-visible />
								
				<LazyCmsHpPromosDark hydrate-on-visible />
				
				<LazyCmsHpPanels hydrate-on-visible />

				<LazyCatalogHpCategories hydrate-on-visible />

				<LazyCmsHpLocations hydrate-on-visible />	
			
				<LazyCmsHpMiele hydrate-on-visible />

			</div>
		</div>

		<LazyCatalogSpecialProducts hydrate-on-visible :pageId="page?.id" />

		<LazyPublishLatestPosts hydrate-on-visible />

	</BaseCmsPage>
</template>

<script setup>
	import { NuxtLink } from '#components';
	const labels = useLabels();	
</script>
