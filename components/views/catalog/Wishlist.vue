<template>
	<BaseCmsPage v-slot="{page}">
	<Body class="page-catalog-view_wishlist" />
    <div class="wrapper">
        <ClientOnly>
            <BaseCatalogWishlist v-slot="{items,  onRemove}">
                <div class="wishlist-header">
                    <h1 class="wishlists-title"><BaseCmsLabel code="wishlists" /> <span v-if="items?.length" class="wishlist-title-counter">{{items?.length}}</span></h1>                    
                </div>
                <div v-if="items?.length" id="view_wishlist">
                    <div @click="onRemove" class="btn btn-border btn-wishslit-delete">
                        <span><BaseCmsLabel code="wishlist_delete" /></span>
                    </div>
                    <div class="c-items wishlist-items">
                        <CatalogIndexEntry v-for="item in items" :key="item.id" :item="item" mode="wishlist" />    
                    </div>
                </div>
                <div v-else class="c-empty wishlist-empty">
                    <div class="no-wishlist-title"><BaseCmsLabel code="wishlist_no_products_title" /></div>
                </div>
            </BaseCatalogWishlist>
        </ClientOnly>
    </div>
	</BaseCmsPage>
</template>

<script setup>
	const auth = useAuth();
	const labels = useLabels();
	const {getAppUrl} = useApiRoutes();
	const isLoggedIn = computed(() => auth.isLoggedIn());

	onMounted(() => {
		if(isLoggedIn.value) {
			navigateTo({path: getAppUrl('auth_wishlist')})
		}
	})

</script>