<template>
	<BaseCatalogCategory v-slot="{item: category, contentType}" :seo="true">
		<BaseCatalogProducts :fetch="{extra_data: ['documents']}" v-slot="{items: products, loading, pagination, nextPage, loadMore}" :infinite-scroll="2" @loadProducts="onLoad">
			<Body :class="['catalog-layout-page', contentType == 'manufacturer' && 'page-brand', contentType == 'search' && 'page-search', toggleFilters && 'active-filter', category?.code == 'kompleti' && 'page-groupp-index']" />
			<BaseCatalogItemsLayout v-slot="{itemsLayout, changeItemsLayout}" default="grid">
				<div class="wrapper c-header" :class="contentType == 'search' && 's-header'">
				
					<div v-if="contentType == 'search'" class="pos-r wrapper s-header-wrapper">
						<ClientOnly>
							<BaseSearchResults :search-modules="[{module: 'catalog'}, {module: 'publish'}, {module: 'cms'}]" v-slot="{searchTerm}">
								<SearchTitleContainer :extraterm="searchTerm" />
							</BaseSearchResults>
						</ClientOnly>
					</div>
					<template v-else>
						<CmsBreadcrumbs :items="category.breadcrumbs" />

						<div v-if="contentType == 'manufacturer' && category.main_image" class="cm-logo">
							<BaseUiImage :data="category.main_image_thumbs?.['width350-height60']" default="/images/no-image-100.jpg" :alt="category.seo_h1" />                        
						</div>
						<h1 v-else-if="category?.seo_h1" class="c-title" v-html="category.seo_h1"></h1>


						<BaseUiAccordion v-if="category?.content && category?.hide_description != 1">
							<BaseUiAccordionPanel id="catalogDescription" v-slot="{onToggle, active}">
								<div class="c-desc" :class="{ active }">
									<div v-if="category?.content" ref="contentRef" class="c-desc-content" :class="{'active': active}" v-html="category.content" v-interpolation />
									<div v-if="isLongText" class="c-desc-btn" @click="onToggle">
										<span class="more">{{ labels.get('category_description_more') }}</span>
									</div>
								</div>
							</BaseUiAccordionPanel>
						</BaseUiAccordion>

						<CatalogCategoriesSpecial v-if="category && contentType == 'category' && mobileBreakpoint && category?.code != 'kompleti'" :category="category" />
					</template>
				</div>

				<ClientOnly>
					<div v-if="contentType == 'category' && category?.id && category?.code != 'kompleti'" class="wrapper wrapper-promo">
						<CmsPromo :categoryId="category.id" modeClass="c-promo" />
					</div>
				</ClientOnly>

				<div class="wrapper wrapper-toolbar">
					<div v-if="category?.code != 'kompleti'" class="clear toolbar"> 
						<div class="toolbar-col toolbar-col1">
							<div v-if="!mobileBreakpoint && pagination?.items?.total > 1" class="c-counter">{{pagination.items.total}} proizvod/a</div>
						</div>  
						<div class="toolbar-col toolbar-col2">
							<BaseCatalogActiveFilters v-slot="{items: activeFilters}">
								<span v-if="pagination?.items?.total > 1 || activeFilters?.length" @click="toggleFilters = !toggleFilters" class="btn-toggle-filter" :class="{'wide': pagination?.items?.total <= 1, 'active': toggleFilters}"><BaseCmsLabel code="filter" /></span>
							</BaseCatalogActiveFilters>
							
							<CatalogActiveFilters v-if="tabletBreakpoint" />    
							<div v-if="mobileBreakpoint && pagination?.items?.total > 1" class="c-counter">{{pagination.items.total}} proizvod/a</div>

							<BaseCatalogSpecialFilter filter="with_qty" v-if="products?.length || route.query?.with_qty" />
							<BaseCatalogSpecialFilter filter="discount_base" v-if="products?.length || route.query?.discount_base" />

							<div v-if="pagination?.items?.total > 0" class="c-layout">
								<div class="c-layout-label"><BaseCmsLabel code="layout" /></div>
								<span class="c-layout-grid" :class="{'active': itemsLayout == 'grid'}" @click="changeItemsLayout('grid')">Mreža</span>
								<span class="c-layout-list" :class="{'active': itemsLayout == 'list'}" @click="changeItemsLayout('list')">Lista</span>
							</div>  

							<BaseCatalogSort :sort-options="['new', 'old', 'expensive', 'cheaper', 'az', 'za']" v-slot="{items: sortOptions, selected, onSort}">
								<div v-if="pagination?.items?.total > 1" class="sort c-sort">
									<select class="sort-select" @change="onSort">
										<option :value="sort" v-for="sort in sortOptions" :selected="sort == selected" :key="sort"><BaseCmsLabel :code="'ordering_'+sort" /></option>
									</select>
								</div>
							</BaseCatalogSort>                     
						</div>                  
					</div>               
				</div>

				<div class="display-f wrapper c-wrapper">
					<aside v-if="category?.code != 'kompleti'" :class="['sidebar c-sidebar', toggleFilters && 'active']">
						<div class="sidebar-advisor c-sidebar-advisor" v-html="labels.get('advisor')"></div>
						
						<CatalogActiveFilters v-if="!tabletBreakpoint" />
												
						<BaseCatalogFilters v-slot="{searchFields}">
							<CatalogFilters @close-filters="toggleFilters = false;" :total-products="pagination?.items?.total || 0" :search-fields="searchFields" :content-type="contentType" />
						</BaseCatalogFilters>
					</aside>

					<div class="main-content">
						<div id="items_catalog_layout">
							<template v-if="products.length">
								<div id="items_catalog" class="c-items" :class="[loading && 'loading']">
									<template v-if="category?.code == 'kompleti'">
										<CatalogIndexEntryGroup v-for="product in products" :key="product.id" :item="product" />
									</template>
									<template v-else v-for="product in products" :key="product.id">    
										<CatalogIndexEntryList v-if="!mobileBreakpoint && itemsLayout == 'list'" :item="product" />
										<CatalogIndexEntry v-else :item="product" />
									</template>
									<span v-if="loading" class="loader load-more-loader"><span>učitavam...</span></span>
								</div>
								<ClientOnly>
									<div class="load-more-container c-load-more-container" data-products-scroll-trigger>
										<button v-if="nextPage && !loading" type="button" class="btn btn-yellow btn-secondary load-more btn-load-more" @click="loadMore()"><UiLoader v-if="loading" /><BaseCmsLabel code="load_more_catalog" /></button>
									</div>
								</ClientOnly>
								<BaseUiPagination class="pagination c-pagination-items" />
							</template>
							<template v-else>
								<div class="c-empty"><BaseCmsLabel code="no_products" /></div>
							</template>
						</div>
					</div>
				</div>
			</BaseCatalogItemsLayout>            
		</BaseCatalogProducts>	
	</BaseCatalogCategory>
</template>

<script setup>
	const {scrollTo, onMediaQuery, insertBefore} = useDom();
	const route = useRoute();
	const labels = useLabels();

	const toggleFilters = ref(false);
	const activeDesc = ref(false);

	const {matches: mobileBreakpoint} = onMediaQuery({
		query: '(max-width: 750px)',
	}); 

	const {matches: tabletBreakpoint} = onMediaQuery({
		query: '(max-width: 990px)',
	});     

	function onLoad(products){
		if(products?.items.length){
			sendProductImpressions(products.items, products.contentType == 'search' ? 'search' : 'Proizvodi');
		}
	}


	const contentRef = ref(null);
	const isLongText = ref(false);
	watchEffect(() => {
		if (contentRef.value) {
			const contentText = contentRef.value.textContent;
			const tempElement = document.createElement('div');
			tempElement.style.position = 'absolute';
			tempElement.style.left = '-9999px';
			tempElement.style.visibility = 'hidden';
			tempElement.style.width = `${contentRef.value.offsetWidth}px`;
			tempElement.innerHTML = contentText;
			tempElement.style.fontSize = window.getComputedStyle(contentRef.value).getPropertyValue('font-size');
			tempElement.style.lineHeight = window.getComputedStyle(contentRef.value).getPropertyValue('line-height');
			document.body.appendChild(tempElement);

			const contentHeight = tempElement.offsetHeight;

			isLongText.value = contentHeight > 64;

			document.body.removeChild(tempElement);
		}
	});

</script>

<style lang="less" scoped>
	.c-desc {
		position: relative;
		&.active{
			.c-desc-btn{
				display: inline-flex; position: relative; right: unset; top: unset; display: none;
				&:before{content: none;}
				&>span:after{.rotate(180deg);}
				.more{display: none;}
			}
		}
	}
	.c-desc-content {
		display: -webkit-box; overflow: hidden; text-overflow: ellipsis; -webkit-line-clamp: 2; -webkit-box-orient: vertical; padding: 0; position: relative;
		&:not(.active):deep(p){padding-bottom: 0;}
		&.active{display: block; overflow: initial; text-overflow: unset; -webkit-line-clamp: unset; -webkit-box-orient: unset;}
		@media (max-width: @m){
			-webkit-line-clamp: 1;
		}
	}
	.c-desc-btn{
		display: inline-block; margin: 0; color: var(--textColor); background: var(--white); padding: 0; font-size: 18px; line-height: 24px; font-weight: bold; text-decoration: underline; text-decoration-color: var(--yellow); text-underline-offset: 3px; position: absolute; left: 50%; margin-left: -40px; bottom: 1px; cursor: pointer; z-index: 1; .transition(text-decoration-color);
		@media (min-width: @t){
			&:hover{text-decoration-color: transparent;}
		}
		@media (max-width: @m){
			font-size: 14px; line-height: 20px; left: unset; margin-left: 0; right: 0;
			&:before{.pseudo(auto,auto); background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #ffffff 90%); top: 0; bottom: 0; right: 100%; left: -100px; z-index: -1;}
		}
	}
</style>