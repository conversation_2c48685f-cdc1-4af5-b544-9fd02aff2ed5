<template>
	<BaseCatalogDetail v-slot="{item}" @load="onLoad">
		<div>
			<div class="wrapper cd-wrapper">
				<div class="cd-row cd-row1">
					<div class="cd-header-placeholder"></div>
					<div class="cd-col1">
						<div v-if="item.manufacturer_main_image && item.manufacturer_url_without_domain" class="cd-brand">
							<NuxtLink :to="item.manufacturer_url_without_domain">
								<BaseUiImage :data="item.manufacturer_main_image_thumbs?.['width95-height30']" default="/images/no-image-100.jpg" loading="lazy" :alt="item.manufacturer_title" />
							</NuxtLink>
						</div>

						<div class="cd-badges">
							<template v-if="item?.lists_info">
								<template v-for="(attr, index) in item.lists_info" :key="index">
									<NuxtLink v-if="attr.list_main_image" :to="attr.url_without_domain" class="cp-list-badge cd-list-badge">
										<BaseUiImage loading="lazy" :data="attr.list_main_image_thumbs?.['width60-height200']" default="/images/no-image-50.jpg" :alt="attr.title ? attr.title : ''" />
									</NuxtLink>
								</template>
							</template>

							<template v-if="item.status?.length && item.status == '1' || item.status == '2'">
								<div class="cp-list-badge cd-list-badge cd-badge-status" :class="'cd-badge-status-' + item.status">{{item.status}}</div>
							</template>

							<template v-if="Object.keys(item?.badge_coupons || {}).length">
								<CatalogBadgeCoupon :coupons="item?.badge_coupons" mode="details" />
							</template>

							<div v-if="item.priority_details?.title" class="cp-badge cd-badge new" :class="item.priority_details.title">{{item.priority_details.title}}</div>

							<template v-if="(gift = item.attributes_special?.find(attr => attr.attribute_code === 'poklon'))">
								<div class="cp-badge cd-badge cp-badge-gift cd-badge-gift"><span>{{gift.title}}</span></div>
							</template>
						</div>

						<div id="product_images" class="cd-images" v-interpolation>
							<template v-if="item.images?.length || item.element_certificate">
								<div class="cd-hero-image">
									<BaseUiSwiper class="cd-hero-slider has-slider" :options="{ effect: 'fade', fadeEffect: { crossFade: true }, navigation: { enabled: false }}" :thumbs-swiper="thumbsSwiper" @init="setMainSwiper">
										<BaseUiSwiperSlide v-for="(file, index) in item.images" :key="file.id || index" :class="['cd-hero-slide', index === 0 && 'cd-hero-slide-first']" @mouseenter="activeZoom(file?.file_thumbs?.['width1300-height1300']?.thumb, index)" @mousemove="zoomOnMove($event, index)" @mouseleave="resetZoom">
											<a :href="file.url" class="fancybox" rel="catalog" :data-index="index" :data-thumb="file.file_thumbs?.['width120-height120']?.thumb">
												<span>
													<BaseUiImage :title="file.title" :alt="file.description" :loading="index === 0 ? 'eager' : 'lazy'" :data="file.file_thumbs?.['width650-height650']" default="/images/no-image-650.jpg" :data-zoom-image="file.url" :data-product_main_image="index === 0 ? 1 : null" />
													<div v-if="zoomIndex === index && zoom" id="zoom-container" :style="zoomedImageStyle"></div>
												</span>
											</a>
										</BaseUiSwiperSlide>

										<BaseUiSwiperSlide v-if="item.element_certificate" class="cd-hero-slide cd-hero-slide-cert">
											<a :href="absoluteImagePath(imageFromString(item.element_certificate))" class="fancybox" rel="cert">
												<span><BaseUiImage :src="imageFromString(item.element_certificate)" default="/images/no-image-100.jpg" /></span>
											</a>											
										</BaseUiSwiperSlide>
									</BaseUiSwiper>
								</div>

								<ClientOnly>
									<div v-if="item.images?.length > 1 || item.element_certificate" class="fz0 cd-thumbs" :class="{'has-arrows': item.images?.length > 5 || (item.images?.length > 4 && item.element_certificate)}">
										<BaseUiSwiper
											class="cd-thumbs-slider"
											:class="{'has-slider': item.images?.length > 1}"
											:options="{slidesPerView: 5, slidesPerGroup: 5, watchSlidesProgress: true, navigation: {enabled: true, nextEl: '.thumb-swiper-button-next', prevEl: '.thumb-swiper-button-prev'}}"
											@init="setThumbsSwiper">
											<BaseUiSwiperSlide v-for="thumb in item.images" :key="thumb.id" class="cd-thumb">
												<span><BaseUiImage loading="lazy" :data="thumb.file_thumbs?.['width100-height100']" default="/images/no-image-100.jpg" /></span>
											</BaseUiSwiperSlide>
											<BaseUiSwiperSlide v-if="item.element_certificate" class="cd-thumb">												
												<span><BaseUiImage :src="imageFromString(item.element_certificate)" default="/images/no-image-100.jpg" /></span>										
											</BaseUiSwiperSlide>
										</BaseUiSwiper>
										<div class="thumb-swipper-navigation">
											<div class="thumb-swiper-button thumb-swiper-button-prev swiper-button-prev">Prev</div>
											<div class="thumb-swiper-button thumb-swiper-button-next swiper-button-next">Next</div>
										</div>
									</div>
								</ClientOnly>
							</template>
							<template v-else>
								<img src="/images/no-image-435.jpg" alt="" data-product_main_image="1" />
							</template>
						</div>
					</div>
					<div class="cd-col2">
						<!-- FIXME photopay -->
						<div class="cd-header">
							<CmsBreadcrumbs v-if="item?.breadcrumbs" :items="item.breadcrumbs" />

							<template v-if="item?.discount_expire">
								<BaseUiCountdown :end="item.discount_expire" v-slot="{days, hours, minutes, seconds, ended}">
									<div class="cd-action-cnt" v-if="!ended">
										<div class="cd-discount-countdown">
											<BaseCmsLabel code="discount_countdown" tag="span"/>
												<div class="discount-timer" v-if="!ended">
													<div v-if="days && days > 1" class="days">
														<span>{{ days }}</span>d
													</div>
													<div class="hour">
														<span>{{ hours }}</span>h
													</div>
													<div class="min">
														<span>{{ minutes }}</span>m
													</div>
													<div class="sec">
														<span>{{ seconds }}</span>s
													</div>
												</div>
										</div>
										
									</div>
								</BaseUiCountdown>
							</template>
							<div class="cd-code">
								<div><span>{{item.code}}</span></div>
							</div>
							<h1 v-if="item.seo_h1" class="cd-title">{{item.seo_h1}}</h1>

							<template v-if="item?.feedback_rate_widget">
								<FeedbackRates :rates="item.feedback_rate_widget.rates"/>
							</template>

							<template v-if="item?.feedback_comment_widget && item.feedback_comment_widget.comments_status != 1">
								<span class="cd-comments-info">
									<div class="btn-comments" @click="scrollTo('#comments', {offset: 80})">
										<BaseCmsLabel code="comments" class="label" tag="span"/>
										({{item.feedback_comment_widget.comments ? item.feedback_comment_widget.comments : 0}})
									</div>
								</span>
							</template>
						</div>

						<div v-if="item?.type == 'groupproduct' && item?.groupproducts_ids" class="cd-groupp-cnt">
							<ClientOnly>
								<BaseCatalogProductsWidget :fetch="{id: item.groupproducts_ids.split(',').filter(id => id.trim() !== ''), limit: 20}" v-slot="{items: groupProducts}">
									<div v-if="groupProducts?.length" class="cd-groupp-title"><BaseCmsLabel code="group_product_title" /></div>
									<div v-if="groupProducts?.length" class="cd-groupp-items">
										<div v-for="item in groupProducts" :key="item.id" class="cd-groupp-item">
											<NuxtLink :to="item.url_without_domain">{{item.title}}</NuxtLink>											
										</div>
									</div>							
								</BaseCatalogProductsWidget>
							</ClientOnly>
						</div>

						<template v-if="!item.disable_added_to_cart">
							<template v-if="item.price_custom > 0">
								<div class="cd-price-container">			
									<div class="cd-price-container-row" :class="[!item.attributes_special?.find(attr => attr.attribute_code === 'energetski-razred') && 'no-energy-badge']">
										<div class="cd-price cd-price-first">
											<template v-if="item.discount_percent_base > 0 || item.price_custom < basicPriceBase">
												<div class="cd-current-price">
													<span v-if="basicPriceBase"><BaseUtilsFormatCurrency :wrap="true" :price="basicPriceBase" /></span>
												</div>
											</template>
											<template v-else>
												<div class="cd-current-price">
													<span><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></span>
												</div>
											</template>
										</div>

										<CatalogEnergyBadge :item="item" modeClass="cd-attr-energy" @clickedEnergy="moveToEnergy();" />

										<div v-if="item?.installments_info?.items?.length" class="cd-payment" ref="installmentsCnt">
											<div @click="installmentsTooltip = !installmentsTooltip" :class="['btn-installments-calc', 'btn-installments-calc1', installmentsTooltip && 'active']"><span><BaseCmsLabel code="installments_calc" default="Izračun rata"/></span></div>
											<div :class="['cd-payment-installments', 'cd-payment-installments1', installmentsTooltip && 'active']">
												<span @click="installmentsTooltip = false" class="cd-payment-installments-close"></span>
												<div v-if="labels.get('monthly_payment_cards')" class="cd-payment-installments-cards" v-html="labels.get('monthly_payment_cards')"></div>
												
												<template v-if="item.installments_info.items[item.installments_info.items.length - 1]?.per_month">
													<div v-for="(installment, index) in item.installments_info.items" :key="index" class="cd-installment-row">
														<span class="label">
															Kartice 
															<template v-if="Number(installment.max) > 1">
																{{installment.min}}<template v-if="Number(installment.min) < Number(installment.max)">-{{installment.max}}</template> rate:
															</template>
															<template v-else>
																jednokratno
															</template>
														</span>

														<span class="value">
															<BaseUtilsFormatCurrency :price="installment.total" />
															<template v-if="Number(installment.max > 1)"> - već od <BaseUtilsFormatCurrency :price="installment.per_month" />/mj</template>
														</span>													
													</div>
												</template>											
											</div>
										</div>

										<div class="cd-placeholder-mobile"></div>

										<div v-if="item?.type == 'groupproduct' && item?.groupproducts_ids" class="cd-save-cnt" ref="savePrice">
											<div v-if="item?.price_custom < item.basic_price_base" @click="savePriceTooltip = !savePriceTooltip" :class="['btn-save-price', savePriceTooltip && 'active']"><span><BaseCmsLabel code="price_save" default="Štedite:"/> <BaseUtilsFormatCurrency :price="item.basic_price_base - item.price_custom" /></span></div>
											<div :class="['cd-save-price-tooltip', savePriceTooltip && 'active']">
												<span @click="savePriceTooltip = false" class="cd-save-price-close"></span>
												<div v-if="labels.get('save_price_tooltip')" class="save-price-tooltip-content" v-html="labels.get('save_price_tooltip')"></div>
											</div>
										</div>

										<div v-if="item?.type != 'groupproduct'" class="cd-photopay-row" ref="photoPayCnt">
											<div class="cd-photopay cd-tooltip-label" :class="[photoPayTooltip && 'active']">
												<div class="cd-photopay-cnt" @click="modal.open('photopayModal'), photoPayTooltip = !photoPayTooltip">
													<BaseCmsLabel code="photo_pay" class="cd-photopay-title" tag="span" />
													<div class="cd-photopay-desc">
														<BaseCmsLabel code="photo_pay_description" tag="span" />
														<span class="info-icon-photopay"></span>
													</div>
												</div>
												<div class="cd-photopay-tooltip cd-tooltip loggedin">
													<div class="cd-tooltip-close" @click="photoPayTooltip = false"></div>
													<BaseCmsLabel v-if="user?.id" code="photopay_tooltip_loggedin" tag="div" />
													<BaseCmsLabel v-else code="photopay_tooltip_notloggedin" tag="div" />
												</div>
											</div>

											<CatalogPhotopayModal v-if="user?.id" />
										</div>
									</div>

						
									<div v-if="item.discount_percent_base > 0 || item.price_custom < basicPriceBase" class="cd-lowest-price-cnt">
										<span class="cd-badge-discount">-{{Math.round(item?.discount_percent_base ?? 0)}}%</span>
										<div class="cd-lowest-price-price-group">
											<div class="cd-price-value cd-lowest-price red">
												<span><BaseUtilsFormatCurrency :wrap="true" :price="item.price_custom" /></span>
											</div>
											<div class="cd-lowest-price-note">
												<span v-html="labels.get('cash_price')"></span>
												<span v-if="item.extra_price_lowest && item.extra_price_lowest > 0" class="cd-extra-price-lowest">
													<BaseCmsLabel code="extra_price_lowest" />: 
													<strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
												</span>
											</div>

											<ClientOnly>
												<template v-if="item?.extra_price_lowest_data?.price && item?.extra_price_lowest_data?.lowest_price_source == 'coupons_used' && item?.extra_price_lowest_data?.lowest_price_info?.coupon_code">
													<div class="cd-lowest-coupon-price">
														<span><BaseUtilsFormatCurrency :wrap="true" :price="item.extra_price_lowest_data.price" /></span>
													</div>
													<BaseCmsLabel class="cd-lowest-coupon-price-desc" tag="div" :replace="[{'%DISCOUNT%': Math.round((item.price_custom - item.extra_price_lowest_data.price) / item.price_custom * 100)}, {'%COUPON%': item.extra_price_lowest_data.lowest_price_info.coupon_code}]" code="coupon_discount_price" />
												</template>
											</ClientOnly>
										</div>
									</div>
									<template v-else>
										<ClientOnly>
											<div v-if="item.extra_price_lowest && item.extra_price_lowest > 0" class="cd-lowest-price-note no-discount">	
												<span class="cd-extra-price-lowest">
													<BaseCmsLabel code="extra_price_lowest" />:
													<strong><BaseUtilsFormatCurrency :price="item.extra_price_lowest" /></strong>
												</span>
											</div>

											<div v-if="item?.extra_price_lowest_data?.price && item?.extra_price_lowest_data?.lowest_price_source == 'coupons_used' && item?.extra_price_lowest_data?.lowest_price_info?.coupon_code" class="cd-lowest-coupon-price-cnt">
												<div class="cd-lowest-coupon-price">
													<span><BaseUtilsFormatCurrency :wrap="true" :price="item.extra_price_lowest_data.price" /></span>
												</div>
												<BaseCmsLabel class="cd-lowest-coupon-price-desc" tag="div" :replace="[{'%DISCOUNT%': Math.round((item.price_custom - item.extra_price_lowest_data.price) / item.price_custom * 100)}, {'%COUPON%': item.extra_price_lowest_data.lowest_price_info.coupon_code}]" code="coupon_discount_price" />
											</div>
										</ClientOnly>
									</template>

									<div class="cd-placeholder"></div>

									<template v-if="Object.keys(item?.badge_coupons || {}).length">
										<template v-for="(coupon, id) in item?.badge_coupons" :key="id">
											<div v-if="coupon.description && coupon.color_text && coupon.color_bg" class="cd-badge-coupon-text" :style="{'background-color': coupon.color_bg, 'color': coupon.color_text}"><BaseCmsLabel code="badge_coupon_text" tag="span" /> <strong>{{coupon.description}}</strong></div>
											<div v-else-if="coupon.description" class="cd-badge-coupon-text"><BaseCmsLabel code="badge_coupon_text" /> <strong>{{coupon.description}}</strong></div>
										</template>
									</template>
									

									<template v-if="item.status?.length && item.status == '1' || item.status == '2'">
										<div class="cd-badge-status-container">
											<div class="cd-badge-status-image" :class="'cd-badge-status-' + item.status">{{item.status}}</div>
											<div class="cd-badge-status-desc" v-html="labels.get('badge_status_desc_'+item.status)"></div>
										</div>
									</template>

									<div class="cd-qty-shipping-price" v-interpolation>
										<div v-if="item?.qty_visible && item.qty_visible == 1 && item.available_qty > 0" class="cd-available-qty"><BaseCmsLabel code="available_qty" /> <span>{{ Math.round(item.available_qty) }} <BaseCmsLabel code="unit" /></span></div>
										
										<BaseCmsLabel v-if="item.exclude_delivery != 0" code="product_free_shipping" class="cd-shipping-price cd-shipping-price-free" tag="div" />
										<div v-else class="cd-shipping-price cd-shipping-price-paid" v-html="labels.get('product_shipping_price').replace('%PRICE%', formatCurrency(item.shipping_price))"></div>
									</div>
								</div>

								<ClientOnly>
									<BaseCatalogProductsWidget  v-if="item.is_available" :fetch="{
										limit: 10, 
										advanced_filters: {
											id: item.id, 
											category_id: item.category_id, 
											manufacturer_id: item.manufacturer_id,
											attributes_ids: item.attributes_ids, 
											price: item.price_custom
										},
										warranty_main_item_price: {
											basic_price: item.basic_price_custom, 
											price: item.price_custom
										},
										warranty_product_id: item.id, 
										only_available: false, 
										sort: 'older'}" 
										v-slot="{items: warrantyProducts}">

										<CatalogWarranty v-if="warrantyProducts.length" :warrantyProducts="warrantyProducts" mode="detail" @warrantyChanged="selectedWarrantyCode = $event" />	
									</BaseCatalogProductsWidget>						
								</ClientOnly>
							</template>
							<ClientOnly>
								<template v-if="item.available_qty <= 0">
									<FeedbackNotificationForm status="not-available" :item="item" />
								</template>
								<template v-else-if="item.status == '3'">
									<div class="cd-inquiry">
										<BaseCmsLabel code="not_available" class="cd-inquiry-title" tag="div" />
										<BaseFormSiteForm code="contact" class="form-label inquiry-form form-animated-label" v-slot="{fields, loading, status}">
											<template v-if="!status?.success">
												<BaseFormField v-for="field in fields" :key="field.name" :item="field" v-slot="{errorMessage, floatingLabel, required}">
													<template v-if="field.name == 'subject'">
														<BaseFormInput type="hidden" :value="'Upit o proizvodu ' + item.title + ', url: ' + item.url" />
													</template>
													<p v-else class="field" :class="['field-' + field.name, {'ffl-floated': floatingLabel}]">
														<BaseFormInput :id="field.name" />
														<label :for="field.name"><BaseCmsLabel :code="'f_inquiry_' + field.name" /> <BaseCmsLabel v-if="!required" code="not_mandatory" class="not-mandatory" tag="span" /></label>
														<span class="field_error error" v-show="errorMessage" v-html="errorMessage" />
													</p>
												</BaseFormField>
												<button :disabled="loading" type="submit" class="btn btn-yellow btn-cd-inquiry" :class="{'loading': loading}"><UiLoader v-if="loading" /><BaseCmsLabel code="inquiry_send" tag="span" /></button>
											</template>
											<BaseCmsLabel v-show="status?.success" code="inquiry_success" class="cd-inquiry-success" tag="div" />
										</BaseFormSiteForm>
									</div>
								</template>
							</ClientOnly>

							<div class="cd-btns">
								<CatalogSetWishlist :item="item" mode="detail" />

								<CatalogSetCompare :item="item" mode="detail" />

								<BaseWebshopAddToCart v-if="item.is_available && item.available_qty > 0 && item.status != '3'" v-slot="{onAddToCart, loading}" :data="customAddToCartData">
									<div class="add-to-cart-container " :class="item.available_qty == 1 && 'cd-qty-limit'">
										<button type='button' :class="['btn cp-list-btn-addtocart cd-btn-add']" @click="onAddToCart"><UiLoader v-if="loading" /><span v-html="labels.get('add_to_shopping_cart')"></span></button>
									</div>
								</BaseWebshopAddToCart>
							</div>
						</template>
						<div v-else class="product-eol-cnt">
							<BaseCmsLabel code="product_eol_title" class="product-eol-title" tag="div" />
							<NuxtLink :to="item.category_url_without_domain" class="btn product-eol-btn"><BaseCmsLabel code="product_eol_btn" /></NuxtLink>
						</div>

						<ul class="cp-list-attrs cd-attrs" v-if="item?.attributes_special">
							<template v-for="(attribute_special, index) in item.attributes_special" :key="attribute_special.id">
								<template v-if="attribute_special.attribute_code != 'posebne_oznake' || attribute_special.attribute_code != 'poklon'">
									<template v-if="index < 4">
										<li class="cp-list-attr" :class="'cp-list-attr-' + attribute_special.code">
											<div>
												<div class="cp-list-attr-image">
													<span v-if="attribute_special.attribute_code == 'energetski-razred'" @click="moveToEnergy();" class="btn-attr-energy">
														<BaseUiImage loading="lazy" :src="attribute_special.image" default="/images/no-image-50.jpg" width="60" height="60" />
													</span>
													<BaseUiImage v-else loading="lazy" :src="attribute_special.attribute_image" default="/images/no-image-50.jpg" width="50" height="50" />
												</div>
												<template v-if="attribute_special.attribute_code == 'energetski-razred'">
													<div v-if="(energy_doc = item.documents?.find(doc => doc.title === 'Informacijski list'))" class="cp-list-attr-title">
														<a v-if="energy_doc?.url" target="_blank" :href="energy_doc.url">{{energy_doc.title}}</a>
													</div>
												</template>
												<template v-else>
													<div class="cp-list-attr-title">{{attribute_special.attribute_title}}</div>
													<div class="cp-list-attr-value">{{attribute_special.title}}</div>
												</template>												
											</div>
										</li>
									</template>
								</template>
							</template>
						</ul>

						<div v-if="item.short_description" class="cd-short-description" v-html="item.short_description"></div>

						<ClientOnly>
							<BaseCatalogProductsWidget :fetch="{related_code: 'related_color', related_item_id: item.id}" v-slot="{items: colorRelatedItems}">
								<div class="cd-related-container" v-if="colorRelatedItems?.length">
									<div class="cd-related-color-title"><BaseCmsLabel code="related_products" default="Proizvod je dostupan i u ovim bojama" />:</div>								
									<div class="cd-related-color-products">
										<NuxtLink class="cd-related-color-item" :to="relatedItem.url_without_domain" v-for="relatedItem in colorRelatedItems" :key="relatedItem.id">
											<span>
												<BaseUiImage v-if="relatedItem.main_image_2_thumbs?.['width100-height100']?.thumb" :data="relatedItem.main_image_2_thumbs?.['width100-height100']" default="/images/no-image-50.jpg" :title="relatedItem.title" :alt="relatedItem.description ? relatedItem.description : relatedItem.title" loading="lazy" />
												<BaseUiImage v-else :data="relatedItem.main_image_thumbs?.['width100-height100']" default="/images/no-image-50.jpg" :title="relatedItem.title" :alt="relatedItem.description ? relatedItem.description : relatedItem.title" loading="lazy" />
											</span>
										</NuxtLink>
									</div>
								</div>
							</BaseCatalogProductsWidget>

							<BaseCatalogProductsWidget :fetch="{related_code: 'related_size', related_item_id: item.id, limit: 0, id_exclude: item.id}" v-slot="{items: sizeRelatedItems}">
								<div class="cd-related-container" v-if="sizeRelatedItems?.length">
									<div class="cd-related-color-title"><BaseCmsLabel code="related_size_products" default="Proizvod je dostupan i u ovim dijagonalama" />:</div>
									<div class="cd-related-color-products">
										<NuxtLink class="cd-related-color-item cd-related-size-item" :to="relatedItem.url_without_domain" v-for="relatedItem in sizeRelatedItems" :key="relatedItem.id">
											<span>
												<BaseUiImage :data="relatedItem.main_image_thumbs?.['width100-height100']" default="/images/no-image-50.jpg" :title="relatedItem.title" :alt="relatedItem.description ? relatedItem.description : relatedItem.title" loading="lazy" />
												<template v-if="relatedItem.attributes_special?.length">
													<span v-for="attr in relatedItem.attributes_special.filter(attr => attr.attribute_code != 'energetski-razred').slice(0, 1)" :key="attr.id" class="cd-related-size-title">{{attr.title}}</span>												
												</template>
											</span>
										</NuxtLink>
									</div>
								</div>
							</BaseCatalogProductsWidget>
						</ClientOnly>
					</div>
				</div>
				<div class="cd-row cd-row2">
					<div class="cd-col3">
						<CatalogDetailContent :item="item" />

						<ClientOnly>
							<CmsShare mode="cd-share" />
						</ClientOnly>
					</div>

					<div class="cd-col4">
						<BaseCmsRotator :fetch="{code: 'benefits', limit: 2, response_fields: ['id','link','url_without_domain','title','link2', 'title2']}" v-slot="{items}">
							<div class="cd-benefits-wrapper" v-if="items?.length">
								<div class="cd-benefits">
									<div class="cd-benefit-item" v-for="item in items" :key="item.id">
										<NuxtLink :to="item.url_without_domain" class="cd-benefit" :class="item.link2">
											{{item.title}}
											<span v-if="item.title2">{{item.title2}}</span>
										</NuxtLink>
									</div>
								</div>
							</div>
						</BaseCmsRotator>

						<ClientOnly>
							<BaseCatalogProductsWidget :fetch="{related_code: 'related', related_item_id: item.id, limit: 10, always_to_limit_strict_rules: true}" v-slot="{items: relatedProducts}"  @loadProductsWidget="(products) => onLoadedProducts(products, 'Povezani proizvodi')">
								<div class="cd-related-products" v-if="relatedProducts?.length">
									<BaseCmsLabel code="related_products" class="cd-related-title" default="Povezani proizvodi" tag="div" />
									<div class="cd-related-slider">
										<CatalogSpeciallists :items="relatedProducts" mode="cdRelated" />
									</div>
								</div>
							</BaseCatalogProductsWidget>
						</ClientOnly>
					</div>
				</div>
			</div>

			<ClientOnly>
				<BaseCatalogProductsWidget :fetch="{related_code: 'upsale', related_item_id: item.id, limit: 10}" v-slot="{items: upsaleProducts}"  @loadProductsWidget="(products) => onLoadedProducts(products, 'Proizvodi')">
					<div class="cd-upsale" v-if="upsaleProducts?.length">
						<div class="wrapper cd-upsale-wrapper">
							<BaseCmsLabel code="upsale_title" class="cd-upsale-title" tag="div" />
							<div :class="['cd-upsale-slider', upsaleProducts?.length < 4 && 'centered']">
								<CatalogSpeciallists :items="upsaleProducts" />
							</div>
						</div>
					</div>
				</BaseCatalogProductsWidget>
			</ClientOnly>

			<div class="brands-wrapper">
				<CatalogBrands />
			</div>
		</div>
	</BaseCatalogDetail>
</template>

<script setup>
	const {scrollTo, onClickOutside, onMediaQuery, appendTo, insertAfter} = useDom();
	const labels = useLabels();
	const {formatCurrency} = useCurrency();
	const productDetails = ref(null);
	const {absoluteImagePath} = useText();
	const route = useRoute();
	const energySlideIndex = ref(null);
	const modal = useModal();

	const auth = useAuth();
	const user = computed(() => auth.getUser());
	
	const selectedWarrantyCode = ref('');

	const customAddToCartData = computed(() => {
		if(!productDetails.value) return {};
		
		if(selectedWarrantyCode.value){
			return [{modalData: productDetails.value, shopping_cart_code: productDetails.value.shopping_cart_code}, {modalData: {warranty: true}, shopping_cart_code: selectedWarrantyCode.value}]
		}
		return {modalData: productDetails.value, shopping_cart_code: productDetails.value.shopping_cart_code}		
	});

	const mainSwiper = ref(null);
	const setMainSwiper = (swiper) => {
		mainSwiper.value = swiper;
		energySlideIndex.value = productDetails.value.images.length;
		if(route?.query?.mode == 'energy' && energySlideIndex.value){
			mainSwiper.value.slideTo(energySlideIndex.value);
		}
	};	

	const thumbsSwiper = ref(null);
	const setThumbsSwiper = (swiper) => {
		thumbsSwiper.value = swiper;
	};

	const installmentsTooltip = ref(false);
	const installmentsCnt = ref();
	const savePriceTooltip = ref(false);
	const savePrice = ref();
	const photoPayTooltip = ref(false);
	const photoPayCnt = ref();
	
	onClickOutside(installmentsCnt, () => {
		installmentsTooltip.value = false;
	});
	onClickOutside(savePrice, () => {
		savePriceTooltip.value = false;
	});
	onClickOutside(photoPayCnt, () => {
		photoPayTooltip.value = false;
	});

	const basicPriceBase = computed(() => {
		if(productDetails.value?.installments_info?.max_price){
			return productDetails.value?.installments_info?.max_price;
		}
		return productDetails.value?.basic_price_base
		
	})

	function onLoad(data) {
		if(data?.item){
			productDetails.value = data.item;
		}		
	}	

	function imageFromString(string) {
		const pattern = /<img[^>]+src[\s='"]+([^"'>\s]+)/i;
		string = string || '';

		const match = string.match(pattern);

		return match && match[1] ? match[1] : 'no-results.jpg';
	}

	function moveToEnergy(){
		mainSwiper.value.slideTo(energySlideIndex.value);
		scrollTo('.cd-hero-image');
	}

	const {matches: tabletBreakpoint} = onMediaQuery({
		query: '(max-width: 1250px)',
	});  


	//zoom on hover
	let zoom = ref(false);
	let zoomImage = ref(null);
	let zoomIndex = ref(null);
	let zoomScale = ref(1);
	let zoomedImageStyle = ref('');
	const config = useAppConfig();

	function activeZoom(image, index) {
		if(!tabletBreakpoint.value) {
			zoom.value = true;
			zoomImage.value = config.host + image;
			zoomIndex.value = index;
		}
	}
	function zoomOnMove(event, index) {
		if(zoom.value == true && zoomIndex.value === index && zoomImage.value) {
			const container = event.currentTarget;
			if(container) {
				const { left, top, width, height } = container.getBoundingClientRect();
				const mouseX = event.clientX - left;
				const mouseY = event.clientY - top;

				zoomScale.value = 1.4;
				const backgroundImage = `white url("${zoomImage.value}")`;
				zoomedImageStyle.value = `
					background: ${backgroundImage};
					transform: scale(${zoomScale.value});
					transform-origin: ${(mouseX / width) * 100}% ${(mouseY / height) * 100}%;
				`;
			}
		}
	};
	function resetZoom() {
		zoom.value = false;
		zoomImage.value = null;
		zoomIndex.value = null;
		zoomScale.value = 1;
		zoomedImageStyle.value = '';
	};

	onMediaQuery({
		query: '(max-width: 1250px)',
		enter: () => {
			insertAfter('.cd-photopay-row', '.cd-placeholder');
		},
	});
	onMediaQuery({
		query: '(max-width: 750px)',
		enter: () => {
			appendTo('.cd-header', '.cd-header-placeholder');
			insertAfter('.cd-share', '.cd-benefits-wrapper');
			insertAfter('.cd-photopay-row', '.cd-placeholder-mobile');
		},
	});

	function onLoadedProducts(products, type){
		if(products?.items.length){
			sendProductImpressions(products.items, type);
		}
	}
</script>

<style lang="less" scoped>
	.cd-thumbs{
		position: relative;
		:deep(.swiper-navigation){display: none!important;}
		&:not(.has-arrows){
			:deep(.thumb-swipper-navigation){display: none;}
			:deep(.swiper-wrapper){justify-content: center;}
			.swiper-wrapper .cd-thumb:first-child{border-left: 1px solid var(--borderColor);}
		}		
	}
	.thumb-swipper-navigation{height: 100%; width: 100%; position: absolute; top: 0; display: flex; justify-content: space-between;}
	.thumb-swiper-button{
		width: 35px; height: 100%; top: 0; border: 1px solid var(--borderColor); display: flex; align-items: center; justify-content: center; font-size: 0; z-index: 1; cursor: pointer;
		&:before{.icon-arrow-right; font: 16px/1 var(--fonti); color: darken(#E0E2DB,10%); display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; .transition(color);}
		@media (min-width: @t){
			&:hover{
				&:before{color: var(--yellow);}
			}
		}
		@media (max-width: @m){width: 28px;}
	}
	.thumb-swiper-button-prev{
		left: 0;
		&:before{.rotate(180deg)}
	}
	.thumb-swiper-button-next{right: 0; margin-right: 1px;}
	:deep(.swiper-button-disabled){
		cursor: default;
		&:before{color: darken(#E0E2DB,10%)!important;}
	}
	:deep(.cd-thumb.swiper-slide-thumb-active){box-shadow: inset 0px 0px 0px 2px var(--yellow); border-color: var(--yellow); z-index: 2; position: relative;}

	.inquiry-form{
		.field-phone{display: none;}
	}

	//Discount countdown
	.cd-action-cnt{
		display: flex; align-items: center; position: relative; margin: 6px 0 15px;
		@media (max-width: @tp){display: block; margin: 2px 0 6px;}
	}
	.cd-discount-countdown{
		display: flex; align-items: center; padding: 8px 14px 8px 42px; background: var(--red); color: var(--white); font-size: 14px; line-height: 1.2; font-weight: bold;
		&:before{.icon-calendar(); font: 18px/1 var(--fonti); color: var(--white); position: absolute; left: 14px; top: 7px;}
		@media (max-width: @tp){display: inline-flex;}
	}
	.discount-timer{
		font-size: 14px; line-height: 1.2; color: var(--white); display: flex; flex: 0 0 auto;
		span{display: inline-block; text-align: right;}
		.sec span{width: 14px;}
		&>div{margin-left: 4px;}
	}

	//STATUS BADGES
	.cd-badge-status{
		width: 60px; height: 60px!important; font-size: 0;
		@media (max-width: @t){width: 45px; height: 45px!important;}
	}
	.cd-badge-status-1{background: url(assets/images/status1.png) no-repeat left top; background-size: contain;}
	.cd-badge-status-2{background: url(assets/images/status2.png) no-repeat left top; background-size: contain;}
	.cd-badge-status-container{
		display: flex; align-items: center; justify-content: flex-start; margin-top: 30px;
		@media (max-width: @t){margin-top: 25px;}
		@media (max-width: @tp){margin-top: 15px;}
	}
	.cd-badge-status-image{
		width: 50px; height: 50px; flex: 0 0 50px; font-size: 0;
		@media (max-width: @tp){width: 48px; height: 48px; flex-basis: 48px;}
	}
	.cd-badge-status-desc{
		font-size: 14px; line-height: 17px; position: relative; margin-left: 9px;
		:deep(span){display: block; font-size: 11px; line-height: 13px; color: #939393;}
		@media (max-width: @tp){
			margin-left: 10px;
			:deep(span){font-size: 12px; line-height: 14px;}
		}
	}

	//EOL
	.product-eol-cnt{
		position: relative; display: block; margin: 24px 0 40px;
		@media (max-width: @t){margin: 18px 0 32px;}
		@media (max-width: @m){margin: 24px 0;}
	}
	.product-eol-title{
		font-size: 30px; line-height: 1.1; font-weight: bold; color: var(--red); padding-bottom: 16px;
		@media (max-width: @t){font-size: 24px; padding-bottom: 12px;}
		@media (max-width: @m){font-size: 20px; line-height: 1.2;}
	}
	.product-eol-btn{
		width: 100%; height: 65px;
		@media (max-width: @t){height: 55px;}
		@media (max-width: @m){height: 48px;}
	}

	//group
	.cd-groupp-cnt{
		padding: 8px 0 4px;
		@media (max-width: @m){padding: 0 0 5px;}
	}
	.cd-groupp-title{
		font-size: 18px; line-height: 28px; font-weight: bold; padding-bottom: 5px;
		@media (max-width: @m){font-size: 14px; line-height: 20px;}
	}
	.cd-groupp-item{
		font-size: 18px; line-height: 28px; font-weight: normal; margin-bottom: 5px;
		@media (max-width: @m){font-size: 14px; line-height: 20px;}
		a{
			display: inline-block;
			@media (min-width: @h){
				&:hover{text-decoration: none;}
			}
		}
	}

	//save price
	.cd-save-cnt{
		font-size: 13px; line-height: 17px; position: relative; z-index: 100;
		@media (max-width: @m){height: 45px;}
	}
	.btn-save-price{
		border: 1px solid var(--borderColor); height: 45px; display: inline-flex; align-items: center; justify-content: center; padding: 0 23px; text-decoration: none; font-size: 15px; color: var(--red); font-weight: bold; .transition(all); width: 100%; cursor: pointer;
		&:hover, &.active{text-decoration: none; border-color: var(--gray);}
		span{
			display: block; position: relative; padding-right: 20px;
			&:after{.icon-info-btn(); font: 14px/1 var(--fonti); color: var(--textColor); position: absolute; right: 0; top: 1px;}
		}
		@media (max-width: @tp){
			font-size: 14px; padding: 0 11px; height: 40px;
			span{
				padding-right: 18px;
				&:after{font-size: 13px; }
			}
		}
	}
	.cd-save-price-close{
		width: 20px; height: 20px; position: absolute; right: 10px; top: 10px; display: flex; justify-content: center; align-items: center; text-decoration: none; cursor: pointer; display: none;
		&:before{.icon-close; font: 14px/1 var(--fonti); color: var(--red); font-weight: bold;}
		&:hover{text-decoration: none;}
		@media (max-width: @tp){display: flex;}
	}
	.cd-save-price-tooltip{
		position: absolute; background: #fff; padding: 15px 20px; box-shadow: 0 2px 15px rgba(0,0,0,.2); left: 0; top: 53px; width: 320px; text-align: left; display: none;
		&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 121px; top: -3px;}
		&.active{display: block;}
		@media (max-width: 1300px){
			left: -40px;
			&:before{left: 160px;}
		}
		@media (max-width: @t){
			left: -115px;
			&:before{left: unset; right: 75px;}
		}
		@media (max-width: @tp){
			top: 50px;
			&:before{right: 92px;}
		}
		@media (max-width: @m){
			left: -128px; box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.15);
			&:before{right: 82px;}
		}
	}
	.save-price-tooltip-content{
		@media (max-width: @tp){padding-right: 10px;}
	}

	//COUPON cd-col2
	.cd-badge-coupon-text{
		background-color: var(--yellow); color: var(--textColor); font-size: 14px; line-height: 20px; font-weight: normal; height: 33px; padding: 0 13px 0 41px; width: auto; display: inline-flex; align-items: center; justify-content: center; white-space: nowrap; margin-top: 24px; position: relative;
		&:before{.icon-discount(); font: 21px/1 var(--fonti); position: absolute; left: 13px; color: var(--textColor); top: 5px;}
		@media (max-width: @t){margin-top: 20px;}
		@media (max-width: @m){margin-top: 14px;}
	}


	//PHOTOPAY
	.cd-photopay-row{
		align-self: baseline; position: relative; margin-left: auto;
		@media(max-width: @t){max-width: unset; margin: 20px 0 0;}
		@media(max-width: @m){width: 100%; max-width: 100%; margin: 0;}
	}
	.cd-photopay{
		width: 100%;
		@media (min-width: @t){
			&:hover{
				.cd-photopay-tooltip{max-height: unset; overflow: visible; visibility: visible; opacity: 1;}
				.cd-photopay-cnt{
					&:after{opacity: 1;}
				}
			}
		}
		@media (max-width: @t){
			display: inline-block; width: auto; position: relative;
			&.active{
				.cd-photopay-tooltip{max-height: unset; overflow: visible; visibility: visible; opacity: 1;}
				.cd-photopay-cnt{
					&:after{opacity: 1;}
				}
			}
		}
		@media (max-width: @m){display: block; width: 100%;}
	}
	.cd-photopay-cnt{
		display: block; width: 100%; color: var(--textColor); padding: 15px 18px 16px 54px; background: var(--yellow); position: relative; cursor: pointer;
		&:before{.icon-barcode(); font: 23px/1 var(--fonti); color: var(--textColor); position: absolute; left: 20px; top: 50%; transform: translateY(-50%); z-index: 1;}
		&:after{.pseudo(100%,100%); left: 0; top: 0; background: rgba(49, 49, 49, 0.3); right: 0; bottom: 0; opacity: 0; z-index: 0; transition: opacity .3s, background .3s;}
		@media (max-width: 1300px){
			padding-left: 33px; padding-right: 5px;
			&:before{font-size: 20px; left: 9px;}
		}
		@media (max-width: @t){
			padding-left: 54px; padding-right: 18px;
			&:before{font-size: 23px; left: 20px;}
		}
		@media (max-width: @m){
			padding-left: 61px; padding-right: 15px;
			&:before{font-size: 24px; left: 20px;}
		}
	}
	.cd-photopay-title{
		display: block; font-size: 15px; line-height: 18px; font-weight: bold; text-transform: uppercase; z-index: 2; position: relative;
		@media(max-width: @m){font-size: 14px; line-height: 17px;}
	}
	.cd-photopay-desc{
		display: block; font-size: 12px; line-height: 14px; padding-top: 0; position: relative; z-index: 2;
		.info-icon-photopay{
			display: inline-block; width: 14px; height: 14px; flex-shrink: 0; position: relative; margin-left: 4px; top: 2px;
			&:before{.icon-question(); font: 14px/1 var(--fonti); color: var(--textColor); position: absolute;}
		}
	}
	.cd-photopay-tooltip{
		width: auto; right: 0; top: 100%; left: 0; position: absolute; background: white; padding: 14px 20px 5px; box-shadow: 0 2px 15px 0 rgba(0,0,0,0.15); font-size: 13px; line-height: 16px; z-index: 20; visibility: hidden; opacity: 0; transition: visibility .3s, opacity .3s;
		&:before{.pseudo(8px,8px); background: var(--white); right: 21px; top: -4px; .rotate(45deg);}
		:deep(h5){padding-top: 0; padding-bottom: 2px; font-size: 13px; line-height: 17px;}
		@media (max-width: @m){
			padding: 15px 30px 9px 21px;
			&:before{right: unset; left: 248px;}
		}
	}
	.cd-tooltip-close{
		display: none; position: absolute; width: 20px; height: 20px; top: 11px; right: 11px; align-items: center; justify-content: center; cursor: pointer; z-index: 2;
		&:after{.icon-close; font: 15px/1 var(--fonti); color: var(--red); font-weight: bold; align-items: center; justify-content: center; display: flex; align-items: center; justify-content: center;}
		@media(max-width: @t){display: flex;}
	}
</style>