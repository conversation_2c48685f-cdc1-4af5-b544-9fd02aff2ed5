<template>
	<BaseCmsPage v-slot="{page}">
		<Body class="page-brands" />

        <main class="main">
            <div class="wrapper main-wrapper">
                <div class="m-wrapper">
                    <div class="m-header">
                        <CmsBreadcrumbs :items="page?.breadcrumbs" />
                        <h1 v-if="page?.seo_h1">{{ page.seo_h1 }}</h1>
                        <div v-if="page?.content" v-html="page.content"></div>
                    </div>

                    <BaseCatalogManufacturers :fetch="{special: 1, limit: 12}" v-slot="{items}">
                        <div class="m-special" v-if="items?.length">                       
                            <NuxtLink v-for="manufacturer in items" :key="manufacturer.id" :to="manufacturer.url_without_domain" class="m-special-item">
                                <span><BaseUiImage :data="manufacturer.main_image_thumbs?.['width100-height30']" default="/images/no-image-50.jpg" :alt="manufacturer.title" /></span>
                            </NuxtLink>         
                        </div>
                    </BaseCatalogManufacturers>

                    <BaseCatalogManufacturers :fetch="{hierarchy_by: 'alphabet', limit: 0, sort: 'title'}" v-slot="{items}">
                        <div class="m-items">                 
                            <template v-if="items?.length">
                                <div class="m-column" v-for="manufacturer in items" :key="manufacturer.alphabet">
                                    <h3 class="m-letter">{{ manufacturer.alphabet }}</h3>
                                    <ul class="m-list">
                                        <li v-for="item in manufacturer.items" :key="item.id">
                                            <div class="m-title">
                                                <NuxtLink :to="item.url_without_domain">{{ item.title }}</NuxtLink>
                                            </div>
                                        </li>
                                    </ul>						
                                </div>							
                            </template>
                            <template v-else><BaseCmsLabel code="no_manufacturers" /></template>               
                        </div>
                    </BaseCatalogManufacturers>
                </div>
            </div>
        </main>        
	</BaseCmsPage>
</template>