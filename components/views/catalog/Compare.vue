<template>
	<BaseCmsPage v-slot="{page}">
		<ClientOnly>
			<BaseCatalogCompare v-slot="{items, attributes, differentAttributes, onToggleMode, mode}">
				<Body :class="['page-compare']" />
                <main class="main">
                    <div class="wrapper main-wrapper">
                        <div class="pos-r compare-sidebar-header">
                            <h1 class="page-compare-title"><BaseCmsLabel code="compare_products_title" /></h1>
                            <div class="compare-pager" v-if="items?.length > 0">
                                <BaseUiSwiper :options="thumbsOptions" @init="setThumbsSwiper">
                                    <BaseUiSwiperSlide v-for="(item,index) in items.slice(0, 3)" :key="item.id">
                                        <div class="compare-page">{{index + 1}}</div>
                                    </BaseUiSwiperSlide>

                                    <template v-if="items?.length < 3">
                                        <BaseUiSwiperSlide>
                                            <div class="compare-page">3</div>
                                        </BaseUiSwiperSlide>
                                    </template>
                                </BaseUiSwiper>
                            </div>                            
                        </div>
                        <div class="fz0 clear c-compare-items">
                            <BaseUiSwiper v-if="items?.length" :options="sliderOptions" :thumbs-swiper="thumbsSwiper" @init="setMainSwiper">
                                <template v-if="items?.length">
                                    {{updateItems(items, attributes)}}
                                    <BaseUiSwiperSlide v-for="item in items.slice(0, 3)" :key="item.id" class="compare-item">
                                        <CatalogIndexEntry :item="item" mode="compare" />

                                        <div class="c-compare-m-btns" v-if="items.length > 1">
                                            <span @click="onToggleMode('difference')" class="c-compare-btn c-compare-btn-diff" :class="{'active': mode == 'difference'}"><BaseCmsLabel code="compare_difference" /></span>
                                            <span @click="onToggleMode('all')" class="c-compare-btn c-compare-btn-all" :class="{'active': mode == 'all'}"><BaseCmsLabel code="compare_all_details" /></span>
                                        </div>

                                        <div class="cp-attributes">
                                            <table class="table-cp-attributes">
                                                <tbody>
                                                    <template v-for="(attr, index) in item.attributes" :key="attr.id">
                                                        <tr v-if="attr.title" class="attr-row" :class="['attr-row'+index, {'active': !differentAttributes.includes(attr.attribute_code) && mode == 'difference'}]" :data-compare-attribute="attr.attribute_code">
                                                            <td class="col-attribute-title">{{ attr.attribute_title }}</td>
                                                            <td class="col-title">{{attr.title}}</td>
                                                        </tr>
                                                        <tr v-else class="attr-row attr-row-empty" :class="['attr-row'+index, {'active': !differentAttributes.includes(attr.attribute_code) && mode == 'difference'}]" :data-compare_attribute="attr.attribute_code">
                                                            <td class="col-attribute-title">-</td>
                                                            <td class="col-title">-</td>
                                                        </tr>
                                                    </template>
                                                </tbody>
                                            </table>
                                        </div>
                                    </BaseUiSwiperSlide>
                                </template>
                                <template v-if="items?.length < 3">
                                    <BaseUiSwiperSlide class="compare-item">
                                        <article class="cp cp-compare cp-new-compare">
                                            <CatalogCompareSearch mode="new-compare" />
                                        </article>
                                    </BaseUiSwiperSlide>
                                </template>
                            </BaseUiSwiper>
                            <div v-else class="compare-item">
                                <article class="cp cp-compare cp-new-compare">
                                    <CatalogCompareSearch mode="new-compare" />
                                </article>
                            </div>
                        </div>
                        <div v-if="items?.length" class="c-compare-sidebar-attributes" >
                            <div class="c-compare-btns-cnt" v-if="items.length > 1">
                                <span @click="onToggleMode('difference')" class="c-compare-btn c-compare-btn-diff" :class="{'active': mode == 'difference'}"><BaseCmsLabel code="compare_difference" /></span>
                                <span @click="onToggleMode('all')" class="c-compare-btn c-compare-btn-all" :class="{'active': mode == 'all'}"><BaseCmsLabel code="compare_all_details" /></span>
                            </div>

                            <table v-if="items?.length" class="table-cp-attributes table-c-all-attributes">
                                <tbody>
                                    <tr v-for="(attribute,index) in attributes" :key="attribute.id" class="attr-row" :class="['attr-row'+index, {'active': !differentAttributes.includes(attribute.attribute_code) && mode == 'difference'}]" :data-compare-attribute="attribute.attribute_code">
                                        <td class="col-title">{{ attribute.attribute_title }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>                        
                    </div>
                </main>
			</BaseCatalogCompare>
			<template #fallback>
				<BaseThemeUiLoading />
			</template>
		</ClientOnly>
	</BaseCmsPage>
</template>

<script setup>
	const items = ref([]);

	const thumbsOptions = {
		slidesPerView: 3,
		slidesPerGroup: 3,
		navigation: {enabled: false},
		breakpoints: {
			750: {
				enabled: false,
			}
		}
	}

	const sliderOptions = {
		navigation: {
			enabled: true,
			wrapperClass: 'splide__arrows',
			nextElClass: 'splide__arrow splide__arrow--next',
			prevElClass: 'splide__arrow splide__arrow--prev'
		},
		breakpoints: {
			750: {
				enabled: false,
			}
		}
	}

	const thumbsSwiper = ref(null);
	const mainSwiper = ref(null);

	const setThumbsSwiper = (swiper) => {
		thumbsSwiper.value = swiper;
	};

	const setMainSwiper = (swiper) => {
		mainSwiper.value = swiper;
	};

	const updateItems = (newItems, atts) => {
		if(items.value == newItems) return;
		items.value = newItems;
	
		if(mainSwiper.value){
			setTimeout(() => {
				mainSwiper.value.update();
			}, 50);
		}	
		if(thumbsSwiper.value){
			setTimeout(() => {
				thumbsSwiper.value.update();
			}, 100);
		}

        nextTick(() => {
            adjustRowHeights(atts);
        });

	};

    function adjustRowHeights(atts){     
        const colTitles = document.querySelectorAll('tr.attr-row .col-title');
        colTitles.forEach((col) => {
            col.style.height = '';
        });

        for (let i = 0; i <= atts.length; i++) {
            let maxRowHeight = 0;
            const rows = document.querySelectorAll(`tr.attr-row${i} .col-title`);

            rows.forEach((row) => {
                const outerHeight = row.offsetHeight;
                if (outerHeight > maxRowHeight) {
                    maxRowHeight = outerHeight;
                }
            });

            rows.forEach((row) => {
                row.style.height = `${maxRowHeight}px`;
            });
        }
    };
</script>

<style lang="less" scoped>
    @media (max-width: @m){        
        :deep(.swiper-button){       
            position: absolute; left: 15px; top: 225px; z-index: 40; min-width: 0; background: #fff; box-shadow: inset 0 0 0 3px var(--yellow); width: 35px; height: 35px; font-size: 0; padding: 0; border-radius: 100%; cursor: pointer; .transition(background-color);
            &:before{.icon-arrow-right; font: 12px/35px var(--fonti); position: absolute; left: 0; top: 0; width: 100%; text-align: center; color: var(--textColor);}
            &.swiper-button-disabled{
                cursor: default; background: #fff; box-shadow: inset 0 0 0 3px rgba(252,208,2,0.2);
                &:before{color: darken(#E0E2DB,10%)!important;}
            }
            &.swiper-button-lock{display: none;}
        }
        :deep(.swiper-button-prev){
            left: 15px; 
            &:before{transform: rotate(180deg);}
        }
        :deep(.swiper-button-next){left: auto; right: 15px;}	
    }
</style>