<template>
	<main class="main">
		<div class="wrapper main-wrapper">
			<CmsSidebar />

			<div class="main-content">
				<div class="cms-content">
					<CmsBreadcrumbs v-if="data?.breadcrumbs" :items="data.breadcrumbs" />
					<h1 v-if="data?.seo_h1">{{ data.seo_h1 }}</h1>
					<div v-if="data?.content" v-html="data.content" v-interpolation />

					<CmsShare />
				</div>
			</div>
		</div>
	</main>
</template>

<script setup>
	const {data} = inject('baseCmsPageData');
</script>
