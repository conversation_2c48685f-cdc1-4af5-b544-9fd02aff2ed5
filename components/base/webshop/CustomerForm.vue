<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-bind="$attrs" v-model="formData">
		<slot :loading="loading" :errors="errors" :meta="meta" :values="values" :fields="fields" :status="status" />
	</BaseForm>
</template>

<script setup>
	const emit = defineEmits(['load', 'submit']);
	const {emit: globalEmit} = useEventBus();
	const config = useAppConfig();
	const auth = useAuth();
	const webshop = useWebshop();
	const {getAppUrl} = useApiRoutes();
	const dom = useDom();
	const labels = useLabels();
	const props = defineProps({
		submitUrl: {
			type: String,
			default: 'webshop_shipping',
		},
		changeFields: String,
		locationConfig: Object,
	});
	const status = ref(null);
	const loading = ref(false);
	const formData = ref({});

	// fetch form fields
	const fields = ref(null);

	// change fields types and validation if needed: https://hapi.marker.hr/#/Customer/get_v1_customer_fields_
	const changeFields = {};
	if (props.changeFields) {
		changeFields.change_fields = props.changeFields;
	}

	onMounted(async () => {
		fields.value = await auth.fetchForm({type: 'webshop.customer', ...changeFields}).then(res => res.data);

		// wait for next tick (DOM update) to make sure form is rendered
		await nextTick();

		// wrap r1 and company_oib fields
		wrapFields('field-group field-group-r1', ['b_r1', 'b_company_oib', 'b_company_name', 'b_company_address', 'b_company_zipcode', 'b_company_city']);

		// wrap b_shipping fields
		wrapFields('field-group field-group-shipping', ['b_same_as_shipping', 'b_first_name', 'b_last_name', 'b_address', 'b_zipcode', 'b_city', 'b_location', 'b_phone', 'b_house_number', 'b_country']);

		// setup country field visibility logic
		if (props.locationConfig?.countries?.length) setupCountryFieldVisibility();

		// set focus on first field in form. "data-autofocus" attribute needs to be set on form element
		dom.setFieldFocus();

		// emit event when form is loaded. Can be used to trigger analytics event or similar
		emit('load');
	});

	async function onSubmit({values, actions}) {
		loading.value = true;
		const formValues = values;
		if (props.changeFields) {
			formValues._change_fields = props.changeFields;
		}

		const res = await webshop.submitCustomerData(formValues);
		await webshop.fetchCustomer();
		status.value = res;

		// set field errors if response contains api errors
		if (res?.data?.errors?.length) {
			loading.value = false;
			res.data.errors.forEach(error => {
				actions.setFieldError(error.field, labels.get(error.error, error.error));
			});
			return res;
		}

		emit('submit', res);

		// if no api errors, navigate to next step
		if (res?.success && !res.data?.errors?.length && props.submitUrl) {
			return navigateTo(getAppUrl(props.submitUrl));
		}
		loading.value = false;
	}

	// wrap related fields with subfields
	function wrapFields(cssClass, relatedFields) {
		const rootField = document.getElementById(relatedFields[0]);
		if (!rootField) return;

		// get root element
		const rootElement = rootField.parentElement.parentElement;

		// create wrapper div
		let wrapperDiv = document.createElement('div');
		wrapperDiv.className = cssClass;

		// Move elements to the wrapper
		const relatedField = document.getElementById(relatedFields[0]).parentElement;
		rootElement.insertBefore(wrapperDiv, relatedField);
		relatedFields.forEach(field => {
			const el = document.querySelector(`input[name="${field}"], select[name="${field}"]`);
			if (el) wrapperDiv.appendChild(el.parentElement);

			// add event listener to main field
			if (field == relatedFields[0]) {
				el.addEventListener('change', function () {
					wrapperDiv.classList.toggle('active');
				});

				// set active class on load if checkbox is checked
				if (el.name == 'b_r1' && el.checked) wrapperDiv.classList.add('active');
				if (el.name == 'b_same_as_shipping' && !el.checked) wrapperDiv.classList.add('active');
			}
		});
	}

	// Set initial visibility of country fields and add event listeners to handle country changes
	function setupCountryFieldVisibility() {
		// Wait for form values to be set
		setTimeout(() => {
			const countryField = document.querySelector('select[name="country"]');
			if (countryField) {
				countryField.addEventListener('change', () => handleCountryChange(countryField.value));
				handleCountryChange(formData.value?.values?.country, '', false);
			}

			const bCountryField = document.querySelector('select[name="b_country"]');
			if (bCountryField) {
				bCountryField.addEventListener('change', () => handleCountryChange(bCountryField.value, 'b_'));
				handleCountryChange(formData.value?.values?.b_country, 'b_', false);
			}
		}, 200);
	}

	// Handle country change
	let countryChangeTimeout;
	function handleCountryChange(countryValue, prefix = '', clearValues = true) {
		clearTimeout(countryChangeTimeout);
		countryChangeTimeout = setTimeout(() => {
			// Emit event to show locations based on selected country
			globalEmit('selectedCountry', {
				country: formData.value?.values?.country,
				bcountry: formData.value?.values?.b_country,
			});
		}, 300);

		const needsLocation = props.locationConfig.countries.includes(parseInt(countryValue));

		// Get fields
		const locationField = document.querySelector(`.field-${prefix}location`);
		const zipcodeField = document.querySelector(`.field-${prefix}zipcode`);
		const cityField = document.querySelector(`.field-${prefix}city`);

		// Do not do anything if fields are not found
		if (!locationField || !zipcodeField || !cityField) return;

		// Pupulate location field when zipcode or city is changed
		const zipcodeInput = zipcodeField?.querySelector('input');
		const cityInput = cityField?.querySelector('input');
		if (zipcodeInput) zipcodeInput.addEventListener('input', () => formData.value.setFieldValue(prefix + 'location', zipcodeInput.value + ' ' + cityInput.value));
		if (cityInput) cityInput.addEventListener('input', () => formData.value.setFieldValue(prefix + 'location', zipcodeInput.value + ' ' + cityInput.value));

		// Clear values if needed (when country is changed)
		if (clearValues) {
			formData.value.setValues({
				[prefix + 'location']: '',
				[prefix + 'zipcode']: '',
				[prefix + 'city']: '',
			});
		}

		// Show/hide fields
		if (needsLocation) {
			if (locationField) locationField.removeAttribute('hidden');
			if (zipcodeField) zipcodeField.setAttribute('hidden', '');
			if (cityField) cityField.setAttribute('hidden', '');
		} else {
			if (locationField) locationField.setAttribute('hidden', '');
			if (zipcodeField) zipcodeField.removeAttribute('hidden');
			if (cityField) cityField.removeAttribute('hidden');
		}
	}
</script>

<style>
	.field-group > *:not(:first-child) {
		display: none;
	}
	.field-group.active > *:not(:first-child) {
		display: block;
	}
	.field[hidden] {
		display: none !important;
	}
</style>
