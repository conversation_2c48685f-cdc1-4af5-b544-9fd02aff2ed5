<template>
	<BaseForm @submit="onSubmit" :loading="loading" v-slot="{errors, meta, values}" v-model="formValues">
		{{ validPayment }}
		<slot :errors="errors" :meta="meta" :values="values" :loading="loading" :fields="fields" :selectedPayment="selectedPayment" :selectedField="selectedField" :status="status" :onPaymentUpdate="onPaymentUpdate" />
	</BaseForm>
</template>

<script setup>
	const emit = defineEmits(['load', 'update', 'submit']);
	const config = useAppConfig();

	let gtm;
	if (__GTM_TRACKING__) gtm = useGtm();

	const {getCurrency} = useCurrency();

	let fbCapi;
	if (__FB_CAPI_TRACKING__) fbCapi = useFbCapi();

	const auth = useAuth();
	const router = useRouter();
	const props = defineProps({
		submitUrl: {
			type: String,
			default: 'webshop_review_order',
		},
	});

	const {getAppUrl} = useApiRoutes();
	const {getCartData, updatePayment} = useWebshop();
	const cartData = computed(() => getCartData());
	const status = ref(null);
	const loading = ref(false);
	const formValues = ref();

	// emit event when form is loaded. Can be used to trigger analytics event or similar
	onMounted(() => emit('load'));

	const {validPayment} = inject('baseWebshopCheckoutData');
	watch(
		formValues,
		data => {
			if (!data?.value) return;
			//validPayment.value = data?.meta?.valid;
			console.log(data.value);
		},
		{immediate: true}
	);

	// selected payment method from cart
	const selectedPayment = computed(() => {
		if (cartData.value?.cart?.payments?.selected?.length) {
			return cartData.value.cart.payments.selected[0];
		}

		return null;
	});

	// currently selected payment field on frontend (not saved to cart yet)
	const selectedField = computed(() => {
		const selected = formValues.value?.values?.payment;
		if (selected && cartData.value?.cart?.payments?.available?.length) {
			return cartData.value.cart.payments.available.find(item => item.id == selected);
		}

		return null;
	});

	// payment fields
	const fields = computed(() => {
		if (cartData.value?.cart?.payments?.available?.length) {
			return [
				{
					name: 'payment',
					type: 'radio',
					options: cartData.value.cart.payments.available.map(item => ({
						...item,
						key: item.id,
						title: item.title,
						selected: selectedPayment.value?.id == item.id,
					})),
					validation: [
						{
							type: 'not_empty',
							value: null,
							error: 'error_not_empty',
						},
					],
				},
			];
		}

		return [];
	});

	// submit selected payment option
	let statusTimeout;
	async function onPaymentUpdate(field) {
		status.value = null;
		loading.value = true;
		if (statusTimeout) clearTimeout(statusTimeout);

		// wait for form meta to be updated
		await new Promise(resolve => setTimeout(resolve, 500));

		// if form is not valid, do not submit
		if (!formValues.value?.meta?.valid) {
			loading.value = false;
			/* 
				do not submit 
				if there are errors in the form 
				if selected payment type is credit card and card type is not yet selected
			*/
			if (!formValues.value.values.hasOwnProperty('payment_option_id') || (formValues.value.values.hasOwnProperty('payment_option_id') && !formValues.value.values.payment_option_id)) {
				return;
			}
		}

		// update payment option
		const res = await updatePayment({
			payment: formValues.value.values.payment,
			payment_option_id: formValues.value?.values.payment_option_id ? formValues.value.values.payment_option_id : 0,
			cc_installments: formValues.value?.values.cc_installments ? formValues.value.values.cc_installments : 1,
		});

		// emit event when payment is updated. Can be used to trigger analytics event or similar
		emit('update', res);

		// set status message and hide it after 5 seconds
		status.value = res;
		statusTimeout = setTimeout(() => {
			status.value = null;
		}, 5000);
		loading.value = false;
	}

	// handle form submit. If loading is not active and no api errors, navigate to next step
	async function onSubmit({values}) {
		if (!loading.value && !status.value?.data?.errors?.length && props.submitUrl) {
			loading.value = true;

			// gtm tracking
			if (gtm) {
				gtm.gtmTrack('addPaymentInfo', {
					cart: cartData.value,
					payment_type: selectedPayment.value?.code,
				});
			}

			if (fbCapi) {
				const cartProducts = getCartData('products');
				fbCapi.sendEvent('addPaymentInfo', {
					content_ids: cartProducts.map(item => item.code),
					contents: cartProducts.map(el => {
						return {
							id: el.code,
							quantity: el.quantity,
							item_price: el.total,
						};
					}),
					currency: getCurrency()?.code,
					value: cartData.value?.total?.total_items_total || 0,
					content_type: 'product',
					payment_method: selectedPayment.value?.code,
				});
			}

			emit('submit', values);
			return navigateTo(getAppUrl(props.submitUrl));
		}
	}
</script>
