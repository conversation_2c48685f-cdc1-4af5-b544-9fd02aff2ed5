<template>
	<Link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
	<Link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<Link rel="shortcut icon" href="/favicon.ico" />
	<Link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
	<Meta name="apple-mobile-web-app-title" content="centar-tehnike" />
	<Link rel="manifest" href="/site.webmanifest" />

	<Body :class="{'fixed-header': fixedHeader}" />
	<!-- FIXME speed; adventski; savjetnik-->
	<div class="page-wrapper" id="top">
		<template v-if="showHeader">
			<CmsHeader />
		</template>

		<slot />

		<template v-if="showHeader">
			<ClientOnly>
				<LazyCmsNewsletter hydrate-on-visible />
			</ClientOnly>
			<CmsFooter />
		</template>
	</div>

	<button class="tally-chat-btn" data-tally-open="nWq7P3" data-tally-align-left="1" data-tally-hide-title="1" data-tally-emoji-text="👋" data-tally-emoji-animation="wave"><span>Korisnička podrška</span></button>

	<ClientOnly>
		<div @click="scrollTo('#top')" class="ontop" :class="{'active': showToTopButton}"></div>
		<LazyWebshopAddToCartModal hydrate-on-visible />
		<LazyGdpr :hydrate-after="1000" />
		<LazyBaseThemeUiModal v-if="Object.keys(modal.activeModals()).includes('quick')" name="quick" :zoom="true" :mask-closable="true" :svgicons="false" />
		<LazyBaseThemeUiAdminBar v-if="user?.staff || user?.superuser || user?.developer" />
	</ClientOnly>
</template>

<script setup>
	const modal = useModal();
	const {user} = useAuth();
	const {scrollTo, onMediaQuery} = useDom();
	const { addScript } = useMeta();

	const showToTopButton = ref(false);
	const fixedHeader = ref(false);
	const route = useRoute();
	const showHeader = computed(() => {
		if(!route.meta.action) return true;
		return (['customer', 'shipping', 'payment', 'review_order'].includes(route.meta.action)) ? false : true;
	});

	const onScroll = () => {
		const scroll = window.scrollY;
		
		if (!document.body.classList.contains('page-checkout')) {
			showToTopButton.value = (scroll > 800) ? true : false;
			if (!document.body.classList.contains('active-autocomplete') && !document.body.classList.contains('active-nav')) {
				fixedHeader.value = (scroll > 90) ? true : false;
			}
		} else{
			fixedHeader.value = false;
		}
	};

	onMediaQuery({
		query: '(max-width: 750px)',
		leave: () => {
			window.location.reload();
		}
	});		
	
	onBeforeUnmount(() => {
		window.removeEventListener('scroll', onScroll);
	})
	onMounted(async () => {
		window.addEventListener('scroll', onScroll, {passive: true});

		addScript({
			key: 'TallyChat',
			src: '/media/tallychat.js',
			defer: true
		});

		addScript({
			key: 'MauticTrackingObject',
			gdpr: 'analytics',
			innerHTML: `
				(function(w,d,t,u,n,a,m){w['MauticTrackingObject']=n;
					w[n]=w[n]||function(){(w[n].q=w[n].q||[]).push(arguments)},a=d.createElement(t),
					m=d.getElementsByTagName(t)[0];a.async=1;a.src=u;m.parentNode.insertBefore(a,m)
				})(window,document,'script','https://centartehnike.sales-snap.com/mtc.js','mt');
				mt('send', 'pageview');
			`,
		});
	});
</script>