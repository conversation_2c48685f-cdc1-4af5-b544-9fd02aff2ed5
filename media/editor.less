@import "defaults.less";


/*------- helpers -------*/
.float-left { float: left; }
.float-right { float: right; }
.strong { font-weight: bold; }
.italic { font-style: italic; }
.uppercase { text-transform: uppercase; }

.first{margin-left:0 !important;}
.last{margin-right:0 !important;}

.image-left, .alignleft { float: left; margin: 5px 20px 10px 0px; }
.image-right, .alignright { float: right; margin: 5px 0px 10px 20px; }

.align-left {text-align:left;}
.align-right {text-align:right;}
.center {text-align:center;}

.underline { text-decoration:underline; }
.nounderline { text-decoration:none; }
.rounded { border-radius: @borderRadius; }

.red {color: @red;}
.green {color: @green;}
.orange {color: @orange;}

.first-title { margin-top: 0; padding-top: 0; }

.icon{
	position: relative;
	&:before{.icon-pin; font: 15px/15px @fonti; margin-right: 10px; display: inline-block;}
}
.icon-tel:before{.icon-phone;}
.icon-tel2:before{.icon-phone2;}
.icon-mail:before{.icon-envelope;}
.icon-mail2:before{.icon-mail;}
.icon-arrow:before{.icon-arrow-right;}
.icon-reverse:before{.scaleX(-1);}

.extra{font-size: 22px; line-height: 30px;}
.extra2{
	background: @gray; color: #fff; padding: 35px 40px; font-size: 22px; line-height: 32px; margin-bottom: 25px;
	a{
		text-decoration: underline; color: @yellow;
		&:hover{color: @yellow; text-decoration: none;}
	}
}
.extra3{border: 3px solid @yellow; padding: 35px 40px; font-size: 22px; line-height: 32px;}
.testimonial{
	position: relative; margin-bottom: 55px; margin-top: 20px;
	img{position: absolute; left: 0; top: 0; z-index: 0;}
}
.testimonial-cnt{
	background: @gray; color: #fff; text-align: center; margin-left: 230px; top: 20px; padding: 65px 60px 40px; z-index: 1; position: relative; min-height: 240px; overflow: hidden;
	a{color: #fff;}
	&:after{.pseudo(270px,270px); background: @yellow; border-radius: 200px; top: -175px; right: -175px;}
	&:before{.icon-quote; font: 35px/35px @fonti; .scaleY(-1); position: absolute; right: 14px; top: 15px; color: @textColor; z-index: 1;}
}
.testimonial-name{color: @yellow; padding: 0; font-size: 18px; line-height: 23px; padding-top: 5px;}
.testimonial-title{font-size: 13px; text-transform: uppercase; font-weight: bold; padding: 0; line-height: 20px;}
/*------- /helpers -------*/

/*------- selectors -------*/
* { margin: 0; padding: 0; border: none; }
body {background: #fff; padding: 10px 15px; color: @textColor; .font(@fontSize, @lineHeight, @font); }
table { border-spacing: 0; border: none; }
a {
	color: @linkColor;text-decoration: underline; .transition();
	&:hover { text-decoration: underline;color: @linkHoverColor; }
}
ul, ol {margin: 0px 0px 10px 35px;padding: 0px;}
ol { margin-left: 40px; }
h1, h2, h3, h4{
	font-weight: bold; padding-bottom: 15px; padding-top: 35px; text-transform: uppercase;
	a, a:hover{text-decoration: none;}
}
h1{font-size: 48px; line-height: 51px; text-transform: uppercase; padding-top: 0; color: #000;}
h2{font-size: 36px; line-height: 40px;}
h3{font-size: 30px; line-height: 36px;}
h4{font-size: 24px; line-height: 35px;}
p{padding-bottom: 10px;}
/*------- /selectors -------*/

/*------- tables -------*/
.table{
	width: 100%; border-spacing: 0; margin: 10px 0px 20px; font-size: 15px;
	th{font-weight: bold; font-size: 15px; text-align: left; padding: 6px 0; border-bottom: 1px solid @borderColor;}
	td{border-bottom: 1px solid @borderColor; padding: 6px 0;}
	&.stripe tbody tr:nth-child(even){background: #E9E9E9;}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
.table-wrapper{overflow-x: scroll; -webkit-overflow-scrolling: touch;}
/*------- /tables -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{
	position: relative; display: inline-flex; align-items: center; justify-content: center; padding: 2px 25px 0; line-height: 19px; text-transform: uppercase; height: 55px; font-size: 18px; color: #fff; background: @gray; font-weight: bold; text-decoration: none; color: @yellow; .transition(all);
	&:hover{color: @yellow; background: @gray/1.2; text-decoration: none;}
}
.btn-yellow{
	background: @yellow; color: @textColor;
	&:hover{background: @yellow/1.1; color: @textColor;}
}
.btn-medium{height: 40px; padding: 0 20px; font-size: 16px; line-height: 22px; padding-top: 2px;}
.btn-small{height: 30px; font-size: 16px; padding-top: 1px; padding: 0 15px;}
.btn-border{border: 1px solid @gray;}
/*------- /buttons -------*/