function OpenPopUp(URL, name, features) {
	//v2.0
	window.open(URL, name, features);
}

$(function () {
	cmswebshop = CmsWebshop();
	cmswebshop.init({
		'tracking_enable': {
			'index': 'gtm:AddToCart|%ITEM_CODE%|%ITEM_TITLE%|%ITEM_CATEGORY_TITLE%|%ITEM_MANUFACTURER_TITLE%|%ITEM_PRICE_INT%',
			'detail': 'gtm:AddToCart|%ITEM_CODE%|%ITEM_TITLE%|%ITEM_CATEGORY_TITLE%|%ITEM_MANUFACTURER_TITLE%|%ITEM_PRICE_INT%',
		},
		'tracking_event': {
			'addToCart': 'AddToCart',
			'addClick': 'ProductClick',
		},
	});
	//cmswebshop.shopping_cart.set_min_order_total();

	cmscoupon = CmsCoupon();
	cmscoupon.init();

	cmscompare = CmsCompare();
	cmscompare.init();

	cmswishlist = CmsWishlist();
	cmswishlist.init();

	cmsgoogle4 = CmsGoogle4();
	cmsgoogle4.init();

	// newsletter
	cmsnewsletter = CmsNewsletter();

	var winWidth = $(window).width();
	let windowOffset = 0;

	// thank you page show password
	if ($('#field-password').size()) {
		$('#field-password').showPassword();
	}

	// variations
	if ($('[data-variations]').size()) {
		cmscatalog = CmsCatalog();
		cmscatalog.variation_choices('bxslider', 'input', 1);
		cmscatalog.change_variation_detail(0, $('[data-variations]').data('variation_active_id'));
	}

	// hero slider
	/* $('.hero-slider').slick({
		infinite: true,
		slidesToShow: 1,
		slidesToScroll: 1,
		fade: true,
		dots: true,
		arrows: false,
		draggable: false,
		autoplay: true,
		responsive: [
			{
				breakpoint: 1240,
				settings: {
					draggable: true
				}
			},
			{
				breakpoint: 750,
				settings: {
					fade: false
				}
			}
		]	
	}); */
	if (winWidth <= 980) {
		$('.benefits').slick({
			infinite: true,
			slidesToShow: 1,
			slidesToScroll: 1,
			dots: false,
			arrows: false,
			fade: true,
			autoplay: true,
			autoplaySpeed: 2000,
		});
	}

	if (winWidth <= 750) {
		// promo slider
		$('.cw .promo').slick({
			infinite: true,
			slidesToShow: 1,
			slidesToScroll: 1,
			dots: false,
			//arrows: false,
		});

		$('.mw-slider').slick({
			infinite: true,
			slidesToShow: 3,
			slidesToScroll: 3,
			dots: false,
			//arrows: false,
		});

		$('.p-items .pp-featured').unwrap();
		$('.pw .p-items, .pd-related .p-items').not('.pw-hp .p-items').slick({
			infinite: false,
			slidesToShow: 1,
			slidesToScroll: 1,
			dots: false,
			lazyLoad: 'ondemand',
		});

		var compareSlider = $('.compare-slider').slick({
			slidesToShow: 1,
			slidesToScroll: 1,
			infinite: false,
			lazyLoad: 'ondemand',
			dots: false,
			nav: false,
		});

		$('.compare-page').on('click', function (e) {
			e.preventDefault();

			var $this = $(this),
				index = $this.data('index');

			compareSlider.slick('slickGoTo', index);
			$('.compare-page').removeClass('active');
			$this.addClass('active');
		});

		compareSlider.on('beforeChange', function (event, slick, currentSlide, nextSlide) {
			$('.compare-page').removeClass('active');
			$('.compare-page[data-index="' + nextSlide + '"]').addClass('active');
		});
	}

	if (winWidth <= 1300) {
		//advent item
		$('.advent-item-disabled').on('click', function () {
			$(this).toggleClass('active');
			$('.advent-item-disabled').not(this).removeClass('active');
		});
	}

	$('.calendar-btn-coupon').on('click', function () {
		setTimeout(() => {
			if (!$('.coupon_message').hasClass('coupon_message_response_error')) {
				$(this).parent().addClass('active');
			}
		}, 800);
	});

	// catalog image slider
	var cdSlider = $('.cd-hero-slider').slick({
		infinite: false,
		slidesToShow: 1,
		slidesToScroll: 1,
		fade: true,
		dots: false,
		arrows: false,
		draggable: false,
		responsive: [
			{
				breakpoint: 750,
				fade: false,
			},
		],
	});

	$('.cd-hero-slider').on('afterChange', function (event, slick, direction) {
		$('.cd-thumb[data-slide-index="' + direction + '"]')
			.addClass('slick-current')
			.siblings()
			.removeClass('slick-current');
	});

	$('.cd-thumbs-slider').slick({
		infinite: false,
		slidesToShow: 5,
		slidesToScroll: 5,
		dots: false,
		draggable: false,
	});

	$('.cd-thumb').on('click', function () {
		var $this = $(this),
			index = $this.data('slide-index');

		cdSlider.slick('slickGoTo', index);
		$this.addClass('slick-current').siblings().removeClass('slick-current');
	});

	$('.btn-attr-energy').on('click', function () {
		var index = $(this).data('slide-index');
		cdSlider.slick('slickGoTo', index);

		if (winWidth <= 760) {
			$([document.documentElement, document.body]).animate(
				{
					scrollTop: $('#product_images').offset().top,
				},
				500
			);
		}
	});

	// trigger click on .btn-attr-energy element if mode is set in url
	var urlParams = new URLSearchParams(window.location.search);
	var mode = urlParams.get('mode');
	if (mode) {
		$('.btn-attr-energy').trigger('click');
	}

	$('.cd-related-slider').slick({
		slidesToShow: 2,
		slidesToScroll: 2,
		infinite: true,
		lazyLoad: 'ondemand',
		draggable: false,
		responsive: [
			{
				breakpoint: 750,
				settings: 'unslick',
			},
		],
	});

	$('.cw-slider').slick({
		slidesToShow: 5,
		slidesToScroll: 5,
		infinite: true,
		lazyLoad: 'ondemand',
		draggable: false,
		responsive: [
			{
				breakpoint: 1400,
				settings: {
					slidesToShow: 4,
					slidesToScroll: 4,
				},
			},
			{
				breakpoint: 750,
				settings: 'unslick',
			},
		],
	});

	if ($('.cd-hero-slider').size()) {
		var zoomWidth = 680,
			zoomHeight = 680;

		if (winWidth < 1400) {
			zoomWidth = 600;
		}

		if (winWidth > 1200) {
			//image zoom
			$('.cd-hero-slide:not(.cd-hero-slide-cert)>span>img').elevateZoom({
				easing: true,
				//zoomType: "inner",
				cursor: 'crosshair',
				borderSize: 1,
				responsive: true,
				zoomWindowOffetx: 0,
				zoomWindowOffety: 0,
				zoomWindowPosition: 'zoom-container',
				loadingIcon: '/media/images/loader.gif',
				zoomWindowWidth: zoomWidth,
				zoomWindowHeight: zoomHeight,
				borderColour: '#cccccc',
			});
		}
	}

	$('.product-gallery').fancybox({
		'type': 'iframe',
		'padding': 0,
		'margin': 0,
		'scrolling': 'auto',
		'width': '100%',
		'height': '100%',
		'autoSize': false,
		'autoScale': false,
		'fitToView': false,
		'wrapCSS': 'fancybox-gallery',
		afterLoad: function (current, previous) {
			$('body').addClass('fancybox-active');
		},
		afterClose: function () {
			$('body').removeClass('fancybox-active');
		},
	});

	// locations
	$('.lp-images').slick({
		infinite: true,
		slidesToShow: 1,
		slidesToScroll: 1,
		fade: true,
		dots: false,
		arrows: true,
		draggable: false,
	});

	// contact map
	var location_map = $('#map_canvas');
	if (location_map.size()) {
		setTimeout(function () {
			cmslocation = CmsLocation();
			cmslocation.config.markeractive = '/media/images/icons/pin1.svg';
			cmslocation.config.markerinactive = '/media/images/icons/pin1.svg';
			cmslocation.config.scrollwheel = false;
			cmslocation.init(site_lang, {maxZoom: 20}, {maxWidth: 200, pixelOffset: new google.maps.Size(25, -75), closeBoxMargin: '0', closeBoxURL: '/media/images/close.png'});
			cmslocation.set_points_detect();
		}, 2000);
	}

	// autocomplete
	var acItems = winWidth < 750 ? 3 : 5;

	/* $("input[name='search_q']").CmsAutocomplete({
		module: 'catalog',
		lang: site_lang,
		result_per_page: 'catalogproduct:' + acItems,
		result_fields: 'all:image,price,code',
		layout_default: '<span class="image"><img src="%item_image%" alt=""></span><span class="search-col2"><span class="search-title">%item_label%</span><span class="search-price">%item_price%</span></span>',
		auto_redirect: true,
	}); */

	$("input[name='search_q']").CmsAutocomplete({
		module: 'catalog',
		advanced: true,
		separated_contenttype: true,
		show_all: true,
		lang: site_lang,
		result_image: '64x64_r',
		result_fields: 'all:image,price,code',
		result_per_page: 'publish:5,catalogcategory:15,catalogmanufacturer:16,catalogproduct:6',
		layout_extra: {
			'catalogproduct': '<span class="image"><img src="%item_image%" alt=""></span><span class="search-cnt"><span class="search-title">%item_label%</span><span class="search-code">%item_code%</span><span class="search-price">%item_price%</span></span>',
		},
		show_all_layout_extra: {
			'catalogproduct': '%item_label%',
			'catalogcategory': '%item_label%',
			'catalogmanufacturer': '%item_label%',
			'publish': '%item_label%',
		},
		show_all_label_extra: {
			'catalogproduct': 'Prikaži sve proizvode',
			'catalogcategory': 'Prikaži sve',
			'catalogmanufacturer': 'Prikaži sve',
			'publish': 'Prikaži sve članke',
		},
		auto_redirect: true,
	});

	$("input[name='search_q']").on('autocompleteopen', function (event, ui) {
		body.addClass('active-autocomplete');
	});

	$("input[name='search_q']").on('autocompleteclose', function (event, ui) {
		body.removeClass('active-autocomplete');
	});
	// search
	var searchEl = $('div.sw');
	/* searchEl.find('.sw-toggle').on('click', function (e) {
		//console.log('active');
		searchEl.toggleClass('active');
		$('.header').toggleClass('active');
		searchEl.find('input').focus();
	}); */

	searchEl.find('.sw-input').on('click', function (e) {
		if (!searchEl.hasClass('active')) {
			searchEl.toggleClass('active');
			searchEl.find('input').focus();
		}
	});
	searchEl.find('.sw-toggle').on('click', function (e) {
		searchEl.removeClass('active');
		$('.sw-input').val('');
	});

	// compare search autocomplete
	setTimeout(function () {
		$("input[name^='compare_autocomplete-']").each(function () {
			$(this).CmsAutocomplete({
				module: 'catalog',
				lang: site_lang,
				result_fields: 'all:image,price',
				layout: '<span class="image"><img src="%item_image%" alt=""></span><span class="search-col2"><span class="search-title">%item_label%</span><span class="search-price">%item_price%</span></span>',
				mode: 'compare',
			});
		});
	}, 800);

	var totalAttrs = $('.c-compare-sidebar-attributes').data('total-attributes'),
		rowHeight = 0;

	function equalCompareTable() {
		$('tr.attr-row .col-title').css('height', '');
		for (var i = 1; i <= totalAttrs; i++) {
			$('tr.attr-row' + i).each(function (i) {
				var outerHeight = $(this).find('.col-title').outerHeight();
				if (outerHeight > rowHeight) {
					rowHeight = outerHeight;
				}
			});
			$('tr.attr-row' + i)
				.find('.col-title')
				.css('height', rowHeight);
			rowHeight = 0;
		}
	}

	equalCompareTable();

	//Auth widget
	if (winWidth > 980) {
		$('.aw>a').on('click', function (e) {
			var $this = $(this),
				parent = $this.parent();

			e.preventDefault();

			if (parent.hasClass('active')) {
				parent.toggleClass('active');
			} else {
				$('.aw').removeClass('active');
				$this.parent().addClass('active');
			}
		});
	}

	// rwd tables
	var iOS = navigator.userAgent.match(/(iPad|iPhone|iPod)/g) ? true : false;
	if (winWidth <= 750) {
		if (!$('body').hasClass('page-auth')) {
			// responsive tables
			$('.cms-content table').wrap('<div class="table-wrapper"/>');

			if (iOS == true) {
				$('div.table-wrapper').addClass('ios');
			}
		}
	}

	$('.btn-comments').on('click', function () {
		$('.tabs li#comments, .tabs-content #tab3').addClass('active').siblings().removeClass('active');
	});

	$('.footer-title').on('click', function () {
		$(this).parent().toggleClass('active');
	});

	// social share
	$(document).CmsShare({networks: 'facebook,whatsapp,viber,email'});

	// image titles
	$('.main .image-border').CmsUtilsImageTitle();

	// page transitions
	if ($('.sweepstake-pages').length) {
		kontextElement = kontext(document.querySelector('.sweepstake-pages'));
		var kontextPrevSlide = 0;

		$('.btn-sweepstake-next').on('click', function () {
			kontextPrevSlide = kontextElement.getIndex();
		});
		$('.btn-sweepstake-prev').on('click', function () {
			var prev_page = parseInt($(this).data('prev_page'), 10);
			if (!isNaN(prev_page)) {
				kontextElement.goTo(prev_page);
			} else {
				kontextElement.prev();
			}

			// pager
			var page_index = kontextElement.getIndex();
			var next_page = page_index + 1;

			$('.sweepstake-pager-item' + next_page)
				.removeClass('active')
				.removeClass('completed');
			$('.sweepstake-pager-item' + page_index)
				.removeClass('completed')
				.addClass('active');
		});
	}

	// fixed elements
	var scrollPosition = 0,
		header = $('.header'),
		body = $('body');

	if (!$('body').hasClass('page-checkout')) {
		$(window).on('scroll', function () {
			var currScrollPosition = $(this).scrollTop();

			scrollPosition = currScrollPosition;

			var ontop = $('a.ontop');
			if (scrollPosition > 800) {
				ontop.addClass('active');
			} else {
				ontop.removeClass('active');
			}
			if (!$('body').hasClass('active-autocomplete')) {
				if (scrollPosition > 90) {
					body.addClass('fixed-header');
				} else {
					body.removeClass('fixed-header');
				}
			}
		});
	}

	// news ticker
	if ($('.p-ticker-body').length) {
		$('.p-ticker-body').carouselTicker();
	}

	// faq
	$('.fp-title').on('click', function () {
		$(this).parent().toggleClass('active');
	});

	// catalog filter
	$('#attribute_filters_select').CmsFilter({lang: site_lang});

	// floating labels
	$('.field-b_company_name, .field-b_company_oib, .field-b_company_address').addClass('field-cnt').removeClass('field');

	$('.field-b_r1').on('click', function () {
		setTimeout(function () {
			$('.field-cnt').floatingFormLabels();
		}, 200);
	});

	$('.field').floatingFormLabels();
	setTimeout(function () {
		if (winWidth > 750) {
			$('#webshop_form:not(".step3") .field:first').addClass('ffl-floated');
		} else {
			$('#webshop_form:not(".step3") .field:first input').blur();
		}
	}, 50);

	setTimeout(function () {
		$('.comment-form .field').floatingFormLabels();
	}, 500);

	$('.field-cnt').floatingFormLabels();

	// close shopping cart preview
	$('.product-message-modal .close, .modal-continue-shopping').live('click', function () {
		$('.product-message-modal').removeClass('active');
		$('body').removeClass('.active-cart-modal');
	});

	var acceptTerms = $('#field-accept_terms');

	function checkTerms() {
		if (!acceptTerms.is(':checked')) $('.wc-accept-terms-tooltip1').toggleClass('active');
	}

	$('.btn-finish').on('mouseover', function () {
		checkTerms();
	});
	$('.btn-finish').on('mouseout', function () {
		checkTerms();
	});

	if (winWidth <= 1300) {
		$('.cart.active').on('mouseover', function () {
			location.href = $(this).data('shoppingcart_url');
		});
	}

	$('.btn-toggle-filter, .btn-close-filter').on('click', function () {
		$('body').toggleClass('active-filter');
		$('.btn-toggle-filter, .c-sidebar').toggleClass('active');
	});

	/* $('.btn-toggle-nav').on('click', function () {
		$('body').toggleClass('active-nav');
		$(this).toggleClass('active');
	}); */

	$(document).on('mouseup', function (e) {
		var container = $('div.modal-box');

		if (!container.is(e.target) && container.has(e.target).length === 0) {
			$('div.product-message-modal').removeClass('active');
		}
	});

	if (winWidth <= 750) {
		$('.btn-toggle-nav').on('click', function () {
			$(this).toggleClass('active');
			$('.nc-m').removeClass('active');
			$('.nc-m>*').removeClass('active');
			$('.nc-m-cnt').removeClass('lvl2').removeClass('lvl3');
			if ($('body').hasClass('active-nav')) {
				$('body').removeClass('active-nav');
				window.scroll(0, windowOffset);
				$('.header-m').removeClass('menu-lvl-active');
			} else {
				windowOffset = $(window).scrollTop();
				$('body').addClass('active-nav');
			}
		});
	}

	if (winWidth <= 750) {
		//nav lvl1
		$('.nc-m-lvl1 a').on('click', function (e) {
			var $this = $(this),
				parent = $this.parent(),
				id = $this.data('category'),
				thisDataTitle = $this.data('menu_item_title');

			$('.nc-m-lvl1 a, .nc-m-lvl2 a, .nc-m-lvl3 a, .nc-m-lvl2, .nc-m-lvl3').removeClass('active');

			if (parent.hasClass('has-children')) {
				e.preventDefault();
				$this.addClass('active');
				$('.nc-m-lvl2[data-category=' + id + ']').addClass('active');
				$('.header-m').addClass('menu-lvl-active');
				parent.addClass('active-extra');
				$('.nc-m-cnt').addClass('lvl2');
			}

			$('.m-nav-title').html(thisDataTitle);
		});

		//nav lvl2
		$('.nc-m-lvl2 a').on('click', function (e) {
			var $this = $(this),
				parent = $this.parent(),
				id = $this.data('subcategory'),
				thisDataTitle = $this.data('menu_item_title');

			$('.nc-m-lvl2 a, .nc-m-lvl3 a, .nc-m-lvl3').removeClass('active');
			if (parent.hasClass('has-children')) {
				e.preventDefault();
				$this.addClass('active');
				$('.nc-m-lvl3[data-subcategory=' + id + ']').addClass('active');
				parent.addClass('active-extra');
				$('.nc-m-cnt').removeClass('lvl2');
				$('.nc-m-cnt').addClass('lvl3');
			}

			$('.m-nav-title').html(thisDataTitle);
		});

		//RETURN MENU BUTTON
		$('.m-nav-title').on('click', function () {
			var menuTitleData = $('.m-nav-title').data('menu_title'),
				lvl3 = $('.nc-m-lvl3'),
				lvl2 = $('.nc-m-lvl2');

			if (lvl3.length && $(lvl3).hasClass('active')) {
				$('.nc-m-lvl3').removeClass('active');

				var $thisMenuTitle = $('.nc-m-lvl1>.active-extra>a'),
					thisMenuDataTitle = $thisMenuTitle.data('menu_item_title');
				$('.m-nav-title').html(thisMenuDataTitle);

				$('.nc-m-lvl2>li').removeClass('active-extra');
				$('.nc-m-cnt').removeClass('lvl3');
				$('.nc-m-cnt').addClass('lvl2');
			} else if (lvl2.length && $(lvl2).hasClass('active')) {
				$('.m-nav-title').html(menuTitleData);
				$('.header-m').removeClass('menu-lvl-active');
				$('.nc-m-lvl2').removeClass('active');
				$('.nc-m-lvl1>li').removeClass('active-extra');
				$('.nc-m-cnt').removeClass('lvl2');
			} else {
				$('body').removeClass('active-nav');
				window.scroll(0, windowOffset);
				$('.header-m').removeClass('menu-lvl-active');
				$('.btn-toggle-nav').removeClass('active');
				$('.nc-m-lvl1 a, .nc-m-lvl2 a, .nc-m-lvl3 a, .nc-m-lvl2, .nc-m-lvl3').removeClass('active');
			}
		});
	}

	// delay submenus
	if (winWidth > 1250) {
		$('.nav-categories>li')
			.mouseenter(function () {
				var el = $(this);

				el.delay(300).queue(function () {
					el.addClass('active');
					el.dequeue();
				});
			})
			.mouseleave(function () {
				$('.nav-categories>li').stop(true).removeClass('active');
			});
	} else if (winWidth > 980) {
		$('.nav-categories>li>a').click(function (e) {
			var parent = $(this).parent();

			if (parent.hasClass('has-children')) {
				e.preventDefault();
				if (!parent.hasClass('active') && winWidth > 760) {
					$('.nav-categories>li').removeClass('active');
				}
				parent.toggleClass('active');
			}
		});
	}

	// close elements on outside click
	function closeActiveElement(el) {
		$(document).on('mouseup', function (e) {
			var container = el;
			if (!container.is(e.target) && container.has(e.target).length === 0) {
				container.removeClass('active');
			}
		});
	}
	closeActiveElement($('.sw'));
	closeActiveElement($('.aw'));
	closeActiveElement($('.nav-categories>li'));
	closeActiveElement($('.btn-installments-calc, .cd-payment-installments, .box_price'));

	if (winWidth <= 750) {
		$('.nav-submenu>li.has-children>a').on('click', function (e) {
			e.preventDefault();
			$(this).parent().toggleClass('active');
		});
	}

	// filter tooltip
	$('.cf-row-detail').on('click', function () {
		var $this = $(this),
			tooltip = $this.next();

		if (tooltip.hasClass('active')) {
			tooltip.toggleClass('active');
			$this.toggleClass('active');
		} else {
			$('.cf-tooltip, .cf-row-detail').removeClass('active');
			tooltip.addClass('active');
			$this.addClass('active');
		}
	});

	$('.btn-close-cf-tooltip').on('click', function () {
		$('.cf-tooltip, .cf-row-detail').removeClass('active');
	});

	var observer = new MutationObserver(function (mutations) {
		if (document.querySelector('.fancybox-opened')) {
			document.body.classList.add('active-fancybox');
		} else {
			document.body.classList.remove('active-fancybox');
		}
	});

	observer.observe(document.body, {
		attributes: false,
		childList: true,
		characterData: false,
		subtree: true,
	});

	ssm.addState({
		query: '(max-width: 1240px)',
		onEnter: function () {
			$('.cp-list').each(function () {
				var $this = $(this),
					badge = $this.find('.cp-list-badge-special');

				badge.insertAfter($this.find('.cp-list-code'));
			});

			equalCompareTable();
		},
	});

	ssm.addState({
		query: '(max-width: 990px)',
		onEnter: function () {
			$('.cf-active').detach().appendTo('.wrapper-toolbar');
			equalCompareTable();
		},
		onLeave: function () {
			$('.cf-active').insertAfter('.c-sidebar-advisor');
			equalCompareTable();
		},
	});

	ssm.addState({
		query: '(max-width: 750px)',
		onEnter: function () {
			//$('.header .nav-categories').detach().appendTo('.header-m');
			$('.header .nc-m-cnt').detach().appendTo('.header-m');
			$('.nav').detach().appendTo('.header-m');
			$('.social-top').detach().appendTo('.header-m');
			$('.top-contact').detach().appendTo('.header-m');
			$('.c-sidebar-advisor').insertBefore('.toolbar');
			$('.c-counter').detach().appendTo('.wrapper-toolbar');
			$('.toolbar-filter-qty').appendTo('.wrapper-toolbar');
			$('.toolbar-filter-discount').appendTo('.wrapper-toolbar');
			$('.cd-header').appendTo('.cd-header-placeholder');
			$('.cd-share').insertAfter('.cd-benefits-wrapper');
			$('.wp').each(function () {
				var $this = $(this),
					attr = $this.find('.wp-attrs'),
					title = $this.find('.wp-title');

				attr.insertAfter(title);
			});
			$('.btn-sweepstake-cancel').insertAfter('.sweepstake-buttons');
			$('.flyer-download').insertAfter('.flyer-content');
			$('.a-auth-title').insertBefore('.sidebar-container');
			//$('.cards').insertBefore('.footer-row3');
			$('.aw').insertBefore('.ww');
			$('.exchange-rate').insertAfter('.cards');
		},
		onLeave: function () {
			if (!$('body').hasClass('quick')) {
				location.reload();
			}
		},
	});
});

function sweepstakeFormValidation($response_form, errors) {
	var visible_errors = 0;
	var $visible_step = $('.sweepstake-page.show', $response_form);

	for (var f in errors) {
		// only visible fields for step form
		if ($('#field-error-' + f, $visible_step).size() > 0) {
			$('#field-error-' + f, $visible_step)
				.html(errors[f])
				.fadeIn();
			$('#field-' + f, $visible_step).addClass('field_error_input');
			$("label[for='field-" + f + "']", $visible_step).addClass('field_error_label');
			visible_errors++;
		}
	}

	if (visible_errors) {
		return false;
	} else {
		$('.sweepstake-page.show').addClass('completed');

		// pager
		var page_index = kontextElement.getIndex();
		var active_page = page_index + 1;

		$('.sweepstake-pager-item' + page_index)
			.removeClass('active')
			.addClass('completed');
		$('.sweepstake-pager-item' + active_page).addClass('active');

		var answer_id = $('input[name^="question_"]:checked', $visible_step).val() || 0;
		var next_question_id = answer_id ? $('[data-next_question_' + answer_id + ']').data('next_question_' + answer_id) || 0 : 0;
		var next_page = next_question_id ? $('[data-question_id="' + next_question_id + '"]').data('question_layer') || 0 : 0;

		if (next_page) {
			kontextElement.goTo(next_page);
			$('[data-question_layer="' + next_page + '"]')
				.find('a.btn-sweepstake-link')
				.attr('data-prev_page', page_index);
		} else {
			kontextElement.goTo(active_page);
			$('[data-question_layer="' + active_page + '"]')
				.find('a.btn-sweepstake-link')
				.attr('data-prev_page', page_index);
			//kontextElement.next();
		}
	}
}

// recaptcha v3
var submitted_form_id = '';
var form = '';
var submitted_form_action = '';

$('[data-before_submit]').click(function () {
	form = $(this).closest('form');
	submitted_form_id = $(this).closest('form').attr('id');
	submitted_form_action = $(this).closest('form').attr('data-main_action');
});

function onSubmit(token) {
	var response = true;

	if (submitted_form_action === 'subscribe') {
		response = cmsnewsletter.subscribe(form);
	}

	if (submitted_form_action === 'comment_form') {
		response = cmsfeedback.addcomment(form);
	}

	if (submitted_form_action === 'notifyme_form') {
		response = cmsfeedback.addnotifyme(form);
	}

	if (response !== false && typeof response !== 'undefined') {
		document.getElementById(submitted_form_id).submit();
	}
}
