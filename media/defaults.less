/*------- mixins -------*/
.transition (@element: color, @speed: .3s) {
	transition: @arguments;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;	
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.text-shadow (@color, @x: 1px, @y: 1px, @blur: 0px) {
	text-shadow: @x @y @blur @color;
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background-image: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	//background-image: -webkit-linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.font(@fontSize, @fontLineHeight, @fontType) {
	font-size: @fontSize;
	line-height: @fontLineHeight; 
	font-family: @fontType;
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.scaleX(@factor) {
	transform: scaleX(@factor);
	-webkit-transform: scaleX(@factor);
	-ms-transform: 		 scaleX(@factor);	
}
.scaleY (@factor) {
	transform: scaleY(@factor);
	-webkit-transform: scaleY(@factor);
	-ms-transform: scaleY(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.skew (@deg, @deg2) {
	transform: skew(@deg, @deg2);
	-webkit-transform: skew(@deg, @deg2);
	-ms-transform: skew(@deg, @deg2);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.perspective (@value: 1000) {
	perspective: 		@value;
	-webkit-perspective: 	@value;
}
.clear { 
	*zoom: 1; clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}
.shadow { content:""; position: absolute; left: 35px; right: 35px; top: 20px; bottom: 5px; box-shadow: 0px 10px 65px rgba(0,0,0,.6); }
.lloader {
	background: #fff url(images/loader.gif) no-repeat center center; background-size: 65px auto;
	img { opacity: 0; .transition(opacity); }
	&.loaded {
		background: #fff;
		img { opacity: 1; }
	}
}
.list{
	list-style: none; padding: 0; margin: 0 0 15px 20px;
	li{
		position: relative; padding: 2px 0 2px 23px;
		&:before{.pseudo(8px, 8px); background: @yellow; border-radius: 200px; top: 10px; left: 0;}
	}
}
/*------- /mixins -------*/



/*
	LEGEND
	screen sizes: 1680, 1400, 1240, 990, 750

	- normalize
	- helpers
	- tables
	- selectors
	- forms
	- info messages
	- buttons
	- navigation
	- header
	- search widget
	- compare widget
	- wishlist widget
	- autocomplete
	- cart widget
	- auth widget
	- main
	- locations
	- contact
	- faq
	- brands widget
	- hero slider
	- hp promo section
	- benefits
	- support - stores
	- miele
	- promo
	- catalog widget
	- catalog
	- catalog post
	- catalog detail
	- toolbar
	- filter
	- brands
	- publish
	- publish post
	- publish detail
	- search
	- wishlist
	- comments
	- sweepstake
	- auth
	- compare
	- share
	- tell friend
	- sidebar
	- breadcrumbs
	- newsletter
	- cart
	- coupon widget
	- orders
	- checkout
	- thank you
	- add to cart modal
	- footer
*/