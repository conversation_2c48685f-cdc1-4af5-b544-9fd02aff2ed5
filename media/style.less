/*------- normalize -------*/
*{margin: 0; padding: 0; border: 0; outline: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; -webkit-font-smoothing: antialiased;}
img{max-width: 100%; height: auto;}
html{font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary{display: block;}
audio, canvas, progress, video{display: inline-block; /* 1 */}
audio:not([controls]){display: none; height: 0;}
ins{font-weight: normal; text-decoration: none;}
progress{vertical-align: baseline;}
[hidden], template{display: none;}
a{background-color: transparent; -webkit-text-decoration-skip: objects; /* 2 */}
a:active, a:hover{outline: 0; -webkit-tap-highlight-color: transparent;}
abbr[title]{border-bottom: 1px dotted;}
b, strong{font-weight: bold;}
dfn{font-style: italic;}
mark{background: #ff0; color: #000;}
small{font-size: 80%;}
sub, sup{font-size: 75%; line-height: 0; position: relative; vertical-align: baseline;}
sup{top: -0.5em;}
sub{bottom: -0.25em;}
svg:not(:root){overflow: hidden;}
hr{box-sizing: border-box; height: 0; border-bottom: 1px solid #ccc; margin-bottom: 10px; border-top-style: none; border-right-style: none; border-left-style: none;}
pre{overflow: auto;}
pre.debug{font-size: 14px !important;}
code, kbd, pre, samp{font-family: monospace, monospace; font-size: 1em;}
button, input, optgroup, select, textarea{color: inherit; /* 1 */ font: inherit; /* 2 */ margin: 0; /* 3 */}
button{overflow: visible;}
button, select{text-transform: none;}
button, html input[type="button"], input[type="reset"], input[type="submit"]{-webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */}
button[disabled], html input[disabled]{cursor: default;}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{border-style: none; padding: 0;}
input{line-height: normal; border-radius: 0; box-shadow: none;}
input[type="checkbox"], input[type="radio"]{box-sizing: border-box; /* 1 */ padding: 0; /* 2 */}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button{height: auto;}
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration{-webkit-appearance: none;}
input[type=text], input[type=email], input[type=password], input[type=tel], input[type=search]{-webkit-appearance: none;}
::-webkit-input-placeholder{color: inherit; opacity: 1;}
fieldset{border: none; margin: 0; padding: 0;}
textarea{overflow: auto; resize: vertical;}
optgroup{font-weight: bold;}
table{border-collapse: collapse; border-spacing: 0;}
input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill{-webkit-box-shadow: 0 0 0 30px #fff inset; box-shadow: 0 0 0 30px #fff inset;}
/*------- /normalize -------*/

@import "defaults.less";

/*------- helpers -------*/
.display-n{display: none;}
.display-ib{display: inline-block;}
.display-b{display: block;}
.display-t{display: table;}
.display-tc{display: table-cell;}
.display-f{display: flex;}
.align-vt{vertical-align: top;}
.align-vm{vertical-align: middle;}
.align-l{text-align: left;}
.align-r{text-align: right;}
.align-c{text-align: center;}
.fz0{font-size: 0;}
.fs-i{font-style: italic;}
.fw-b{font-weight: bold;}
.fw-n{font-weight: normal;}
.float-l, .float-left{float: left;}
.float-r, .float-right{float: right;}
.pos-r{position: relative;}
.pos-a{position: absolute;}
.pos-s{position: static;}
.strong{font-weight: bold;}
.italic{font-style: italic;}
.uppercase{text-transform: uppercase;}
.first{margin-left: 0 !important;}
.last{margin-right: 0 !important;}
.image-left, .alignleft{float: left; margin: 5px 20px 10px 0px;}
.image-right, .alignright{float: right; margin: 5px 0px 10px 20px;}
.align-left{text-align: left;}
.align-right{text-align: right;}
.center{text-align: center;}
.underline{text-decoration: underline;}
.nounderline{text-decoration: none;}
.rounded{border-radius: @borderRadius;}

.red{color: @red;}
.green{color: @green;}
.orange{color: @orange;}
.yellow{color: @yellow;}

@media (max-width: 980px){
	.tel, .tel a, a[href^=tel]{color: @textColor; cursor: default;}
}
.first-title{margin-top: 0; padding-top: 0;}
.image-wrapper{
	margin: 15px 0;
	img{display: block;}
}
.image-title{text-align: center; color: #9B9B9B; font-size: 13px; line-height: 20px; padding: 10px 0 0;}
.wrapper{max-width: @pageWidth; margin: auto;}
.page-wrapper{overflow: hidden;}
.icon{
	position: relative;
	&:before{.icon-pin; font: 15px/15px @fonti; margin-right: 10px; display: inline-block;}
}
.icon-tel:before{.icon-phone;}
.icon-tel2:before{.icon-phone2;}
.icon-mail:before{.icon-envelope;}
.icon-mail2:before{.icon-mail;}
.icon-arrow:before{.icon-arrow-right;}
.icon-reverse:before{.scaleX(-1);}

.extra{font-size: 22px; line-height: 30px;}
.extra2{
	background: @gray; color: #fff; padding: 35px 40px; font-size: 22px; line-height: 32px; margin-bottom: 25px;
	a{
		text-decoration: underline; color: @yellow;
		&:hover{color: @yellow; text-decoration: none;}
	}
}
.extra3{border: 3px solid @yellow; padding: 35px 40px; font-size: 22px; line-height: 32px;}
.testimonial{
	position: relative; margin-bottom: 55px; margin-top: 20px;
	img{position: absolute; left: 0; top: 0; z-index: 0;}
}
.testimonial-cnt{
	background: @gray; color: #fff; text-align: center; margin-left: 230px; top: 20px; padding: 65px 60px 40px; z-index: 1; position: relative; min-height: 240px; overflow: hidden;
	a{color: #fff;}
	&:after{.pseudo(270px,270px); background: @yellow; border-radius: 200px; top: -175px; right: -175px;}
	&:before{.icon-quote; font: 35px/35px @fonti; .scaleY(-1); position: absolute; right: 14px; top: 15px; color: @textColor; z-index: 1;}
}
.testimonial-name{color: @yellow; padding: 0; font-size: 18px; line-height: 23px; padding-top: 5px;}
.testimonial-title{font-size: 13px; text-transform: uppercase; font-weight: bold; padding: 0; line-height: 20px;}

.toggle-icon{
	display: inline-block; width: 13px; height: 13px; position: relative;
	&:after, &:before{.pseudo(100%,3px); left: 0; top: 0; background: @yellow;}
	&:before{width: 3px; height: 100%; left: 5px;}
	&:after{top: 5px;}
}
/*------- /helpers -------*/

/*------- selectors -------*/
body{background: #fff; color: @textColor; .font(@fontSize, @lineHeight, @font);}
a{
	color: @linkColor; text-decoration: underline; .transition();
	&:hover{text-decoration: underline; color: @linkHoverColor;}
}
ul, ol{margin: 0; padding: 0;}
h1, h2, h3, h4{
	font-weight: bold; padding-bottom: 15px; padding-top: 35px; text-transform: uppercase;
	a, a:hover{text-decoration: none;}
}
h1{font-size: 48px; line-height: 51px; text-transform: uppercase; padding-top: 0; color: #000;}
h2{font-size: 36px; line-height: 40px;}
h3{font-size: 30px; line-height: 36px;}
h4{font-size: 24px; line-height: 35px;}
p{padding-bottom: 10px;}
/*------- /selectors -------*/

/*------- forms -------*/
label{padding: 0 0 4px 0; display: inline-block; font-size: 15px; line-height: 20px;}
input, textarea, select{-webkit-appearance: none; padding: 0 20px; border: 1px solid @borderColor; font-size: 12px; height: 50px; font-family: @font; line-height: normal; border-radius: 0; .transition(border-color);}
input:disabled, textarea:disabled, input:disabled+label, .disabled{cursor: default !important; color: #ccc;}
input:hover, textarea:hover, select:hover, input:focus, textarea:focus, select:focus{border-color: darken(@borderColor,30%); outline: 0;}
input[type=submit], button{border: none; display: inline-block;}
input[type=checkbox], input[type=radio]{padding: 0; height: auto; border: none;}
textarea{height: 130px; padding-top: 10px; padding-bottom: 10px; line-height: 19px;}
legend{
	font-size: 16px; line-height: 18px; font-weight: bold;
	a{text-decoration: none;}
}
input[type=checkbox], input[type=radio]{position: absolute; left: -9999px; display: inline;}
input[type=checkbox] + label, input[type=radio] + label{cursor: pointer; position: relative; padding: 3px 0 0 35px; min-height: 24px; line-height: 20px; font-size: 15px; text-align: left;}

input[type=radio] + label{padding: 1px 0 0 33px;}

input[type=checkbox] + label:before{.pseudo(20px, 20px); text-indent: 2px; color: #fff; border: 1px solid @borderColor; left: 0; text-align: center; top: 0; .icon-check; font: 10px/20px @fonti; .transition(all);}
input[type=radio] + label:before{.pseudo(20px, 20px); border-radius: 200px; color: #fff; border: 1px solid @borderColor; left: 0; text-align: center; top: 0; box-sizing: border-box; .transition(all);}

input[type=checkbox]:checked + label:before{color: #000; background: @yellow; border-color: @yellow;}
input[type=radio]:checked + label{
	&:before{background: @yellow; border-color: @yellow;}
	&:after{.pseudo(10px,10px); background: @gray; border-radius: 100px; top: 5px; left: 5px;}
}
select{background: url(images/icons/arrow-down.svg) no-repeat right 20px center; background-size: 11px auto;}

.form-inline{
	font-size: 0;
	p{position: relative;}
	label{display: inline-block;}
	label{width: 140px; font-size: @fontSize; text-align: right; padding-right: 15px;}
	input, input, select, textarea{width: 300px; display: inline-block;}
	.error, .field-note, .auth-links{margin-left: 140px; font-size: @fontSize;}
	.field-newsletter, button, .remember, .field-accept_terms, .field-show-password{margin-left: 140px;}
	.field-newsletter, .remember, .field-accept_terms{
		width: 260px;
		label{text-align: left; width: auto;}
	}
	.field-message label{vertical-align: top;}
	.field-optional{position: absolute; font-size: 10px; top: 0; right: 0;}
}
.form-label{
	p, .field{position: relative; padding-bottom: 10px;}
	.error:not(.global-error){font-weight: 400; font-size: 14px; padding: 5px 0 5px 20px;}
	label{position: absolute; top: 17px; left: 20px; padding: 0; cursor: text; z-index: 10; font-size: 15px; line-height: 20px; font-weight: 400; width: auto; text-align: left; .transition(all);}
	.focus, .ffl-floated{
		label{top: 6px; font-size: 12px; color: #898989;}
		input, select{padding-top: 13px;}
		textarea{padding-top: 25px;}
		select{font-size: 15px;}
	}
	input, select{display: block; width: 100%; height: 54px; line-height: normal; font-size: 15px; padding-left: 20px;}
	select{position: relative; z-index: 20;}
	input[type=checkbox]+label, input[type=radio]+label{position: relative; left: auto; top: auto; font-size: 15px; line-height: 20px; color: @textColor;}
	textarea{display: block; width: 100%; font-size: 15px; height: 180px;}
	input[type=radio]:disabled + label{color: #ccc;}
}
.phone-tooltip{font-size: 12px; line-height: 17px; padding: 5px 0 0 20px; text-transform: uppercase;}
/*------- /forms -------*/

/*------- tables -------*/
.table{
	width: 100%; border-spacing: 0; margin: 10px 0px 20px; font-size: 15px;
	th{font-weight: bold; font-size: 15px; text-align: left; padding: 6px 0; border-bottom: 1px solid @borderColor;}
	td{border-bottom: 1px solid @borderColor; padding: 6px 0;}
	&.stripe tbody tr:nth-child(even){background: #E9E9E9;}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
.table-wrapper{overflow-x: scroll; -webkit-overflow-scrolling: touch;}
/*------- /tables -------*/

/*------- info messages -------*/
.error{color: @errorColor; display: block; padding: 5px 0 0 0; font-size: 14px; line-height: 19px;}
.global-error, .global-success, .global-warning{font-size: 15px; margin: 0 0 15px 0; line-height: 21px; padding: 15px 15px 15px 55px; min-height: 50px; background: @errorColor url(images/icons/danger-white.svg) no-repeat 15px 12px; background-size: 25px auto; color: #fff; position: relative;}
.global-success{background-color: @successColor; background-image: url(images/icons/check.svg); background-size: 25px auto; background-position: 20px center;}
.field_error_input, .field_error_input_radio{border-color: @errorColor; background: #fff url(images/icons/danger.svg) no-repeat right 11px center; background-size: 27px auto; padding-right: 50px;}
/*------- /info messages -------*/

/*------- slick -------*/
.slick-slider{position: relative; display: block; box-sizing: border-box; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; -ms-touch-action: pan-y; touch-action: pan-y; -webkit-tap-highlight-color: transparent; }
.slick-list{
	position: relative; overflow: hidden; display: block; margin: 0; padding: 0;
	&:focus{outline: none; }
	&.dragging{cursor: pointer; cursor: hand; }
}
.slick-slider .slick-track, .slick-slider .slick-list{-webkit-transform: translate3d(0, 0, 0); -moz-transform: translate3d(0, 0, 0); -ms-transform: translate3d(0, 0, 0); -o-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0);}
.slick-track{
	position: relative; left: 0; top: 0; display: block; margin-left: auto; margin-right: auto;
	&:before, &:after {content: ""; display: table; }
	&:after {clear: both; }
	.slick-loading & {visibility: hidden; }
}
.slick-slide {
	float: left; height: 100%; min-height: 1px; display: none;
	/*&>div{width: calc(~"100% + 1px");}*/
	img {display: block; }
	&.slick-loading img {display: none; }
	&.dragging img {pointer-events: none; }
	.slick-initialized & {display: block; }
	.slick-loading & {visibility: hidden; }
	.slick-vertical & {display: block; height: auto; }
}
.slick-arrow{
	position: absolute; top: 40%; z-index: 40; width: 60px; height: 60px; min-width: 0; background: #fff; box-shadow: inset 0 0 0 5px @yellow; font-size: 0; padding: 0; border-radius: 300px;
	&:before{.icon-arrow-right; font: 16px/60px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; color: @textColor; .transition(background-color);}
	&.slick-disabled{
		cursor: default;
		&:before{color: darken(@borderColor,10%)!important;}
	}
}
@media screen and (min-width: 1240px) {
	.slick-arrow:hover{background: @yellow;}
}
.slick-next{
	left: auto; right: 0;
	&:before{text-indent: 2px;}
}
.slick-prev:before{.scaleX(-1);}
.slick-arrow.slick-hidden {display: none;}
.slick-dots{
	list-style: none; padding: 0; margin: 15px 0 0; text-align: center; display: none;
	li{
		display: inline-block; vertical-align: top; position: relative; width: 24px; height: 24px; margin: 0 4px; padding: 0!important;
		&:before{display: none!important;}
	}
	button{background:#fff; border-radius: 100px; padding: 0; min-width: 0; width: 24px; height: 24px; display: block; border: 1px solid @borderColor; text-indent: -99999999px;
		&:hover{background: #fff;}
	}
	.slick-active{
		button:after{.pseudo(12px,12px); background: @yellow; border-radius: 100px; left: 6px; top: 6px;}
	}
}
.slick-carousel{
	.slick-prev{left: -90px;}
	.slick-next{right: -90px;}
	.slick-list{padding: 1px 0 0 1px; margin-left: -1px;}
	.slick-list, .slick-track{display: flex;}
	&.slick-initialized .slick-slide{display: flex;}
}
/*------- /slick -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{
	position: relative; display: inline-flex; align-items: center; justify-content: center; padding: 2px 25px 0; line-height: 19px; text-transform: uppercase; height: 55px; font-size: 18px; color: #fff; background: @gray; font-weight: bold; text-decoration: none; color: @yellow; transition: color .3s, background .3s;
	&:hover{text-decoration: none; color: @yellow;}
}
.btn-yellow{
	background: @yellow; color: @textColor;
	&:hover{color: @textColor;}
}
.btn-medium{height: 40px; padding: 0 20px; font-size: 16px; line-height: 22px; padding-top: 2px;}
.btn-small{height: 30px; font-size: 16px; padding-top: 1px; padding: 0 15px;}
.btn-border{border: 1px solid @gray;}
@media screen and (min-width: 1240px) {
	.btn, input[type=submit], button{
		&:hover{background: darken(@gray,20%);}
	}
	.btn-yellow:hover{background: darken(@yellow,10%);}
}
/*------- /buttons -------*/

/*------- navigation -------*/
.btn-toggle-nav,.m-nav-title{display: none;}
.nav-container{position: absolute; top: 0; left: 235px; top: 9px; display: flex;}
.nav{
	font-size: 0; list-style: none; margin: 0; padding: 7px 0 0; line-height: 18px;
	&>li{display: inline-block; position: relative; font-size: 14px; margin: 0 16px 0 0;}
	a{display: block; color: #000; text-decoration: none;}
	li.has-children{
		&>a span{padding-right: 15px; position: relative;
			&:after{.pseudo(5px, 5px); border-left: 1px solid #000; border-bottom: 1px solid #000; .rotate(-45deg); top: 4px; right: 0;}
		}
	}
	ul{background: #fff; font-size: 13px; position: absolute; left: 0; width: 200px; margin: 0; list-style: none; display: none; opacity: 0; .transition(opacity);}
	ul ul{top: 0; left: 100%;}
	li.active>ul, li:hover>ul{display: block; opacity: 1;}
	li:hover>a, li.selected>a{color: #000; text-decoration: underline;}
	//.item-mobile{display: none;}
}

.nav-sidebar{
	padding: 0; margin: 0; list-style: none;
	li{padding: 0 0 10px;}
	a{
		color: #fff; text-decoration: none;
		&:hover span:after{.scaleX(1);}
	}
	span{
		position: relative;
		&:after{.pseudo(100%,2px); background: @yellow; bottom: 0; .scaleX(0); transform-origin: left; .transition(all);}
	}
	.selected a{color: @yellow;}
}

.nav-categories{
	display: flex; height: 60px; font-size: 18px; text-transform: uppercase; list-style: none; padding: 0; margin: 0; justify-content: space-between; position: absolute; left: 0; bottom: 0; width: 100%; text-align: center; font-weight: bold; background: @gray;
	&>li{
		border-right: 1px solid rgba(255,255,255,0.2); flex-grow: 1; text-align: center; height: 100%; .transition(all);
		&:last-child{border: 0;}
		&.active{
			background: @yellow;
			&>a{color: @gray;}
		}
		&>a{color: @white; text-decoration: none; display: flex; justify-content: center; align-items: center; height: 100%; position: relative; z-index: 1;}
		&:last-child{
			margin-left: -1px; margin-right: -1px; background: @red;
			&.active{background: @red/1.1; color: @white;}
		}
	}
	.nav-home{
		font-size: 0; flex-shrink: 0; flex-grow: 0;
		a{
			width: 70px;
			&:after{.icon-home; font: 18px/20px @fonti; color: @yellow;}
		}
	}

	&>li:last-child{
		position: relative;
		.nav-submenu{column-count: 1; text-align: center;}
		.submenu-container{left: initial; right: -36px; left: -36px; padding: 30px 30px 10px; text-align: center;}
		//.submenu-col1{padding-left: 60px;}
		/*&.nav-category-grijanje_hladenje .submenu-container{width: auto;}*/
	}
}
@media screen and (min-width: 1240px) {
	.nav-categories{
		&>li:hover{
			background: @yellow;
			&>a{color: @gray;}
		}
		&>li:last-child:hover{
			background: @yellow;
			&>a{color: @gray;}
		}
	}
	.nav-home:hover{
		a:after{color: @gray;}
	}
}
@media screen and (min-width: 750px) {
	.nav-categories{
		.has-children{
			&:before{opacity: 0;}
			&>a:before{.pseudo(10px,10px); background: @yellow; opacity: 0; visibility: hidden; .rotate(45deg); left: 50%; margin-left: -3.5px; bottom: -5px; .transition(opacity);}
			&.active{
				&>a:before{opacity: 1; visibility: visible;}
				.submenu-container{opacity: 1; visibility: visible;}
			}
		}
	}
}
.submenu-container{background: #fff; position: absolute; left: 0; right: 0; top: 60px; box-shadow: 0 20px 40px 0 rgba(0,0,0,0.1); padding: 30px 30px 10px 35px; display: flex; visibility: hidden; opacity: 0; .transition(opacity); content-visibility: auto; contain-intrinsic-size: 500px; z-index: -1;}
.nav-submenu{
	list-style: none; padding: 0; margin: 0; font-size: 15px; line-height: 20px; font-weight: normal; text-transform: none; text-align: left; column-count: 3;
	a{
		text-decoration: none;
		&:hover{text-decoration: underline;}
	}
	li{max-width: 210px; width: 100%;}
	&>li{margin: 0 0 20px 0; font-size: 18px; line-height: 21px; padding-right: 10px;}
	&>li>a{font-weight: bold; text-transform: uppercase; color: @textColor; display: inline-block; margin-bottom: 4px; cursor: default; text-decoration: none!important;}
	ul{
		list-style: none; padding: 0; margin: 0; font-size: 15px; line-height: 19px;
		li{
			padding: 3px 0;
			&:last-child{padding-bottom: 0;}
		}
	}
	.red a{color: @red;}
}
//.multicolumn ul{column-count: 2; margin-right: 0;}
.submenu-col1{flex-grow: 1;}
.submenu-col2{
	flex: 0 0 639px; padding-left: 29px; padding-bottom: 20px; position: relative; margin-left: 30px;
	&:before{.pseudo(1px, auto); background: @borderColor; left: 0; top: -30px; bottom: -30px;}
	/* p{padding: 0;}
	img{display: block; max-width: 420px; height: auto; .transition(opacity);}
	a:hover img{opacity: .8;} */
}

.nav-category-smartphone{
	.nav-submenu{
		column-count: 3;
		&>li{
			&:nth-child(2), &:nth-child(3){
				&>ul{break-after: column; padding-bottom: 0;}
			}
		}
	}
}
.nav-category-televizori{
	.nav-submenu{
		column-count: 6;
		&>li{
			&:nth-child(2), &:nth-child(3), &:nth-child(4), &:nth-child(6), &:nth-child(7){
				&>ul{break-after: column; padding-bottom: 0;}
			}
		}
	}
}
.nav-category-informatika{
	.nav-submenu{
		column-count: 6;
		&>li{
			&:nth-child(2n){
				&>ul{break-after: column; padding-bottom: 0;}
			}
		}
	}
}
.nav-category-bijela_tehnika{
	.nav-submenu{
		column-count: 3;
		&>li{
			&:nth-child(n){
				&>ul{break-after: column; padding-bottom: 0;}
			}
		}
	}
}
.nav-category-mali_kucanski_aparati{
	.nav-submenu{
		column-count: 3;
		&>li{
			&:nth-child(2){
				&>ul{break-after: column; padding-bottom: 0;}
			}
		}
	}
}
.nav-category-grijanje_hladenje{
	.nav-submenu{
		column-count: 3;
		&>li{
			&:nth-child(2n){
				&>ul{break-after: column; padding-bottom: 0;}
			}
		}
	}
}

//BESTSELLERS
.nav-bestsellers-items-title{text-transform: initial; display: flex; align-items: center; text-align: left; font-size: 28px; line-height: 34px; font-weight: bold; padding-bottom: 8px;}
.bs-show-more{font-size: 18px; line-height: 20px; font-weight: bold; text-decoration: underline; text-decoration-color: @yellow; text-underline-offset: 3px; margin-left: 16px; text-transform: initial;}
.nav-bestsellers-items{display: flex; flex-flow: column;}
.cp-small{
	display: flex; width: 100%; border: 1px solid @borderColor; margin: -1px 0 0 -1px; padding: 16px; text-transform: initial; position: relative;
	.cp-attr-energy{
		margin: 0; font-size: 12px; padding: 0;
		@media (max-width: @m){margin: -3px 0 0 10px;}
		img{height: 35px; width: auto;}
	}
}
.cp-small-col1{
	flex-shrink: 0; flex-grow: 0; width: 144px; position: relative;
	.cp-badges{top: 0; right: unset; left: 0; max-height: 30px;}
}
.cp-small-col2{flex-grow: 1; padding-left: 16px; text-align: left;}
.cp-small-image{
	height: 100%;
	a{height: 100%; display: flex; align-items: flex-end; justify-content: center;}
	img{max-width: 100%; width: auto; height: auto;}
}
.cp-small-brand{
	position: relative; padding-bottom: 5px;
	img{max-height: 15px; width: auto; height: auto; display: block; .grayscale(); display: block;}
}
.cp-small-title{
	font-size: 16px; line-height: 18px; font-weight: bold; text-transform: uppercase;
	a{text-decoration: none;}
}
.cp-small-attrs{display: none!important;}
.cp-small-code{font-size: 12px; line-height: 16px; padding-top: 4px; font-weight: normal;}
.cp-small-price{
	padding-top: 0!important; min-height: initial!important;
	.cp-small-card-prices{
		margin: 5px 0 0px!important; display: flex; flex-wrap: nowrap; font-size: 12px; line-height: 1.1; font-weight: normal;
		.cp-card-price{
			width: initial; margin-right: 10px; padding-bottom: 0;
			&:after{display: none;}
			.cp-card-discount{width: 30px; font-size: 11px; padding: 4px 0 3px;}
		}
	}
	.cp-price-discount{margin-top: 6px; display: flex; flex-wrap: wrap; align-items: center;}
	.cp-current-price{
		font-size: 24px;
		.formated-price{
			.p-small{text-transform: initial;}
			.p-€{right: 10px;}
		} 
	}
	.cp-price-note{padding-top: 1px; width: 100%;}
	.cp-extra-price-lowest{max-width: 100%; text-transform: initial; padding-top: 2px; font-size: 11px;}
}

.cp-small-col3{
	display: flex; position: absolute; right: 16px; bottom: 16px; width: auto;
	.cp-small-wishlist{
		margin-left: 0;
		&>a{
			&:before{.icon-wishlist_notactive;}
		}
		.wishlist-message{
			left: unset; right: -81px;
			&:after{left: unset; right: 96px;}
			@media (max-width: @m){
				right: unset; left: 0;
				&:after{left: 15px; right: unset;}
			}
		}
	}
	.cp-small-compare{
		width: 40px; height: 40px; flex-shrink: 0; flex-grow: 0; font-size: 0; position: relative;
		.cp-compare-container{
			display: block;
			&>a{
				display: block; height: 100%; border: 1px solid #E0E2DB; position: relative; transition: background-color .3s,border-color .3s; text-decoration: none;
				&:before{.icon-compare_redesign; font: 16px/40px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; .transition(all);}
				@media (min-width: 1300px){
					&:hover{
						border-color: @gray; background: @gray;
						&:before{color: @yellow;}
					}
				}
				&.compare_active{
					border-color: @gray; background: @gray;
					&:before{color: @yellow;}
				}
			}
		}
		.cp-compare-info{
			left: unset; right: -41px;
			&:after{left: unset; right: 55px;}
			@media (max-width: @m){
				right: unset; left: 0;
				&:after{left: 15px; right: unset;}
			}
		}
	}
	.cp-small-add{
		width: 40px; height: 40px; flex-shrink: 0; flex-grow: 0; font-size: 0; position: relative; display: flex; justify-content: center; align-items: center; border: 1px solid #0EAD69; background: #0EAD69; padding: 0!important;
		span{
			padding-left: 0; display: flex; align-items: center; justify-content: center;
			&:before{left: initial; top: initial!important;}
		}
		&:hover{border-color: @gray;}
	}
}
.nc-m-lvl2, .nc-m-lvl3{display: none;}
/*------- /navigation -------*/

/*------- header -------*/
.logo{display: block; width:140px; height:86px; background:url(images/logo.svg) no-repeat left top; position: absolute; top: 17px; left: 34px; background-size: contain;}
.header{position: relative; z-index: 1001;}
.wrapper-header{height: 181px;}
.social{
	display: flex; font-size: 0; margin-left: 15px;
	a{
		width: auto; height: 32px; position: relative; padding-left: 13px; padding-right: 8px; font-size: 14px; line-height: 24px; font-weight: bold; color: @white; background: #3B5998; display: flex; justify-content: center; align-items: center; text-decoration: none;.transition(background);
		&:before{.icon-facebook; font: 12px/12px @fonti; color: @white; .transition(color); margin-right: 21px;}
		&:after{.pseudo(1px, 100%); background: rgba(255,255,255,0.2); left: 31px; top: 0;}
		/* &:hover{
			background: @gray;
			&:before{ color: #fff;}
		} */
	}
	//.in:before{.icon-instagram; font-size: 15px; line-height: 15px;}
}
.top-contact{
	position: absolute; right: 195px; width: 270px; top: 16px; font-size: 13px; line-height: 18px; font-weight: 300;
	a{
		text-decoration: none; font-weight: bold; font-size: 14px;
		&:last-child{text-decoration: underline; text-decoration-color: @yellow; text-underline-offset: 4px;}
	}
}
.top-container{display: flex; top: 50px; position: absolute; right: 0; z-index: 10;}
.top-flyer{
	border: 1px solid @borderColor; height: 55px; font-size: 14px; line-height: 14px; text-decoration: none; margin-right: 20px;
	img{display: none;}
	p{padding: 0; height: 100%;}
	a{
		text-decoration: none; position: relative; padding: 14px 23px 0 51px; display: block; height: 100%; .transition(color);
		&:before{.pseudo(26px,35px); background: url(images/icons/pdf.svg) no-repeat left top; background-size: contain; position: absolute; left: 11px; top: 10px;}
		&:hover{color: #000;}
	}
	span{text-transform: uppercase; display: block; font-size: 12px;}
}

.p-ticker{
	background: @gray; color: #fff; box-shadow: 0 10px 20px 0 rgba(0,0,0,0.3); width: 1340px; margin: auto; height: 40px; font-size: 14px; padding: 8px 20px 0; overflow: hidden; position: relative; z-index: 10;
	&:not(.stop) .ticker-items{animation: marquee 30s linear infinite;}
}
.ticker-items{
	list-style: none; padding: 0; margin: 0; display: flex; white-space: nowrap; justify-content: center; flex-grow: 0; flex-shrink: 0;
	li{
		position: relative; padding: 0 18px 0 0; margin-right: 8px;
		&:after{.pseudo(8px,8px); background: @yellow; border-radius: 100px; top: 8px; right: 0;}
		&:last-child:after{display: none;}
	}
	a{
		color: #fff; text-decoration: none;
		&:hover{text-decoration: underline;}
	}
}
/*------- /header -------*/

/*------- compare widget -------*/
.cw-compare{
	width: 55px; height: 55px; border: 1px solid @borderColor; margin-left: -1px; font-size: 0;
	a{
		display: block; position: relative; .transition(background-color); height: 100%; text-decoration: none;
		&:before{.icon-compare_redesign; font: 22px/55px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; .transition(color);}
	}
	&.active{
		.cw-compare-counter{display: initial;}
	}
}
@media screen and (min-width: 1240px) {
	.cw-compare a:hover{
		background: @gray; color: @yellow;
		&:before{color: @yellow;}
	}
}
.cw-compare-counter{font-size: 12px; position: absolute; top: 4px; right: 9px; .transition(color); display: none;}
/*------- /compare widget -------*/

/*------- search widget -------*/
.sw{
	height: 55px; width: 640px; top: 50px; left: 235px; position: absolute; color: #333; border: 1px solid @borderColor; background: #fff;
	form{position: absolute; left: 0; right: 0;}
	&:before{.icon-search; font: 18px/18px @fonti; color: @yellow; position: absolute; top: 18px; left: 16px;}
	&.active{
		&:before{display: none;}
		.sw-list-container{display: block;}
		.sw-toggle{
			display: block;
			&:after{opacity: 1; visibility: visible;}
		}
	}
}
.sw-form{
	position: absolute; width: 100%;
	.ui-autocomplete{max-height: none;}
}
.sw-input{
	height: 53px; border: none; width: 100%; font-size: 18px; padding: 0 110px 0 45px; position: absolute; background: transparent; .placeholder(@textColor, @borderColor);
	&::-webkit-search-cancel-button{display: none;}
}
.sw button{
	display: block; background: @yellow; padding: 0; width: 100px; font-size: 18px; line-height: 20px; height: 55px; top: -1px; right: -1px; position: absolute; z-index: 1; display: flex; justify-content: center; align-items: center; color: @textColor; text-transform: uppercase; .transition(all);
	&:hover{background: darken(@yellow,10%);}
}
.sw-toggle{
	position: absolute; top: 0; left: 0; width: 20px; height: 100%; z-index: 2; display: none; left: 19px;
	&:after{.pseudo(16px,16px); .icon-close; font: 16px/16px @fonti; color: @red; top: calc(~"50% - 10px"); left: calc(~"50% - 10px"); opacity: 0; visibility: hidden; .transition(color);}
}


.sw-list-container{position: absolute; background: @white; top: 54px; padding: 24px 45px 25px; box-shadow: 0 0 40px 0 rgba(0,0,0,0.2); right: 0; left: 0px; display: none; z-index: 10;}
.sw-list-title{
	display: block; font-size: 16px; line-height: 18px; font-weight: bold; margin-bottom: 8px;
	p{padding-bottom: 0;}
}
.sw-list{
	position: relative; display: block; column-count: 3; list-style: none; padding: 0; margin: 0;
	li{display: block; padding: 0 0 5px;}
	a{
		display: inline-block; font-size: 16px; line-height: 22px; text-decoration-color: @yellow; text-underline-offset: 3px;
		&:hover{text-decoration: none;}
	}
}
/*------- /search widget -------*/

/*------- autocomplete -------*/
.autocomplete-container{position: absolute; top: 53px; right: 0; left: 0; box-shadow: 0px 0px 25px rgba(0, 0, 0, .25); z-index: 1000;}
.ui-autocomplete{
	background: #fff; list-style: none; width: auto !important; left: 0px !important; right: 0px !important; padding: 0; margin: 0; max-height: 220px; font-size: 15px; z-index: 550 !important; top: 0 !important; overflow: auto; padding: 0; margin: 0;
	li{
		cursor: pointer; color: @textColor; border-top: 1px solid @borderColor; .transition(background-color); justify-content: center; align-items: center; padding: 0;
		&:before{display: none!important;}
	}
	li:first-child{border: none;}
	li:hover, .ui-state-focus{background: #f3f3f3;}
	a{
		display: flex; .clear; color: @textColor; text-decoration: none; line-height: 17px; padding: 8px 20px; background: none;
		&:hover{color: @textColor;}
	}
	.search-title{display: block; font-weight: bold; padding: 0 0 3px 0;}
	.search-price{display: block; padding-top: 2px;}
	.image{overflow: hidden; display: flex; justify-content: center; align-items: center; background: #fff; width: 60px; height: 60px; line-height: 56px; margin: 0 15px 0 0; text-align: center; border: 1px solid @borderColor; flex-grow: 0; flex-shrink: 0;}
	img{display: inline-block; vertical-align: middle; max-width: 98%; max-height: 98%; width: auto; height: auto;}
	/* li.autocomplete-showall{
		text-align: center; padding: 5px;
		a{.btn; width: 100%;}
	} */
	.search-col2{flex-grow: 1; align-self: center;}
}
.field-autocomplete input{background: #fff url(/media/images/icons/arrow-down.svg) no-repeat right 20px center; background-size: 9px auto;}

body.active-autocomplete{
	.sw .sw-list-container{display: none;}
}

.sw-autocomplete-container{
	position: absolute; top: 54px; right: -210px; background: #fff; left: 0px; box-shadow: 0px 0px 25px rgba(0, 0, 0, .25); z-index: 1000;
	&>.ui-autocomplete{overflow: hidden!important; height: 0px!important;}
	.ui-autocomplete{
		max-height: none!important; background: none!important;
		li{cursor: pointer; color: @textColor; border-top: none; .transition(background-color);}
		a{
			display: block; color: @textColor; text-decoration: none;
			&:hover{text-decoration: none;}
		}
	}
}

.autocomplete-wrapper{width: 100%; display: flex; box-shadow: 0 0px 40px 0 rgba(0,0,0,0.1);}
.autocomplete-title{font-size: 16px; line-height: 18px; font-weight: bold; padding-bottom: 1px;}
.autocomplete-col1{
	width: 408px; flex-grow: 0; flex-shrink: 0; border-right: 1px solid @borderColor;
	.ui-autocomplete{padding-bottom: 0;}
}
.catalogproduct{
	.ui-menu-item{
		position: relative; display: block; width: 100%;
		a{position: relative; display: flex; cursor: pointer; text-decoration: none; padding: 12px 32px; background: none;}
		&:first-child{
			a{padding-top: 24px;}
		}
		.image{
			overflow: hidden; background: #fff; display: flex; width: 64px; height: 64px; line-height: 64px; align-items: center; justify-content: center; text-align: center; flex-grow: 0; flex-shrink: 0; margin: 0 15px 0 0;
			img{display: inline-block; vertical-align: middle; width: auto; height: auto; max-width: 100%; max-height: 100%;}
		}
		.search-cnt{flex-grow: 1; display: flex; flex-flow: column;}
		.search-title{display: block; font-weight: bold; font-size: 14px; line-height: 18px; text-transform: uppercase;}
		.search-code{padding-top: 2px; display: block; font-size: 12px; line-height: 16px;}
		.search-price{display: block; padding-top: 2px; font-size: 14px; line-height: 16px; margin-top: auto;}
		&:hover{
			a{background: #f4f4f4;}
		}
	}
	.autocomplete-showall{
		padding: 12px 32px 32px;
		a{display: block; width: 100%; text-align: center; padding: 16px 15px; background: @gray; color: @yellow; text-transform: uppercase; font-size: 18px; line-height: 20px; cursor: pointer; .transition(all);}
	}
}
.autocomplete-col2{
	flex-grow: 1; display: flex; flex-flow: column;
	/* .ui-autocomplete{
		font-size: 15px;
		a{padding: 2px 0;}
	} */
}
.autocomplete-col-row:not(.autocomplete-col-publish){
	padding: 24px 32px 32px; border-bottom: 1px solid @borderColor;
	.ui-autocomplete{
		position: relative; display: flex; flex-wrap: wrap; width: 100%; list-style: none; margin: 0; padding: 0; font-weight: normal;
		li{
			display: block; margin: 8px 8px 0 0; width: auto;
			&:hover{
				a{background: @gray; border-color: @gray; color: @yellow;}
			}
		}
		a{text-decoration: none; display: flex; border: 1px solid @borderColor; align-items: center; justify-content: center; height: 32px; padding: 0 10px; font-size: 14px; line-height: 16px; color: @gray; white-space: pre-wrap; .transition(all);}
	}
}

.autocomplete-col-publish{
	padding: 24px 32px 25px;
	.autocomplete-title{padding-bottom: 8px;}
	.ui-autocomplete{
		position: relative; display: flex; flex-flow: column; width: 100%; list-style: none; margin: 0; padding: 0; font-weight: normal;
		li{
			display: block; margin-bottom: 7px;
			a{
				text-decoration: none; font-size: 16px; line-height: 22px; display: inline-block; padding: 0;
			}
			&:hover a{text-decoration: underline; text-decoration-color: @yellow; color: @textColor; text-underline-offset: 2px;}
		}
	}
}
/*------- /autocomplete -------*/

/*------- cart widget -------*/
.ww{
	margin-left: 20px; width: 170px;
	&:hover .ww-preview{display: block;}
}
@media screen and (min-width: 750px) {
	.ww.active{
		width: 170px;
		.ww-items{
			background: #0EAD69; color: #fff; font-size: 14px; line-height: 1.2; padding: 0 32px 0 52px; border: none; justify-content: center; align-items: flex-start; flex-flow: column;
			&:before{.pseudo(32px,55px); background: #249E5C; .icon-arrow-down; font: 8px/55px @fonti; right: 0; top: 0; text-align: center; color: @white;}
			&:after{color: @white; .icon-cart_active; font-size: 21px; line-height: 41px;}
			.value{display: initial;}
		}
		.cart-empty{display: none;}
		.ww-counter{display: block;}
	}
}
.ww-preview-items{
	max-height: 448px; overflow: auto;
	&::-webkit-scrollbar { -webkit-appearance: none; width: 5px;  background: @borderColor; }
	&::-webkit-scrollbar-thumb {
		background-color: darken(@borderColor,30%); border-radius: 5px;
		box-shadow: 0 0 1px rgba(255,255,255,.5);
	}
}
.ww-items{
	display: flex; align-items: center; justify-content: center; width: auto; position: relative; font-size: 14px; line-height: 18px; height: 55px; border: 1px solid @borderColor; color: @gray; text-decoration: none; padding: 0 15px 0 48px;
	&:after{position: absolute; .icon-cart_notactive; color: @gray; top: 7px; left: 17px; text-align: center; font: 21px/50px @fonti; top: 0;}
	.value{font-weight: bold; display: none;}
	&:hover{text-decoration: none;}
	ins{display: none!important;}
}
.ww-counter-label{color: @yellow;}
.ww-counter{display: none; text-transform: uppercase; width: 50px; font-size: 10px; line-height: 13px; font-weight: normal;}
.ww-preview{
	display: none; position: absolute; top: 55px; background: #fff; width: 360px; box-shadow: 0px 0px 25px rgba(0, 0, 0, .2); right: 0; font-size: 15px; border-radius: @borderRadius;
	//&:before{.pseudo(10px, 10px); background: #fff; .rotate(45deg); right: 150px; top: -3px;}
}
.ww-totals-cnt{box-shadow: 0 -2px 15px 0 rgba(0, 0, 0, 0.15);}
.ww-preview-footer{padding: 0 16px 6px;}
.ww-preview-btns{
	display: flex;
	.btn{width: 100%; font-size: 18px; padding: 0; background: #0EAD69; color: @white;}
}
.ww-totals{
	font-size: 14px; line-height: 19px; padding: 10px 16px 10px 88px; text-align: right;
	&>div{display: flex; justify-content: space-between;}
}
.ww-total{font-weight: bold; font-size: 16px;}
.ww-preview-note{text-align: center; font-size: 13px; margin-top: 20px; margin-bottom: 5px;}
.ww-preview-note-label{
	position: relative; padding: 0 0 0 35px;
	&:before{.icon-box; font: 25px/25px @fonti; color: @yellow; position: absolute; left: 0; top: -6px;}
}

.free-delivery-missing{display: flex; align-items: center; position: relative; width: 100%; border: 1px solid @borderColor; height: 36px; padding: 1px;}
.free-delivery-missing-bg{position: relative; display: block; height: 100%; background: @yellow; .transition(width);}
.free-delivery-missing-num{position: absolute; left: 0; right: 0; top: 0; bottom: 0; display: flex; align-items: center; justify-content: center; font-size: 15px; line-height: 19px; font-weight: 600;}
.ww-preview-free-delivery{text-align: center; font-size: 13px; line-height: 17px; padding: 10px 0 0 0; margin-bottom: 0;}

.wwp{display: flex; font-size: 12px; line-height: 19px; align-items: center; border-bottom: 1px solid @borderColor; padding: 15px 20px; min-height: 115px; align-items: flex-start;}
.wwp-col1, .wwp-col3{flex-grow: 0; flex-shrink: 0;}
.wwp-col1{width: 60px; position: relative;}
.wwp-col2{flex-grow: 1; padding-left: 10px;}
.wwp-remove{
	font-size: 0; line-height: 0; display: flex; align-items: center; justify-content: center; position: absolute; left: 0; top: 0; background: url(images/wwp-close.svg) no-repeat; width: 20px; height: 20px; background-size: contain;
}
.wwp-image{
	text-align: center;
	img{display: block; max-width: 100%; height: auto; margin: auto; width: auto;}
}
.wwp-title{
	font-size: 15px; line-height: 18px; font-weight: bold; padding-bottom: 3px;
	a{
		text-decoration: none; color: @textColor;
		&:hover{color: #000;}
	}
}
.wwp-cnt{display: flex;}
.wwp-cnt-col1{flex-grow: 1;}
.wwp-price{
	font-size: 12px; line-height: 21px; width: 95px; flex-shrink: 0; text-align: right;
	.formated-price{line-height: 24px;}
}
.wwp-price-count{
	line-height: 1.2;
	span span{display: block;}
}
.wwp-price-old{text-decoration: line-through; font-size: 13px; display: block;}
.wwp-price-current{
	font-size: 22px; font-weight: bold;
	&>span{display: block;}
}
.wwp-price-note{font-size: 11px; line-height: 11px; padding-top: 3px;}
.wwp-quick{
	padding: 16px 16px 10px; min-height: 112px; line-height: 16px;
	.wwp-col1{width: 56px;}
	.wwp-col2{padding-left: 16px;}
	.wwp-title{text-transform: uppercase; font-size: 14px; padding-bottom: 2px;}
	
	.wwp-cnt{
		flex-flow: column;
		.wwp-price{
			width: 100%; text-align: left;
			ins{color: #E3E5E2;}
		}
		.wwp-price-current{
			font-size: 16px; font-weight: normal;
			&>span{display: initial;}
		}
	}
	.wwp-price{display: flex; flex-wrap: wrap; align-items: center; margin-top: 5px;}
}
.wwp-lowest-price{font-size: 10px; line-height: 12px; color: #888788; padding-top: 2px; width: 100%;}
/*------- /cart widget -------*/

/*------- auth widget -------*/
.aw{
	width: auto; height: auto; right: 0; position: absolute; top: 9px;
	&.active{
		.aw-dropdown-list{display: flex !important; flex-flow: column;}
		/* .aw-btn{
			span:before{.icon-auth-active; color: @black;}
			span:after{visibility: visible; opacity: 1; z-index: 101;}
		} */
	}
}
.aw-btn{
	border: 1px solid @borderColor; background: @white; text-decoration: none; height: 32px; width: auto; padding-right: 10px; display: flex; align-items: center; justify-content: center; font-size: 14px; line-height: 16px;
	span{
		position: relative; padding-left: 36px;
		&:before{position: absolute; display: block; top: -2px; left: 10px; font: 18px/18px @fonti; .icon-user_notactive; color: @gray;}
	}
	&:hover{
		text-decoration: none;
	}
	&.aw-btn-loggedin{
		background: @gray; border-color: @gray; color: @yellow;
		span:before{color: @yellow; .icon-user_active;}
	}
}

.aw-dropdown-list{
	display: none !important; position: absolute; right: -30px; box-shadow: 0 0 40px 0 rgba(0,0,0,0.1); background: @white; width: 208px; right: -52px; padding: 24px; font-size: 15px; line-height: 20px; z-index: 1000;
	a{
		padding-bottom: 5px; display: inline-flex; justify-content: center; text-decoration: none;
		&:last-child{padding-bottom: 0;}
	}
	a:hover{text-decoration: underline;}
}
.aw-login{
	right: 0; width: 360px; padding: 24px 32px 32px;
	.field-remember{
		margin-top: 5px;
		input[type=checkbox]+label{font-size: 14px; padding-left: 32px;}
		input[type=checkbox]:checked+label:before{color: @yellow; background: @gray; border-color: @gray;}
	}
}
.lr-btn-pw{
	display: flex; align-items: center; padding-top: 6px;
	.btn{margin: 0; width: auto; padding: 0 33px;}
}
.quick-auth-links{
	flex-grow: 1; text-align: center; font-size: 14px; line-height: 16px;
	a:hover{text-decoration: none;}
}
.aw-quick-login-title{padding-bottom: 12px; position: relative; display: flex; align-items: center; font-weight: bold; font-size: 20px; line-height: 25px;}
.aw-footer{margin-top: 38px; display: flex; flex-flow: column;}
/*------- /auth widget -------*/

/*------- wishlist widget -------*/
#view_wishlist{overflow: visible!important;}
.wishlist{
	border: 1px solid @borderColor; width: 55px; height: 55px; position: relative;
	a{
		display: block; width: 100%; height: 100%; text-decoration: none; position: relative; .transition(background-color);
		&:before{.icon-wishlist_notactive; font: 20px/55px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; .transition(color);}
	}
	&.active{
		a:before{.icon-wishlist_active;}
		.wishlist-counter{display: initial;}
	}
}
@media screen and (min-width: 1240px) {
	.wishlist a:hover{
		background: @gray; color: @yellow;
		&:before{color: @yellow;}
	}
}
.wishlists-title{flex-grow: 1;}
.wishlist-counter{position: absolute; top: 8px; right: 9px; font-size: 12px; line-height: 15px; .transition(color); display: none;}
.btn-wishslit-delete{
	background: none; color: @red; font-size: 16px; position: absolute; right: 0; top: -90px;
	span{
		position: relative; padding: 0 0 0 28px;
		&:before{.icon-bin; font: 19px/19px @fonti; position: absolute; left: 0; top: -2px;}
	}
}
.wishlist-header{display: flex; margin: 60px 0 30px 25px;}
.wishlist-items .cp{width: calc(~"100% / 5 - -1px");}
.wishlist-empty{padding: 0 0 50px;}
#view_wishlist{padding: 1px; position: relative;}
/*------- /wishlist widget -------*/

/*------- main -------*/
.corner-info{
	position: relative; color: @textColor; width: 225px; height: 250px; position: absolute; left: 0; top: 0;
	&:before{.pseudo(560px,560px); background: @yellow; border-radius: 100%; right: 0; bottom: 0;}
}
.corner-info-cnt{
	position: relative; padding: 18px 0 15px 23px; font-size: 13px; line-height: 20px; color: #000;
	p:first-child{
		font-size: 36px; text-indent: -1px; font-weight: 300; line-height: 40px; padding: 0 0 5px;
		strong{font-size: 40px;}
	}
	p:last-child{max-width: 120px;}
}

.main{margin-top: 50px;}
.main-wrapper{
	display: flex; width: 1340px; margin: auto; padding: 0 0 80px;
}
.sidebar{width: 330px; flex-grow: 0; flex-shrink: 0;}
.main-content{width: 100%;}
.cms-content{
	max-width: 760px; margin-left: 80px;
	img{max-width: 100%; height: auto;}
	ul:not(.nav-locations){.list;}
	ol{
		margin: 0 0 0 38px;
		li{padding: 2px 0;}
	}
}

.page-wide{
	.main-wrapper{display: block;}
}
.page-flyer{
	h1{text-transform: none; padding: 0 0 10px 0;}
}
.page-subtitle{font-size: 22px; line-height: 28px; text-transform: uppercase; font-weight: bold;}
.flyer-content{
	background: @gray; margin-top: 20px;
	iframe{width: 100%; display: block; min-height: 500px;}
	p{padding: 0;}
}
.flyer-download{
	a{text-decoration: none; display: block; background: @yellow; height: 55px; display: flex; float: right; align-items: center; font-size: 19px; line-height: 21px; font-weight: bold; padding: 3px 30px 0 85px; position: relative; .transition(all);
		&:after{.pseudo(30px,45px); background: url(images/icons/pdf-white.svg) no-repeat left top; background-size: contain; left: 13px; top: 7px;}
		&:before{.pseudo(55px,55px); background: @gray; left: 0; top: 0;}
		&:hover{background: darken(@yellow,10%);}
	}
	p{padding: 0;}
}
/*------- /main -------*/

/*------- cart -------*/
.w-title{padding-top: 20px; padding-bottom: 25px;}
.wrapper-cart{width: 1200px; margin: auto;}
.w-table{margin-bottom: 25px; border-bottom: 1px solid @borderColor;}
.w-counter{
	font-size: 27px; line-height: 36px; padding-left: 12px; display: none; color: @yellow;
	&.active{display: inline;}
}
.w-cart{display: flex; width: 100%;}
.w-col1{flex-grow: 1; padding-right: 90px;}
.w-col2{width: 460px; flex-grow: 0; flex-shrink: 0;}
.w-totals{position: relative; background: @gray; color: #fff; padding: 40px; font-size: 17px; line-height: 28px; margin-bottom: 10px;}
.w-totals-title{padding: 0 0 12px 0; font-size: 22px; line-height: 28px; font-weight: bold; text-transform: uppercase;}
.w-totals-value{float: right;}
.w-free-shipping{
	padding: 27px 0 0 0; max-width: 100%; font-size: 14px; text-align: center;
	&:before{display: inline-block; vertical-align: top; font-size: 24px; margin: 0 13px 0 0;}
	span{font-weight: bold;}
}
.cart-total{font-size: 20px; font-weight: normal; color: @yellow; font-weight: bold;}
.w-btn-finish{margin: 24px 0 0 0; width: 100%;}
.w-shipping-remaining{margin-top: 27px;}

.wp{display: flex; width: 100%; border-top: 1px solid @borderColor; font-size: 12px; line-height: 19px; align-self: center; padding: 20px 0;
	&>div{align-self: center;}
}
.wp-row-col2{flex-grow: 1; padding-left: 20px;}
.wp-image{
	width: 115px; text-align: center; flex-grow: 0; flex-shrink: 0;
	img{width: auto; height: auto; max-width: 100%; display: block; margin: auto;}
}
.wp-cnt{display: flex; width: 100%;}
.wp-btn-delete{
	font-size: 12px; position: relative; padding-left: 24px; line-height: 13px; text-transform: uppercase; display: inline-block; vertical-align: top; text-decoration: none; color: @textColor; margin: 28px 0 0;
	&:before{left: 0; top:-5px; .icon-bin; color: @textColor; position: absolute; font: 18px/20px @fonti; .transition(opacity);}
	&:hover{
		text-decoration: none; color: @red;
		&:before{color: @red;}
	}
}
.wp-btn-delete-m{display: none;}
.wp-title{
	font-weight: bold; padding: 0 0 5px 0; font-size: 17px; line-height: 18px; text-transform: none;
	a{
		text-decoration: none; color: @textColor;
		&:hover{color: #000;}
	}
}
.wp-message{
	font-size: 12px; display: block; line-height: 15px; top: 64px; background: #fff; left: -30px; right: -30px; color: @green; position: absolute; text-align: center;
	&.product_message_response_error{color: @red;}
}
.wp-total{
	text-align: right; padding-top: 18px; display: flex; flex-grow: 1;
	.formated-price{font-size: 30px; font-weight: bold;}
}
.wp-price-current>span{display: block; padding-bottom: 4px;}
.wp-qty-container{width: 127px; flex-grow: 0; flex-shrink: 0; justify-content: center; padding: 10px 0 0;}
.wp-energy{
	margin-left: 15px; margin-top: -13px;
	@media (max-width: @m){margin: 10px 0 0;}
}
.wp-attr-energy-link{
	text-decoration-color: @yellow; text-underline-offset: 4px;
	&:hover{text-decoration-color: @yellow;}
}
.wp-qty{margin-left: 0; font-size: 0; flex-grow: 1; margin: auto; position: relative;}
.wp-attrs{
	flex-grow: 0; padding: 10px 0 0; width: 280px;
	@media (max-width: @t){width: 120px;}
}
.wp-price-old{height: 20px; text-decoration: line-through;}

.empty-cart{width: 100%; text-align: center; padding: 40px 0 0 0px;}

.ww-shipping{margin-bottom: 25px;}
.payment-note{
	font-size: 15px; line-height: 24px; padding: 0 20px;
	p{padding: 0 0 5px;}
	ul{.list;}
}

.qty-input{width: 42px; height: 42px; border: 1px solid @borderColor; color: @textColor; font-size: 16px; padding: 0; text-align: center; font-weight: bold;}
.btn-qty{
	display: inline-block; vertical-align: top; width: 42px; height: 42px; position: relative;
	&:before, &:after{.pseudo(15px,3px); background: @yellow; left: 15px; top: 20px; .transition(all);}
	&:after{width: 3px; height: 15px; top: 14px; left: 21px;}
}
.btn-qty-dec:after{display: none;}
.wp-unit{text-transform: uppercase; text-align: center; font-size: 10px; line-height: 15px; padding: 4px 0 0; display: block;}
.wp-qty-count{line-height: 1.1;}
/*------- /cart -------*/

/*------- coupon widget -------*/
.wc-btn-toggle{display: none;}
.ww-coupons{
	float: right; text-align: right;
	&.active{
		.ww-coupons-form, .ww-coupons-list{display: none;}
		.ww-coupons-active{display: block;}
	}
}
.ww-coupons-label{font-size: 17px; font-weight: bold; padding: 17px 15px 0 0; line-height: 20px; display: inline-block; vertical-align: top;}
.ww-coupons-add{
	position: relative; z-index: 5; display: inline-block; width: 370px;
	input{width: 100%; height: 50px; padding: 0px 130px 0 55px; font-size: 15px; font-family: @font; .placeholder(@textColor, darken(@borderColor,10%));}
	&.active{display: none;}
	&:before{position: absolute; .icon-discount; font: 22px/22px @fonti; left: 20px; top: 14px; color: @yellow; z-index: 1;}
}
.ww-btn-add{padding: 0 !important; width: 115px; min-width: 0; position: absolute !important; top: 0; right: 0; height: 50px; font-size: 16px; color: @yellow;
}
.coupon_message, .coupon_message_second{background: #fff; display: block; position: relative; text-align: left; margin: 8px 0 0 0; font-size: 13px; line-height: 20px;}
.coupon_message_response_ok{color: @green;}
.coupon_message_response_error{color: @red;}
.ww-coupons-active{display: none; font-size: 17px;}
.ww-coupon-delete{
	text-decoration: none; font-weight: bold; margin-left: 10px; padding-left: 21px; position: relative; color: @red;
	&:before{position: absolute; .icon-bin; font: 16px/16px @fonti; color: @red; left: 0; top: 2px;}
	&:hover{
		text-decoration: none; color: @textColor;
		&:before{color: @textColor;}
	}
}
.ww-coupons-list{width: 370px; text-align: left; float: right; margin: 11px 0 0 0;}
.ww-coupons-title span{font-weight: bold;}
.ww-coupons-table{
	width: 100%;
	td{font-size: 14px; padding: 7px 0; border-top: 1px solid @borderColor; font-size: 14px;}
	.col-code{width: 120px;}
	.col-type{width: 150px; text-align: center;}
	.col-link{
		text-align: right; font-weight: bold;
		a{text-decoration: none;}
	}
}
.ww-coupons-table-header{text-transform: uppercase; font-weight: bold;}
.ww-coupons-list-title{font-weight: bold; padding: 0 0 5px; font-size: 14px;}
.btn-coupon-remove{display: none!important;}
.cart_info_total_extra_coupon_code{font-weight: bold;}
.auth-have-coupon.active{display: none;}

.ww-auth-coupons{width: 100%; text-align: left; position: relative; margin: 0px;}
.ww-auth-coupons-form{position: absolute; top: -60px; right: 0;}
.ww-auth-coupons-list{padding-left: 0; width: 100%; line-height: 21px; margin-top: 0;}
.ww-auth-coupons-table{
	border-bottom: 1px solid @borderColor;
	td{padding-top: 15px; padding-bottom: 15px; font-size: 17px;}
	.col-code{width: 200px; font-weight: bold;}
	.col-value{width: 180px; text-align: center; font-weight: bold;}
	.col-valid{width: 180px;}
	.col-description{padding-right: 20px;}
	.col-valid{text-align: right;}
}
/*------- /coupon widget -------*/

/*------- locations -------*/
.l-items{padding-top: 60px;}
.nav-locations{
	list-style: none; padding: 15px 0 0 0; margin: 0; display: flex; flex-wrap: wrap;
	li{flex-grow: 0; flex-shrink: 0; width: 25%; padding: 0 0 13px;}
	a{
		position: relative; padding: 5px 0 5px 30px; text-decoration: none; display: inline-block; vertical-align: top;
		&:before{.pseudo(21px,30px); left: 0; top: 1px; background: url(images/icons/pin1.svg) no-repeat left top; background-size: contain;}
		&:hover{text-decoration: underline;}
	}
}
.lp{
	display: flex; flex-wrap: wrap; font-size: 16px; line-height: 22px; margin-bottom: 50px;
	&:last-child{margin-bottom: 0;}
}
.lp-title{width: 100%; font-size: 30px; line-height: 34px; padding-top: 0;}
.lp-col{width: 50%; flex-grow: 1;}
.lp-col1{flex-grow: 0; flex-shrink: 0;}
.lp-col2{padding: 15px 0 0 45px; line-height: 20px;}
.lp-images{
	display: none;
	.slick-arrow{
		width: 40px; height: 40px; top: 43%;
		&:before{font-size: 13px; line-height: 40px; .scaleX(-1);}
	}
	.slick-prev{left: -20px;}
	.slick-next{
		right: -20px;
		&:before{.scaleX(1);}
	}
	&.slick-initialized{display: block;}
}
.lp-contact{padding-top: 10px;}
.lp-hours{padding-top: 10px;}
.map{height: 450px; margin-top: 20px;}
.gm-style-iw{width: 100% !important;}
.infoBox{
	width: 290px; border-radius: 3px; position: relative; margin: 15px 0 0 0px; font-size: 14px; line-height: 19px; padding: 20px;
	&>br{display: none;}
	&>img{position: absolute !important; top: 30px; right: 30px; margin: 0 !important; z-index: 10;}
	.image img{max-width: 100%; height: auto;}
	.address, .contact, .business-hour, .title{display: block; width: 100%; padding: 0 0 10px;}
	.business-hour{padding-bottom: 0;}
	.contact{
		a{
			text-decoration: none; margin: 3px 0;
			span{padding-left: 25px;}
		}
	}
	.title{font-weight: bold; text-transform: uppercase; padding: 0 0 6px;}
}
.lp-hours-title{font-weight: bold; padding: 0 0 2px;}
.infoBox-cnt{
	font-size: 13px; display: block; background: @gray; color: #fff; position: relative;
	&:before{.pseudo(10px,10px); background: @gray; .rotate(45deg); left: -4px; top: 30px; z-index: 1;}
	.image{display: block;}
	img{display: block; border-top-left-radius: 3px; border-top-right-radius: 3px; max-width: 100%; height: auto; position: relative; z-index: 5;}
	a, a[href^=tel]{text-decoration: none; color: #fff;}
}
.infoBox-body{padding: 20px 25px; display: block;}
/*------- /locations -------*/

/*------- add to cart modal -------*/
.product-message-modal{
	position: fixed; bottom: 0; top: 0; right: 0; background: rgba(0, 0, 0, .5); opacity: 0; z-index: -40; left: -99999px;
	&.active{opacity: 1; z-index: 99999999; left: 0;}
}
.modal-box{
	display: flex; flex-flow: column; width: 480px; padding: 0 56px 40px; background: @white; position: fixed; left: 0; right: 0; top: 50%; bottom: initial; margin: auto; margin-top: -280px; z-index: 9999; box-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.1);
	.message{
		font-size: 20px; line-height: 25px; color: @textColor; padding: 40px 0 24px; position: relative; font-weight: bold; text-align: center;
		//&:before{background: @yellow; border-radius: 200px; width: 50px; height: 50px; text-align: center; position: absolute; .icon-check; font: 20px/50px @fonti; color: @textColor; left: 0; top: 0;}
		&.product_message_response_error:before{color: @red;}
	}
	.close-button{
		z-index: 10; position: relative; display: block; width: 20px; height: 20px; position: absolute; top: 25px; right: 25px; .transition(all);
		&:before{position: absolute; .icon-close; font: 16px/20px @fonti; width: 100%; text-align: center; color: @red; top: 0; left: 0;}
	}
	.image{
		position: relative; display: block; width: 100%; height: auto; padding: 0 15px;
		img{display: block; width: auto; height: auto; max-width: 100%; max-height: 200px; margin: auto;}
	}
	.desc{position: relative; display: block; width: 100%; padding: 20px 15px 0; text-align: center;}
}
.modal-desc-col{flex-grow: 1;}
.modal-title{
	font-size: 20px; line-height: 18px; font-weight: bold; text-transform: uppercase; color: @gray; display: block; padding-bottom: 0px;
	p{padding-bottom: 0;}
}
.modal-extra-title{
	font-weight: normal; padding-bottom: 0; padding-top: 20px; position: relative;
	&:before, &:after{.pseudo(10px,2px); background: #000; top: 8px; left: 50%; margin-left: -4px;}
	&:after{height: 10px; width: 2px; top: 4px; margin-left: 0;}
}
.modal-extra-title:empty{
	padding-top: 0;
	&:before, &:after{display: none;}
}
.modal-price{
	font-size: 20px; font-weight: bold; line-height: 30px; padding: 15px 0 0;
	.cd-modal-price,.cp-modal-price{display: flex; justify-content: center;}
	.cp-price-note, .cp-extra-price-lowest{display: none;}
}
.modal-old-price{font-size: 16px; line-height: 18px; font-weight: normal;}
.cd-modal-price,.cp-modal-price{
	display: flex; align-items: flex-end; display: none;
	ins{margin: 0 15px!important; color: #E3E5E2;}
}
.modal-buttons{padding: 24px 0 0 0; width: 100%; display: flex; flex-flow: column;}
.modal-view-cart{
	width: 100%; height: 55px; background: #0EAD69; color: @white;
	&:hover{color: @white;}
}
.modal-continue{
	display: flex; margin-top: 15px; overflow-x: hidden;
	@media(max-width: 750px){flex-flow: column-reverse; row-gap: 14px; padding: 0 15px;}
}
.modal-continue-shopping{
	width: 100%; font-size: 14px; line-height: 18px; text-decoration: none; display: flex; align-items: center; justify-content: center; margin-top: 16px; text-decoration: underline; text-decoration-color: @yellow; text-underline-offset: 2px;
	&:hover{text-decoration: none;}
}

//WARRANTY
.fancybox_modal_warranty{
	max-width: 710px !important; min-width: 510px !important; width: auto !important;
	.fancybox-skin{padding: 21px 33px!important; background: @white!important; /*max-height: 780px; overflow: auto;*/ color: @textColor;}
	.fancybox-inner{height: auto!important; width: auto!important; overflow-x: hidden !important;}
	.fancybox-close{
		z-index: 10; position: relative; display: block; width: 20px; height: 20px; position: absolute; top: 20px; right: 20px; .transition(all);
		&:before{position: absolute; .icon-close; font: 16px/20px @fonti; width: 100%; text-align: center; color: @red; top: 0; left: 0;}
	}
	.warranty-modal-close{display: block;}
	.wp-message{color: transparent; background: unset;}
	.message{font-size: 22px; line-height: 28px; padding-top: 0; padding-bottom: 6px; text-transform: unset; color: @textColor;}
	@media(max-width: 750px){
		.message{font-size: 19px; line-height: 21px;}
	}
}
.continue-shopping{
	flex-shrink: 0; padding: 0 60px; display: flex; align-items: center; justify-content: center; font-size: 14px; line-height: 18px; text-decoration: underline; text-decoration-color: #FCD002 !important; text-underline-offset: 4px;
	&:hover{text-decoration: none;}
}
.warranty-modal{
	overflow-x: hidden;
	.modal-title{font-size: 18px; line-height: 23px; text-transform: initial; padding-bottom: 5px!important;}
	.modal-image{
		margin-left: 80px; display: flex; align-items: center; justify-content: center;
		img{display: block; max-width: 100%; max-height: 100%; width: auto; height: auto;}
	}
	.warranty-intro-cnt{width: 340px; flex-shrink: 0; max-width: 340px;}
	.warranty-modal-intro{display: flex;}
	.modal-buttons{
		padding-top: 0 !important; width: 300px;
		p{padding-bottom: 6px;}
		input[type=checkbox]+label, input[type=radio]+label{font-size: 14px; line-height: 18px;}
	}
	.cd-related-products{padding: 8px 0 0 0 !important; overflow-x: hidden; width: 644px;}
	.cd-services-price{display: inline;}
	.cd-related-title{font-size: 18px; line-height: 24px; padding-bottom: 5px;}
	.cd-row{display: block;}
	.cp-header-cnt{padding-top: 40px;}
	.cp-brand{top: 10px; left: 10px;}
	.cd-related-slider{
		display: flex; margin-left: 1px; width: auto;
		.cp{max-width: 215px; width: 215px;}
		.cp-image{
			figure{height: 100px;}
		}
		.cp-btn-addtocart{
			b{display: none;}
		}
		.cp-cnt-header{flex-grow: unset;}
		.cp-price-container-warranty{flex-grow: 1;}
		.cp-cnt{padding: 0 16px 16px;}
		.cp-card-discount{padding: 4px 0 3px; width: 28px; font-size: 10px;}
		.cp-card-prices{font-size: 10px;}
		.cp-title{font-size: 14px; line-height: 18px;}
		.cp-attrs{padding-bottom: 10px;}
		.cp-current-price{font-size: 24px;}
		.cp-rating-container{display: none !important;}
		.cp-badges{top: 10px; right: 15px; max-width: 30px;}
		.cp-special-list-badge{
			img{max-height: 30px;}
		}
		.cp-brand{
			img{max-height: 18px;}
		}
		.cp-attr-energy{
			font-size: 12px;
			img{max-width: 34px;}
		}
		.cp-attrs{
			font-size: 12px; line-height: 16px;
			li{
				margin-right: 6px;
				&:before{top: 5px; width: 4px; height: 4px;}
			}
		}
		.cp-unavailable{top: 73px;}
		.cp-btn-details{font-size: 12px; line-height: 14px; padding: 0 5px;}
		.cp-warranty-select{
			margin-top: 5px; position: relative;
			select{width: 100%; height: 32px; padding: 0 18px 0 12px; background: url(images/icons/arrow-down.svg) no-repeat right 8px center; background-size: 8px auto;}
			.warranty-image{
				position: absolute; left: 12px; top: 7px;
				img{display: block; max-width: 18px; max-height: 18px;}
			}
		}
		.cp-btns{margin-top: 5px;}
		.cp-warranty-select-empty{min-height: 32px;}
	}

	@media(max-width: 750px){
		.warranty-intro-cnt{width: auto; max-width: unset; flex-shrink: unset; padding-right: 40px;}
		.modal-image{display: none;}
		.warranty-modal-intro{padding: 0 15px;}
		.cd-related-title{text-align: left; padding: 0 15px 8px;}
		.cd-related-slider{
			flex-wrap: unset; overflow-x: auto; padding: 1px 0 0 1px; margin-left: 0; margin-right: 0; width: auto;
			&::-webkit-scrollbar{-webkit-appearance: none; height: 0px; display: none;}
			&::-webkit-scrollbar-thumb{height: 0px; display: none;}
			&::-webkit-scrollbar-track-piece{height: 0px; display: none;}
			&::-webkit-scrollbar-track{height: 0px; display: none;}
			.cp-image{
				top: 12px; left: 12px; width: 90px;
				figure{height: 90px;}
			}
			.cp{
				max-width: 290px; width: 295px; flex-shrink: 0;
				&:first-child{margin-left: 15px;}
				&:last-child{margin-right: 15px;}
			}
			.cp-extra-price-lowest{padding-top: 3px;}
			.cp-cnt{padding: 0 12px 12px;}
			.cp-brand{top: unset; left: unset;}
			.cp-btn-addtocart{
				b{display: inline;}
			}
			.cp-header-cnt{padding-top: 12px;}
			.cp-cnt-header{padding-left: 102px; min-height: 138px;}
			.cp-warranty-select{
				select{padding: 0 34px 0 15px; font-size: 13px; background-size: 9px auto; background: url(images/icons/arrow-down.svg) no-repeat right 13px center; height: 40px;}
			}
			.cp-card-prices{flex-wrap: wrap; font-size: 12px; margin-bottom: 2px;}
			.cp-card-price{width: 50%;}
		}
	}
}
.modal-item-price{
	.cp-current-price{font-size: 22px; margin-top: 3px;}
}

//basic modal 
.warranty-modal-intro-basic{
	.message{padding-bottom: 10px;}
	.warranty-intro-cnt{display: flex; flex-flow: column; justify-content: center; align-items: center; width: 100%; max-width: 100%; text-align: center;}
	.modal-image{margin: 0; margin-bottom: 15px;}
	.modal-item-title{max-width: 410px; text-align: center;}
}
/*------- /add to cart modal -------*/

/*------- contact -------*/
.contact-row{
	display: flex;
	h2{padding: 0 0 10px; font-size: 24px; line-height: 30px;}
}
.contact-col1{
	flex-grow: 1; padding-right: 125px;
	p{padding: 0 0 5px;}
}
.contact-col2{
	width: 320px; flex-grow: 0; flex-shrink: 0;
	h3{padding: 0; font-size: 21px; line-height: 24px;}
	.locations-title{padding-top: 40px;}
	.icon-tel2, .icon-mail2{
		text-decoration: none; padding-left: 35px;
		&:before{color: @yellow; position: absolute; left: 0; top: 3px; font-size: 14px; line-height: 14px; margin: 0;}
	}
	.icon-tel2:before{font-size: 20px; line-height: 20px; top: -1px;}
}
.nav-locations-contact{
	li{width: 50%; padding: 0 0 8px;}
}
.map-contact{height: 550px;}
.btn-locations{height: 40px; margin-top: 10px; font-size: 16px; width: 100%;}
/*------- /contact -------*/

/*------- disabled webshop -------*/
.webshop-disabled{
	.search-price, .cp-price-discount, .cp-card-prices, .cp-extra-price-lowest, .cd-price-container, .cd-services, .cd-btn-add, .col-order-total, .wp-total, .wp-sum, .ww-preview, .modal-box, .wp-qty, .w-totals{display: none!important;}
}
.webshop-disabled-note{text-align: center;}
/*------- /disabled webshop -------*/


/*------- cms for employees -------*/

/*------- faq -------*/
.page-faq{
	.share{padding-left: 30px;}
}
.fp{
	padding: 0 0 15px;
	&.active{
		.fp-cnt{max-height: 2500px; padding-top: 4px;}
		.fp-title:before{display: none;}
	}
}
.fp-title{
	font-size: 20px; line-height: 27px; padding: 0 0 0 28px; position: relative; text-transform: none; font-weight: normal;
	&:before, &:after{.pseudo(4px,14px); background: @yellow; left: 0; top: 0;}
	&:before{top: 6px; left: 5px;}
	&:after{top: 11px; width: 14px; height: 4px;}
	span{cursor: pointer;}
	&:hover span{text-decoration: underline;}
}
.fp-group-title{padding: 30px 0 15px 0; color: @red;}
.fp-group-title1{padding-top: 0;}
.fp-cnt{
	overflow: hidden; max-height: 0; font-size: 18px; line-height: 26px; padding-left: 29px; .transition(all);
	a{
		text-decoration: underline; color: @red;
		&:hover{text-decoration: none;}
	}
}
/*------- /faq -------*/

/*------- brands widget -------*/
.mw{display: flex; background: #fff; justify-content: center; height: 90px; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; content-visibility: auto; contain-intrinsic-size: 90px;}
.mwp{
	width: 10%;
	a{
		display: flex; height: 100%; align-items: center; padding: 0 20px; justify-content: center;
		&:hover{
			img{opacity: 1;}
		}
	}
	img{max-width: 100%; height: auto; max-height: 35px; width: auto; opacity: .6; .transition(opacity); display: block; .grayscale();}
	&:not(:last-child){border-right: 1px solid @borderColor;}
}
/*------- /brands widget -------*/

/*------- hero slider -------*/
.hp-header{.gradient(#fff,#eee); padding-bottom: 395px;}
.hero-slider-container{display: flex; width: 1400px; margin: 0 auto; position: relative; margin-top: 16px;}
.hero-slider{display: block; position: relative; width: 100%;}
.hp-header-label{position: absolute; left: 0; bottom: 30px; font-size: 42px; line-height: 43px; font-weight: 300; writing-mode: vertical-lr;}
.hp-header-label1{left: -65px; .rotate(180deg);}
.hp-header-label2{left: auto; right: -65px;}

.c-promo{
	min-height: 600px; max-height: 600px; position: relative; margin: 0; width: 100%; background-size: cover !important;
	&:after{.pseudo(auto, 8px); left: 80px; right: 80px; bottom: -4px; background: @yellow;}
}
.wrapper-hp-promo{min-height: 600px; padding: 0; position: relative;}
a.c-promo{display: block;}
.c-promo-container{position: absolute; display: flex; flex-flow: column; text-align: left; align-items: flex-start; justify-content: flex-end; top: 0; max-height: 600px; min-height: 600px; min-width: 520px; max-width: 520px; left: 0; padding-bottom: 80px; margin-left: 80px; font-size: 20px; line-height: 28px; color: @white;}
.c-promo-headline{font-size: 18px; line-height: 23px; text-transform: uppercase; color: @yellow; font-weight: bold;}
.c-promo-title{font-size: 56px; line-height: 73px; font-weight: bold; text-transform: uppercase; color: @white;}
.c-promo-btns{margin-top: 20px; display: block;}
.c-promo-btn{width: 200px;}

.c-promo-light{
	.c-promo-container{color: @textColor;}
	.c-promo-headline{color: @textColor}
	.c-promo-title{color: @textColor;}
}
.c-promo-center{
	.c-promo-container{text-align: center; align-items: center; margin: 0 auto; left: 0; right: 0; min-width: 490px; max-width: 490px;}
}
/*------- /hero slider -------*/

/*------- popular categories -------*/
.popular-categories-cnt{display: flex; flex-flow: column; position: relative; margin: -302px auto 0; width: 100%;}
.pop-hp-categories-title{
	font-size: 48px; line-height: 50px; text-align: center; font-weight: 300; position: relative; display: block; padding-bottom: 40px;
	p{padding-bottom: 0;}
}
.pop-hp-categories{display: flex; flex-wrap: wrap; background: @white; box-shadow:  0 5px 30px 0 rgba(0, 0, 0, 0.15);}
.cat-item{
	display: flex; flex-flow: column; justify-content: center; align-items: center; text-align: center; width: calc(~"100%/6"); padding: 24px 15px 15px; position: relative; font-size: 16px; line-height: 21px; font-weight: bold; color: @textColor; border-right: 1px solid @borderColor; border-top: 1px solid @borderColor; text-decoration: none;
	&:nth-child(6n){border-right: 0;}
	&:nth-child(-n+6){border-top: 0;}
	span{display: block; padding-top: 6px;}
	img{display: block; width: auto; height: auto; max-width: 100%; max-height: 144px;}
}
.popular-categories-section + .hp-brands{
	margin-top: 170px;
	@media (max-width: @m){margin-top: 30px;}
}
/*------- /popular categories -------*/

/*------- hp-promos -------*/
//LIGHT
.hp-promos-cnt{padding-top: 88px; padding-bottom: 154px; .gradient(#fff,#eee);}
.c-pr-cnt{display: flex; flex-flow: column; row-gap: 48px;}
.c-pr-row{display: flex; column-gap: 40px;}
.c-pr-item{
	position: relative; text-align: center; color: @textColor;
	img{display: block; width: 100%; height: auto; max-width: 100%;}
}
.c-pr-item-img{
	position: relative; display: block;
	&:after{.pseudo(auto, 8px); background: @yellow; bottom: -4px; right: 80px; left: 80px;}
}
.c-pr-content{padding: 24px 10px 0; display: block;}
.c-pr-title{font-size: 32px; line-height: 40px; font-weight: 300; text-decoration: none;}
.c-pr-subtitle{font-size: 16px; line-height: 21px; font-weight: bold; padding-top: 4px;}
.c-pr-item-two-smaller,.c-pr-item-three{
	.c-pr-item-img:after{right: 40px; left: 40px;}
}

//DARK
.hp-promos-cnt-dark{background: none; padding: 0 0 64px; padding-top: 0!important;}
.c-pr-item-dark{
	color: @white;
	.c-pr-title{
		color: @white;
		strong{color: @yellow;}
	} 
}
/*------- /hp-promos -------*/

/*------- hp-brands -------*/
.hp-brands{
	position: relative;
	.mw{margin-top: -90px; flex-wrap: wrap; border-top: 0; border-bottom: 0; height: 180px; contain-intrinsic-size: 180px; box-shadow: 0 5px 30px 0 rgba(0,0,0,0.15); position: relative; z-index: 2;}
	.mwp{
		width: calc(~"100%/9"); border-top: 1px solid @borderColor;
		&:nth-child(9){border-right: 0;}
		&:nth-child(-n+9){border-top: 0;}
	}
}
/*------- /hp-brands -------*/

/*------- hp promo section -------*/
.section-gray{background: @gray; padding: 91px 60px 120px; content-visibility: auto; contain-intrinsic-size: 1900px; margin-top: -90px;}
.panel-row{display: flex; margin-bottom: 40px;}
.panel-cols{column-gap: 35px;}
.panel{
	background: #fff; width: calc(~"100% / 3 - 25px"); margin: 0 40px 0 0; padding: 35px 40px 150px; position: relative; overflow: hidden; min-height: 560px; box-shadow: 0 20px 40px 0 rgba(0,0,0,0.3);
	&:last-child{margin-right: 0;}
	h3{padding: 0;}
	//&:before{.pseudo(170px,170px); background: @yellow; border-radius: 100px; right: -100px; top: -100px;}
	//&:after{.icon-plus; font: 25px/25px @fonti; position: absolute; right: 12px; top: 13px; color: #000;}

	ul{
		list-style: none; padding: 0; margin: 0; font-size: 16px; line-height: 20px; padding-right: 30px; flex-grow: 1; white-space: nowrap; width: 50%;
		li{padding: 4px 0;}
		a{
			text-decoration: none;
			&:hover{text-decoration: underline;}
		}
		.red a{color: @red;}
		&:last-of-type{padding-right: 0;}
	}
}
.panel-title{
	font-size: 36px; line-height: 40px; color: #000; padding: 0 0 16px;
	strong{display: block;}
}
.panel-footer{
	background: @yellow; height: 80px; position: absolute; bottom: 0; left: 0; right: 0;
	img{max-width: 100%; height: auto; display: block; margin: auto;}
	p{padding: 0;}
}
.panel-footer-cnt{position: absolute; bottom: 0; left: 0; right: 0; text-align: center;}
.panel1 ul{flex-grow: 0;}
//.panel2{padding-bottom: 255px;}

.hp-categories-cnt{display: none;}
/*------- /hp promo section -------*/

/*------- benefits -------*/
.benefits{display: flex; margin: 55px; justify-content: space-between;}
.benefit-item{
	max-width: 23%; /* margin: 0 50px 0 0; text-align: center; */
	&:nth-child(2){padding-left: 15px;}
	&:last-child{margin-right: 20px;}
}
.benefit{
	position: relative; color: #fff; height: 90px; padding: 23px 0 0 110px; display: inline-block; vertical-align: top; text-decoration: none; font-size: 20px; text-align: left;
	span{display: block; font-size: 14px; line-height: 18px; font-weight: 300;}
	&:before{.pseudo(90px,90px); .icon-truck; font: 30px/90px @fonti; text-align: center; color: @gray; background: @yellow; border-radius: 100px; left: 0; top: 0; .transition(all);}
	&:hover{
		text-decoration: none; color: #fff;
		&:before{background: #000; color: @yellow;}
	}
}
.benefit-shipping:before{.icon-truck;}
.benefit-payment
	{
		padding-top: 14px;
		&:before{.icon-wallet; font-size: 39px;}
	}
.benefit-flyer:before{.icon-flyer; font-size: 40px;}
.benefit-kekspay{
	&:before{content: ""; background: @yellow url(images/keks.svg) no-repeat center center; background-size: 37px 37px;}
	&:hover{
		&:before{background: #000 url(images/keks_y.svg) no-repeat center center;}
	}
}

/*------- /benefits -------*/

/*------- support - stores -------*/
.locations{margin-bottom: 40px;}
.locations-col1{width: calc(~"33.333% - 26px"); flex-grow: 0; flex-shrink: 0; margin-right: 40px;}
.locations-col2{flex-grow: 1;}
.panel-support{display: flex; flex-direction: column; background: #fff; height: 100%; box-shadow: 0 20px 40px 0 rgba(0,0,0,0.3);}
.panel-support-header{
	background: @yellow; padding: 37px 43px; height: 50%; font-size: 36px; line-height: 40px; color: #000; font-weight: 300;
	strong{display: block;}
}
.panel-support-footer{
	padding: 48px 43px 0; color: @textColor; font-size: 18px; line-height: 25px;
	p{
		padding: 0;
		a:last-child{text-decoration: underline; text-decoration-color: @yellow; text-underline-offset: 2px;}
	}
	h4{font-size: 24px; line-height: 28px; color: #000; text-transform: none; padding: 0 0 5px 0; margin: 0;}
	a{text-decoration: none; font-weight: bold;}
}

.panel-stores{background: url(images/locations.jpg) no-repeat left top; height: 300px; background-size: cover; position: relative;}
.nav-stores{
	list-style: none; padding: 0; margin: 0; position: absolute; bottom: 27px; left: 0; right: 0; justify-content: center; fz
	a{color: #fff;}
}
.panel-stores-title{
	position: absolute; text-transform: uppercase; width: 500px; left: 50%; margin-left: -250px; color: #fff; font-weight: bold; font-size: 36px; padding: 61px 0 0 0; text-align: center; top: 50px;
	&:after{.pseudo(140px,140px); left: 50%; margin-left: -77px; background: @yellow; border-radius: 100px; top: 0;}
	&:before{.pseudo(100px,65px); background: url(images/logo.svg) no-repeat left top; background-size: contain; left: 50%; margin-left: -55px; top: 36px; z-index: 10;}
	span:first-child{padding-right: 104px;}
	span:last-child{padding-left: 104px;}
}
/*------- /support - stores -------*/

/*------- miele -------*/
.panel-miele{
	background: #911829; color: #fff; position: relative; box-shadow: 0 20px 40px 0 rgba(0,0,0,0.3); font: 16px/24px Arial, sans-serif;
	img{max-width: 100%; height: auto; display: block;}
	.flex-col{
		border-right: 1px solid darken(#911829,20%); padding: 40px 40px 48px 80px; flex: 0 0 33.333%;
		&:last-child{border-right: none;}
	}
	a, a[href^=tel]{text-decoration: none; color: #fff;}
	ul{
		list-style: none; padding: 0; margin: 0;
		li{
			margin: 0 0 8px;
			&:last-child{margin-bottom: 0;}
		}
		a{
			position: relative; padding-left: 28px;
			span{
				position: relative;
				&:after{.pseudo(100%,1px); left: 0; bottom: -1px; background: #fff; .scaleX(0); .transition(all); transform-origin: left;}
			}
			&:before{.pseudo(16px,2px); background: rgba(0, 0, 0, 0.15); top: 8px; left: 0; .transition(all);}
			&:hover{
				text-decoration: none;
				&:before{background: #fff;}
				span:after{.scaleX(1);}
			}
		}
	}
	.icon{
		padding-left: 30px;
		&:before{position: absolute; left: 0; top: 4px; font-size: 18px;}
	}
	.icon-mail:before{font-size: 15px; top: 6px;}
	.flex-col1{
		line-height: 20px;
		p:first-child{font-size: 20px; font-weight: bold;}
		a{
			text-decoration: underline; text-underline-offset: 5px; line-height: 30px; font-size: 16px;
			&:hover{text-decoration: none;}
		}
	}
}
.logo-miele{background: url(images/logo-miele.svg) no-repeat left top; background-size: contain; width: 180px; height: 70px; position: absolute; top: 0; left: 50%; margin-left: -90px;
	&+p{padding: 0;}
}
/*------- /miele -------*/

/*------- promo -------*/
.wrapper-promo{max-width: 1440px;}
.promo{flex-wrap: wrap;}
.promo-col{
	width: calc(~"100% / 3 - 45px"); margin: 0 22.4px 45px;
	img{max-width: 100%; height: auto; display: block; .transition(all);}
}
@media screen and (min-width: 1240px) {
	.promo-col a:hover img{box-shadow: 0 20px 40px 0 rgba(0,0,0,0.2);}
}
.promo-col-promo-half{width: calc(~"50% - 45px");}
/*------- /promo -------*/

/*------- catalog widget -------*/
.cw{padding: 104px 0 88px; border-bottom: 1px solid @borderColor;  content-visibility: auto; contain-intrinsic-size: 980px;}
.cw-title{font-size: 48px; line-height: 50px; text-align: center; font-weight: 300; padding: 0 0 32px; color: @gray;}
.btn-cw-all{margin: 40px 0 0; min-width: 200px;}
.cw-btns{text-align: center;}
/*------- /catalog widget -------*/

/*------- catalog -------*/
.btn-toggle-filter{display: none;}
.c-header{text-align: center; padding: 30px 0 15px;}
.c-sidebar{padding-bottom: 40px; width: 285px;}
.c-items{display: flex; flex-wrap: wrap; padding-bottom: 80px;}
.c-empty{padding-left: 30px;}
.c-desc{
	font-size: 22px; line-height: 32px; max-width: 960px; margin: auto; padding-bottom: 30px;
	iframe{width: 1080px; max-width: 100%; min-height: 350px; margin-top: 30px;}
}
/*------- /catalog -------*/

/*------- catalog post -------*/
.cp{
	width: calc(~"25% - -1px"); display: flex; flex-direction: column; position: relative; border: 1px solid @borderColor; margin-left: -1px; margin-top: -1px; line-height: 22px; transition: border-color .3s, box-shadow .3s;
	a{text-decoration: none;}
}
@media screen and (min-width: 1240px) {
	.cp:not(.cp-compare):hover{box-shadow: inset 0px 0px 0px 3px @yellow; border-color: @yellow; z-index: 1;}
}
.cp-badges{
	position: absolute; top: 15px; right: 15px; z-index: 10; max-width: 60px;
	a{text-decoration: none; display: block;}
}
.cp-list-badge{
	margin-bottom: 5px; text-decoration: none; display: block;
	img{display: block; max-width: 100%; height: auto;}
}
.cp-special-list-badge{
	height: auto!important;
	img{width: auto; height: auto;}
}
.cp-title{margin: 0; padding: 0; font-size: 17px; font-weight: normal; line-height: 20px; font-weight: bold; text-transform: none;}
.cp-image{
	width: 100%; display: flex; align-items: center; padding-bottom: 15px; justify-content: center; position: relative;
	img{max-width: 100%; max-height: 100%; width: auto; height: auto; display: block; margin: auto;}
	figure{display: flex; justify-content: center; align-items: center; width: 100%; height: 265px; margin: auto;width: 95%;}
}
.cp-not-available{
	.cp-image, .cp-list-image{
		img{opacity: .4;}
	}
}
.cp-header-cnt{display: block; position: relative; padding-top: 50px;}
.cp-header{height: 50px; display: flex; padding: 0 20px; align-items: center;}
.cp-brand{
	position: absolute; top: 14px; left: 20px;
	img{max-height: 25px; width: auto; height: auto; display: block; .grayscale();}
}
.cp-brand-apple img{max-height: 30px;}
.cp-save{font-size: 16px; flex-shrink: 0;}
.cp-save-label{color: @red; font-weight: bold;}
.cp-code{font-size: 13px; line-height: 20px;}
.cp-badge{display: block; position: absolute; background: @red; z-index: 50; width: 60px; height: 60px; font-size: 20px; font-weight: bold; display: flex; justify-content: center; align-items: center; bottom: 0; border-radius: 100px; color: #fff; font-size: 14px; left: 15px; font-size: 18px;
	&.new{background: @yellow; color: @textColor; text-transform: uppercase;}
}
.cp-badge-gift{
	background: @gray; left: auto; right: 15px; bottom: 0;
	&:before{.icon-gift; font: 25px/25px @fonti; color: @yellow; margin-left: 1px;}
}
.cp-cnt{
	padding: 0 20px 20px; display: flex; flex-grow: 1; flex-flow: column;
	&>a{display: flex; flex-direction: column; flex-grow: 1;}
}
.cp-attrs{
	font-size: 14px; line-height: 20px; list-style: none; margin: 0; padding: 3px 0 30px;
	li{
		padding: 0 0 0 11px; position: relative; display: inline; margin-right: 10px;
		&:before{.pseudo(6px,6px); background: @yellow; border-radius: 100px; left: 0; top: 6px;}
		&:first-child{
			padding-left: 0;
			&:before{display: none;}
		}
	}
}
.cp-attr-energy{
	display: flex; align-items: center; font-size: 14px; margin-bottom: 10px;
	@media (max-width: @m){margin-bottom: 0; padding-top: 2px;}
	img{display: block; margin-right: 10px;}
}
.cp-attr-energy-link{
	text-decoration: underline!important; text-decoration-color: @yellow!important; font-weight: bold; text-underline-offset: 4px;
	&:hover{text-decoration-color: @yellow;}
}
.wwp-attr-energy{
	display: flex; font-size: 10px; margin: -3px 0 0 18px;
	img{height: 23px; width: auto; margin-right: 6px;}
}

@media (max-width: @m){
	.cp-price-container{display: flex; flex-direction: row-reverse; justify-content: flex-end;}
}
.cp-price{position: relative; padding-top: 5px;}
.cp-card-prices{margin-bottom: 5px; font-size: 13px; line-height: 1.2; display: flex; flex-wrap: wrap;}
.cp-card-price{
	padding-bottom: 7px; display: flex; align-items: center; position: relative; width: 50%;
	&:after{.icon-arrow-right; font: 9px/1 @fonti; color: @red; margin-left: 5px; margin-top: -1px; .transition(margin); display: none;}
	//&:hover:after{margin-left: 8px;}
}
.cp-card-discount{background: @red; color: #fff; padding: 6px 0 5px; margin-right: 5px; line-height: 1; font-weight: bold; width: 32px; text-align: center; flex-shrink: 0;}
.cp-price-regular{text-align: right; font-size: 22px; color: #939393;}
.cp-price-discount{flex-grow: 1;}
.cp-current-price{font-size: 30px; font-weight: bold; position: relative;}
.cp-current-price, .cd-current-price, .cd-lowest-price, .cd-lowest-coupon-price, .cd-modal-price{
	ins{font-size: 0; border-right: 1px solid #CDD0CB; font-weight: 300; width: 1px; height: 23px; display: inline-block; vertical-align: top; margin: 0 4px 0 1px;}
}
.formated-price{
	position: relative; line-height: 26px; display: inline-block; vertical-align: top;
	.p-d, .p-{display: none;}
	.p-c{padding-right: 2px;}
	.p-small{font-size: 12px; width: 6px; line-height: 12px; display: inline-block; vertical-align: top;}
	.p-k, .p-n, .p-€, .p-E, .p-U, .p-R{position: absolute; bottom: 3px; right: 6px;}
	.p-k{right: 12px;}
	.p-E{right: 13px;}
	.p-U{right: 8px;}
	.p-R{right: 1px;}
	.p-€{right: 10px;}
}
.cp-unavailable{position: absolute; top: 170px; left: 0; right: 0; text-align: center;}
.cp-unavailable-btn, .cp-list-unavailable{
	display: inline-flex; align-items: center; padding: 8px 11px 7px; color: #fff; background: @red; font-size: 13px; line-height: 1; font-weight: bold; text-transform: uppercase;
	//&:after{.icon-arrow-right; font: 10px/1 @fonti; color: @yellow; margin-left: 7px;}
}
.cp-list-unavailable{margin-bottom: 10px; padding: 7px 12px; font-size: 13px;}
.cp-price-label{text-decoration: none; min-height: 20px;}
.cp-price-note{font-size: 12px; line-height: 10px; font-weight: normal; padding: 3px 0 0;}
.cp-btns{display: flex; position: relative; margin-top: 12px;}
.cp-btn-addtocart{
	height: 40px; display: inline-flex; background: @gray; font-size: 13px; flex-grow: 1; margin-right: 10px; color: #fff; text-decoration: none; justify-content: center; align-items: center; .transition(all);
	span{
		position: relative; padding-left: 25px;
		&:before{.icon-cart; font: 16px/16px @fonti; color: @yellow; position: absolute; left: 0; top: 2px;}
	}
}
.btn-cp-add-detail{
	flex-grow: 1; height: 40px; font-size: 14px; line-height: 18px; border-left: 1px solid rgba(255, 255, 255, 0.1);
	&.cp-btn-addtocart{
		background: #0EAD69; padding: 0 15px; margin-right: 0;
		span:before{.icon-cart_notactive; color: @white; top: -1px;}
	}
}

.cp-btn-details span:before{.icon-info; font-size: 18px;}
.cp-compare-container{width: 40px; height: 40px; position: relative;}
.cp-btn-compare{
	display: block; font-size: 0; height: 100%; border: 1px solid @borderColor; position: relative; transition: background-color .3s, border-color .3s; text-decoration: none;
	&:before{.icon-compare_redesign; font: 16px/39px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; text-indent: 1px; .transition(color);}
	&.compare_active{
		background: @gray; border-color: @gray; border-left: 1px solid rgba(255, 255, 255, 0.1)!important;
		&:before{color: @yellow;}
	}
}
.cp-wishlist{
	width: 40px; height: 40px; flex-shrink: 0; flex-grow: 0; font-size: 0; margin-right: -1px; position: relative;
	&>a{
		height: 100%; border: 1px solid @borderColor; position: relative; transition: background-color .3s, border-color .3s; text-decoration: none;
		&:before{.icon-wishlist_notactive; font: 15px/39px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; .transition(color);}
	}
	&.active{
		.cp-wishlist-add{display: none;}
		.cp-wishlist-remove{display: block;}
		&>a{
			background: @gray; border-color: @gray;
			&:before{color: @yellow; .icon-wishlist_active;}
		}
	}
}

.cp-wishlist-add{display: block;}
.cp-wishlist-remove{display: none;}
.cp-cnt-header{flex-grow: 1;}
.wishlist-message, .cp-compare-info{
	background: #fff; white-space: nowrap; box-shadow: 0 2px 7px rgba(0,0,0,.2); font-size: 12px; line-height: 15px; position: absolute; bottom: calc(~"100% - -10px"); left: 0; padding: 5px 10px;
	&:after{.pseudo(10px,10px); background: #fff; .rotate(45deg); bottom: -4px; left: 15px;}
}

.cp-rating-container{height: 25px;}


.cp-list{
	display: flex; width: 100%; border: 1px solid @borderColor; margin: -1px 0 0 -1px;
	.cp-extra-price-lowest{max-width: unset; padding-top: 3px; max-width: 105px; font-size: 11px;}
}
.cp-list-col1, .cp-list-col3{flex-shrink: 0; flex-grow: 0; width: 280px;}
.cp-list-col1{border-right: 1px solid @borderColor; position: relative;}
.cp-list-col2{flex-grow: 1; padding: 18px 0 20px 40px;}
.cp-list-col3{padding: 20px 30px; text-align: center; display: flex; flex-wrap: wrap;}
.cp-list-col3-top, .cp-list-col3-bottom{width: 100%;}
.cp-list-col3-top{display: flex; padding-top: 15px;}
.cp-list-col3-bottom{align-self: flex-end;}
.cp-list-image{
	img{max-width: 100%; width: auto; height: auto;}
}
.cp-list-attr-energy{
	display: block; flex-shrink: 0; text-align: center; font-weight: bold; padding: 0;
	img{display: block; margin: 0 auto 3px;}
}
.cp-list-code{font-size: 12px; line-height: 18px; padding: 0 0 4px;}
.cp-list-title{padding: 0; margin: 0 0 5px; font-size: 19px; line-height: 20px; font-weight: bold; text-transform: none;}
.cp-list-attrs{display: flex; list-style: none; padding: 0; margin: 25px 0 0; width: 100%; height: 150px;}
.cp-list-attr{
	border: 1px solid @borderColor; width: 25%; margin-left: -1px; position: relative; display: flex; justify-content: center; align-items: center; flex-wrap: wrap; font-size: 13px; line-height: 17px;
	&:after{.pseudo(3px,auto); top: 20px; bottom: 20px; left: -2px; background: @yellow;}
	&:first-child:after{display: none;}
	a{text-decoration: none;}
}
.cp-list-attr-energetski-razred a{display: flex; justify-content: center; align-items: center; width: 100%; height: 100%;}
.cp-list-attr-image{
	width: 100%; margin: 0 0 10px; height: 50px; display: flex; justify-content: center; align-items: center;
	img{max-width: 100%; display: block; margin: auto;}
}
.cp-list-card-prices{
	margin: 15px 0 10px; display: block;
	.cp-card-price{
		width: initial;
		&:after{display: block;}
	}
	.cp-card-discount{padding: 7px 7px 6px; width: auto; text-align: initial; flex-shrink: initial;}
}
.cp-list-cert{
	display: flex; justify-content: center; align-items: center; min-width: 40px; margin-left: -15px; height: 33px; background: #00964e; color: #fff; font-size: 24px; font-weight: bold; text-align: right; text-align: right; padding: 2px 8px 0 15px; position: relative; text-shadow: 1px 0px 1px rgba(0,0,0,.2);
	&:after{.pseudo(0,0); border-style: solid; border-width: 17px 0 16px 15px; border-color: transparent transparent transparent #00964e; right: -15px; top: 0;
	}
}
.cp-list-cert-a-plus-plus{
	background: #00aa01;
	&:after{border-left-color: #00aa01;}
}
.cp-list-cert-a-plu{
	background: #a9c800;
	&:after{border-left-color: #a9c800;}
}
.cp-list-cert-a{
	background: #138f49;
	&:after{border-left-color: #138f49;}
}
.cp-list-cert-b{
	background: #66ae3e;
	&:after{border-left-color: #66ae3e;}
}
.cp-list-cert-c{
	background: #c8d93b;
	&:after{border-left-color: #c8d93b;}
}
.cp-list-cert-d{
	background: #fff641;
	&:after{border-left-color: #fff641;}
}
.cp-list-cert-e{
	background: #ffbc37;
	&:after{border-left-color: #ffbc37;}
}
.cp-list-cert-f, .cp-list-cert-f-razred{
	background: #ff6b2d;
	&:after{border-left-color: #ff6b2d;}
}
.cp-list-cert-g{
	background: #ff3b2b;
	&:after{border-left-color: #ff3b2b;}
}
.cp-list-cert-n-a{
	background: #fff; color: @textColor;
	&:after{border-left-color: #fff;}
}

.attr-tooltip{
	position: absolute; background: #fff; display: none; top: 100%; left: 0; box-shadow: 0px 0px 20px rgba(0,0,0,.2); z-index: 20; width: 520px; padding: 15px;
	img{display: block; max-width: 100%; height: auto; max-height: none!important;}
	&:before{.pseudo(15px,15px); background: #fff; .rotate(45deg); left: 65px; top: -6px;}
}
@media screen and (min-width: 1240px) {
	.cp-list-attr-energetski-razred{
		&:hover .attr-tooltip{display: block;}
	}
}

.cp-list-attr-title{
	text-align: center;
	a{text-decoration: underline;}
}
.cp-list-attr-value{font-weight: bold; font-size: 15px; line-height: 17px; width: 100%; text-align: center; padding: 3px 7px 0;}
.cp-list-price{text-align: left; padding-right: 10px;}
.cp-list-payment{
	font-size: 12px; line-height: 14px; padding: 5px 0 20px;
	span{font-weight: bold; color: @red;}
}
.cp-list-wishlist-btn, .cp-list-btn-compare{
	height: 40px; display: flex; justify-content: center; align-items: center; padding-top: 2px; font-size: 15px; font-weight: bold; border: 1px solid @borderColor; text-decoration: none; color: @textColor; margin: 15px 0 0 0; .transition(all);
	&:before{.icon-wishlist_notactive; font: 15px/15px @fonti; color: @gray; margin: -2px 9px 0 0; .transition(color);}
	@media (min-width: 1300px){
		&:hover{
			text-decoration: none;
			&:before{color: @yellow;}
		}
	}
}
.cp-wishlist-remove{
	background: @gray; color: @white; border-color: @gray;
	&:before{.icon-wishlist_active; color: @yellow;}
}
.cp-list-wishlist{position: relative;}
.cp-list-wishlist-btn{
	text-decoration: none;
	&:hover{text-decoration: none;}
	&.cp-wishlist-remove{display: none;}
}
.cp-list-compare-list .cp-compare-info, .cp-list-wishlist-list .wishlist-message{
	transform: translate(-50%);
	&:after{left: 50%; margin-left: -5px;}
}
.cp-list-wishlist-list .wishlist-message{left: 50%;}

.cp-list-btn-compare{
	margin: 0; text-decoration: none;
	&:hover{text-decoration: none;}
	&:before{.icon-compare_redesign; font-size: 19px; line-height: 19px;}
	small{font-size: 15px;}
	.l2{display: none;}
	&.compare_active{
		background: @gray; color: @white; border-color: @gray;
		&:before{color: @yellow;}
		.l1{display: none;}
		.l2{display: block;}
	}
}

.cp-list-wishlist.active{
	.cp-wishlist-add{display: none;}
	.cp-wishlist-remove{display: flex;}
}
.cp-list-btn-addtocart{
	height: 50px; display: flex; background: @green2; color: #fff; justify-content: center; align-items: center; text-decoration: none; font-size: 17px; line-height: 23px; padding-top: 2px; .transition(all);
	span{
		position: relative; padding-left: 28px;
		&:before{.icon-cart_notactive; font: 18px/20px @fonti; position: absolute; left: 0; top: -1px; color: @white;}
	}

	&:hover{color: #fff;}
}
.cp-list-comments{display: inline-block; vertical-align: top; text-decoration: none; font-size: 13px; line-height: 16px; margin: 3px 0 0 5px;}
.cp-list-brand{
	position: absolute; top: 15px; left: 20px; z-index: 10;
	img{max-width: 100%; height: auto; width: auto; .grayscale();}
}
.cp-list-badge{left: 10px; bottom: 10px;}
.cp-list-badge-special{position: absolute; top: 17px; right: 10px; color: @red; font-weight: bold; font-size: 15px; line-height: 20px; z-index: 10;}
.cp-list-badge-gift{bottom: 10px; right: 10px;}
.cp-list-image{
	height: 100%;
	a{display: block; height: 100%; display: flex; align-items: flex-end; justify-content: center; padding-top: 25px;}
}
.cp-list-rating{height: 26px;}


@media screen and (min-width: 1240px) {
	.cp-wishlist>a:hover{
		background: @gray; border-color: @gray;
		&:before{color: @yellow;}
	}
	.cp-btn-compare:hover{
		background: @gray; border-color: @gray;
		&:before{color: @yellow;}
	}
	.cp-btn-addtocart, .cp-list-btn-addtocart{
		&:hover{text-decoration: none; color: #fff; background: @gray;}
	}
	.cp-list-wishlist-btn, .cp-list-btn-compare{
		&:hover{color: @textColor; background: @gray; border-color: @gray; color: #fff;}
	}
}

.cp-extra-price-lowest{
	display: block; font-size: 10px; line-height: 14px; /* max-width: 145px; */ color: rgba(49,49,49,0.6); padding-top: 4px;
	strong{color: rgba(49,49,49,0.7); font-weight: normal;}
}
/*------- /catalog post -------*/

/*------- catalog detail -------*/
.cd-wrapper{padding-top: 38px;}
.cd-row{display: flex;}
.cd-col1{width: 680px; flex-grow: 0; flex-shrink: 0; margin: 0 0 0 30px; position: relative;}
.cd-col2{flex-grow: 1; padding-left: 95px;}
.cd-row2{
	margin-top: 45px; padding-bottom: 80px;
	&:before{.pseudo(auto,80px); background: @gray; left: 0; right: 0;}
}
.cd-col3, .cd-col4{width: 50%; position: relative;}
.cd-col3{padding: 26px 0 0;}
.cd-col4{padding-left: 110px;}

.cd-hero-slider.has-slider{
	display: none;
	&.slick-initialized{display: block;}
}
.cd-hero-slide{
	display: block;
	img{max-width: 100%; height: auto; display: block; margin: auto;}
	span{display: flex; height: 680px; justify-content: center; align-items: center;}
}
.cd-thumbs-slider{
	position: relative;
	.slick-arrow{
		position: absolute; border-radius: 0; box-shadow: none; left: 0; top: 0; width: 35px; height: 100%; border: 1px solid @borderColor; background: #fff;
		&:before{font-size: 15px; line-height: 120px; color: @borderColor;}
		&:hover{
			background: none;
			&:before{color: @yellow;}
		}
		&.slick-disabled:before{color: @borderColor!important; opacity: .6;}
	}
	.slick-prev:before{.scaleX(-1);}
	.slick-next{
		left: auto; right: 1px;
		&:before{.scale(1);}
	}
	&.has-slider{
		padding: 0 35px; display: none;
		&.slick-initialized{display: block;}
	}
}
.cd-brand{
	position: absolute; left: 0; top: 0; z-index: 1000;
	img{max-width: 100%; width: auto; height: auto; .grayscale();}
}
.cd-badges{position: absolute; top: 0; right: 0; z-index: 1000;}
.cd-badge{position: relative; top: auto; left: auto; margin-bottom: 10px;}
.cd-list-badge{height: auto!important;}
.cd-badge-gift{
	right: auto;
	span{position: absolute; width: 145px; top: 67px; left: -42.5px; text-align: center; text-transform: uppercase; font-weight: normal; color: @textColor; font-size: 14px; line-height: 20px;}
}
.cd-images{
	text-align: center;
	img{max-width: 100%; max-height: 90%; width: auto; height: auto;}
}
.cd-thumb{
	border: 1px solid @borderColor; margin: 0 0 0 -1px;
	span{display: flex; justify-content: center; align-items: center; width: 100%; height: 120px;}
	img{display: block; max-width: 90%; height: auto;}
	&.slick-current{box-shadow: inset 0px 0px 0px 2px @yellow; border-color: @yellow; z-index: 2; position: relative;}
}
.cd-code{font-size: 12px; line-height: 16px; text-transform: uppercase; padding: 0 0 2px;}
.cd-title{font-size: 24px; line-height: 28px; text-transform: none; padding: 0 0 8px;}
.cd-bc{padding: 0 0 15px;}
.cd-comments-info{font-size: 13px; line-height: 21px; display: inline-block; vertical-align: top;}
.cd-attr-energy{
	display: flex; font-size: 14px; line-height: 1; align-items: center; width: 65%; margin: 0;
	img{margin-right: 8px;}
}
.cd-price-container{margin: 15px 0 0; position: relative;}
.cd-price-container-row{
	display: flex; align-items: center; flex-wrap: wrap; row-gap: 15px;
	@media (max-width: @m){
		&.no-energy-badge .cd-price{width: 100%;}
	}
}
.cd-qty-shipping-price{
	display: flex; margin-top: 30px; flex-flow: column;
	@media (max-width: 1250px){margin-top: 25px;}
}
.cd-available-qty{
	font-size: 15px; line-height: 20px; padding-left: 48px; position: relative; padding-bottom: 20px;
	span{font-weight: bold;}
	&:before{.pseudo(34px,27px); left: 0; top: -5px; background: url(images/icons/pallet.svg) top left no-repeat; background-size: contain;}
}
.cd-badge-discount{display: flex; justify-content: center; align-items: center; flex-grow: 0; flex-shrink: 0; width: 60px; height: 60px; background: @red; color: #fff; font-size: 20px; line-height: 21px; border-radius: 300px; float: left; margin: 3px 15px 0 0;}
.cd-price{font-size: 12px; line-height: 15px; margin-right: 30px; white-space: nowrap;}
.cd-current-price{
	font-size: 30px; font-weight: bold; margin-top: 3px; display: block;
}
.cd-shipping-price{
	font-size: 15px; line-height: 19px; padding-left: 48px; position: relative; margin-top: 0;
	&:before{.icon-truck; font: 27px/25px @fonti; color: @yellow; position: absolute; left: 0; top: 5px;}
	span{display: block; font-size: 12px; line-height: 14px; color: #939393;}
}
.cd-shipping-price-free{padding-top: 10px;}
.cd-old-price{text-decoration: line-through;}
.cd-lowest-price-cnt{display: grid; grid-template-columns: min-content auto; margin-top: 20px;}
.cd-lowest-price{
	font-weight: bold; font-size: 30px; line-height: 26px; padding-top: 4px;
	.formated-price{
		line-height: 26px;
		.p-small{font-size: 13px;}
	}
}
.cd-price-value{white-space: nowrap;}
.cd-lowest-price-note{
	font-size: 13px; padding: 5px 0 0 0; line-height: 17px;
	@media (max-width: @m){padding: 10px 0 0; width: auto; margin: 15px 0 0 -65px;}
	span{display: block;}
	&.no-discount{
		margin-top: 20px;
		@media (max-width: @m){margin-left: 0;}
		@media (max-width: @ms){margin-top: 0;}
	}
}
.cd-lowest-coupon-price{
	font-size: 22px; line-height: 1; margin-top: 15px; color: @red;
	&>span:first-child{
		position: relative; padding-left: 26px; display: inline-block; white-space: nowrap;
		&:before{.icon-coupon; font: 19px/1 @fonti; color: @red; position: absolute; top: 2px; left: 0;}
	}
	.formated-price{line-height: 24px;}
}
.cd-lowest-coupon-price-cnt{
	.cd-lowest-coupon-price{
		padding-right: 15px;
		@media (max-width: @m){padding-right: 0;}
	}
}
.cd-lowest-coupon-price-desc{
	margin-top: 5px; font-size: 13px; line-height: 1.3;
	p{padding: 0;}
}
.cd-save{font-size: 16px; line-height: 21px; align-items: center; margin-right: 30px;}
.cd-save-label{font-weight: bold; color: @red;}
.cd-payment{
	font-size: 13px; line-height: 15px; position: relative; z-index: 100;
	&:not(:last-child){margin-right: 10px;}
}
.btn-installments-calc{
	border: 1px solid @borderColor; height: 45px; display: inline-flex; align-items: center; justify-content: center; padding: 0 20px; text-decoration: none; font-size: 15px; font-weight: bold; .transition(all); width: 100%;
	@media (max-width: @m){font-size: 14px; padding: 0 15px; height: 40px;}
	&:hover, &.active{text-decoration: none; border-color: @gray;}
}
.btn-installments-calc-leanpay{
	img{
		display: block; top: -1px; position: relative; margin-left: 6px;
		@media (max-width: @m){height: 13px; width: auto;}
	}
}
.btn-installments-calc1{
	span{
		display: flex; align-items: center;
		&:before{
			content:""; background: url(images/icons/cards.svg) no-repeat 0 0; background-size: contain; margin: 0 10px 0 0; width: 22px; height: 22px;
			@media (max-width: @m){width: 18px; height: 18px; margin: 0 6px 0 0; top: -2px;}
		}
	}
}
.cd-payment-installments{
	position: absolute; background: #fff; padding: 11px 20px; box-shadow: 0 1px 20px rgba(0,0,0,.2); left: 0; top: 57px; width: 340px; text-align: left; display: none;
	&:before{.pseudo(10px,10px); background: #fff; .rotate(45deg); left: 27px; top: -5px;}
	&.active{display: block;}
	ul{
		list-style: none; padding: 0 0 15px; margin: 0;
		li{padding: 8px 15px;}
		li:nth-child(odd){background: #F6F6F6;}
	}
}
.cd-payment-installments-cards{
	margin: 7px 0 0 0;
	p{display: flex; align-items: center;}
	img{margin-right: 5px;}
}
.cd-payment-installments-leanpay{
	.translate(-50%); left: 50%; padding: 25px 20px 10px;
	@media (max-width: @t){.translate(0); left: auto; right: 0;}
	&:before{
		left: 50%; margin-left: -5px;
		@media (max-width: @t){left: auto; margin: 0; right: 45px;}
	}
	p:last-child{
		margin-top: 10px;
		a{
			position: relative; padding-right: 10px; margin-right: 6px;
			&:not(:last-child):after{.pseudo(1px,12px); top: 2px; right: 0; background: @borderColor;}
		}
	}
}
.cd-payment-title{
	font-size: 15px; font-weight: bold; margin-bottom: 10px;
	img{vertical-align: middle; position: relative; top: -4px; margin-left: 8px;}
}
.leanpay-desc{line-height: 1.4;}
.cd-payment-installments-close{
	width: 25px; height: 25px; position: absolute; right: 10px; top: 10px; display: flex; justify-content: center; align-items: center; text-decoration: none;
	&:before{.icon-close; font: 11px/11px @fonti; color: @textColor; font-weight: bold;}
	&:hover{text-decoration: none;}
}
.cd-installment-card{padding: 5px 0;}
.cd-installment-row{
	padding: 2px 0;
	.label{font-weight: bold;}
}
.cd-btns{display: flex; padding: 35px 0;}
.cd-wishlist{position: relative;}
.cd-wishlist .cp-list-wishlist-btn{margin: 0; height: 50px; margin-right: 10px; padding: 0 30px; min-width: 200px; padding: 0 15px;}
.cd-btn-compare{
	height: 50px; margin-right: 10px; padding: 0 15px; min-width: 130px;
	small span{display: none;}
}
.cd-compare .cp-compare-info, .cd-wishlist .wishlist-message{
	bottom: unset; top: calc(~"100% - -10px"); left: calc(~"50% - 5px"); transform: translate(-50%); font-size: 14px; line-height: 16px; z-index: 1;
	&:after{top: -2px; bottom: unset; left: calc(~"50% - 5px"); transform: rotate(45deg) translate(-50%);}
	@media (max-width: @m){
		font-size: 13px; line-height: 16px; top: calc(~"100% - -7px");
		&:after{width: 8px; height: 8px;}
	}
}

.cd-compare{
	.cp-compare-container{display: block; width: 100%;}
	.cp-compare-info{
		top: calc(~"100% - -20px");
		@media (max-width: @m){
			left: unset; right: 0; transform: unset; top: calc(~"100% - -17px");
			&:after{left: unset; transform: rotate(45deg); right: 69px; top: -4px;}
		}
	}
}
.cd-btn-add{text-transform: none; padding: 0;}
.add-to-cart-container{flex-grow: 1;}
.cd-attrs{margin: 0 0 20px; padding: 1px 0 0 1px; content-visibility: auto; contain-intrinsic-size: 150px;}
.cd-short-description{
	font-size: 18px; line-height: 28px; padding: 0 0 30px;
	ul{.list;}
}

.cd-related-color-title{font-size: 17px; line-height: 24px; font-weight: bold; padding: 0 0 8px;}
.cd-related-color-products{display: flex; flex-wrap: wrap; padding: 0 0 40px;}
.cd-related-color-item{
	width: 20%; height: 100px; max-width: 100px; border: 1px solid @borderColor; margin: -1px 0 0 -1px; position: relative; .transition(all);
	&>span{display: flex; justify-content: center; align-items: center; height: 100%; padding: 6px;}
	img{display: block; margin: auto; max-width: 100%; max-height: 100%; height: auto; width: auto;}
	&:hover{box-shadow: inset 0 0 0 2px @yellow; border-color: @yellow; z-index: 2;}
}
.cd-related-size-item>span{padding-bottom: 15px;}
.cd-related-size-title{position: absolute; text-decoration: none; font-size: 10px; line-height: 1.4; left: 0; right: 0; bottom: 2px; text-align: center;}
.cd-related-slider{
	.slick-arrow{
		width: 47px; height: 47px; top: 150px;
		&:before{line-height: 47px; font-size: 13px;}
	}
	.slick-prev{left: -24px;}
	.slick-next{right: -22px;}
	.slick-list{padding: 1px 0 0 1px; margin-left: -1px;}
}

.cd-upsale{border-top: 1px solid @borderColor; padding: 50px 0 80px;}
.cd-upsale-title{text-align: center; font-size: 36px; line-height: 50px; font-weight: bold; padding: 0 0 30px; color: #000;}

.cd-attribute{
	display: flex; font-size: 17px; line-height: 22px; border: 1px solid @borderColor; border-bottom: 0;
	/*&:nth-child(odd){background: #F6F6F6;}*/
	&:last-child{border-bottom: 1px solid @borderColor;}
}
.cd-attribute-group{
	flex-grow: 0; flex-shrink: 0; font-weight: bold; text-transform: uppercase; padding: 9px 10px 7px 20px; width: 30%; border-right: 1px solid @borderColor; border-bottom: 0; position: relative; color: @red;
	&.empty:after{.pseudo(auto,3px); left: 0; right: 0; top: -1px; background: #fff;}
}
.cd-attribute-title{width: 30%; flex-grow: 0; flex-shrink: 0; border-right: 1px solid @borderColor; text-align: right; padding: 10px 20px 7px 10px;}
.cd-attribute-value{padding: 9px 10px 7px 20px; flex-grow: 1;}
.cd-related-title{font-weight: bold; font-size: 36px; line-height: 45px; padding: 0 0 20px; color: #000;}
.cd-related-products{padding: 30px 0 0;}

.cd-benefits{display: flex; padding: 21px 0 0 0; min-height: 80px;}
.cd-benefit{
	position: relative; color: #fff; font-size: 15px; line-height: 20px; display: block; padding: 0 0 0 46px; text-decoration: none;
	span{font-size: 13px; line-height: 17px; display: block; font-weight: 300;}
	&:before{font: 35px/35px @fonti; color: @yellow; position: absolute; left: 0; top: 0;}
	&:hover{text-decoration: none; color: #fff;}
	&.benefit-shipping{
		padding-left: 55px;
		&:before{font-size: 32px;}
	}
	//&.benefit-payment{margin-left: 50px;}
}
.cd-benefit-item:last-child{margin-left: 50px;}
.cd-inquiry{
	max-width: 400px; padding-bottom: 5px;
	textarea{height: 100px;}
}
.cd-inquiry-title{
	font-size: 15px; line-height: 21px; padding: 40px 0 17px;
	h2{text-transform: none; padding: 0; font-size: 28px; line-height: 34px;}
	p{padding: 0;}
}
.btn-cd-inquiry{min-width: 180px;}
.cd-inquiry-success{color: @green;}

.tabs{
	display: flex; height: 54px; text-align: center; font-size: 16px; font-weight: bold; list-style: none; padding: 0; margin: 0;
	a{color: #fff; background: #191716; display: flex; height: 100%; justify-content: center; align-items: center; text-decoration: none; padding: 0 10px; transition: color .3s, background .3s;}
	li{margin-right: 1px; flex-grow: 1;}
	.active a{color: @textColor; background: #fff;}
}
.tab{
	display: none;
	&.active{display: block;}
}
.tab-counter{font-size: 14px; padding-left: 3px;}
.tabs-content{padding: 40px 0 0; font-size: 18px; line-height: 28px;}
.tab-desc{
	ul{.list;}
	ol{margin: 0 0 15px 37px;}
	iframe{width: 100%; max-height: 400px;}
	img{max-width: 100%; height: auto;}
}
.loadbeeTabContent iframe{max-height: none;}

.tab-comments{
	.comments{padding: 0;}
	.comments-title{display: none;}
	.comments-subtitle{text-transform: uppercase;}
}
.btn-tab-toggle{display: none;}
.cd-documents{
	width: 100%; margin-bottom: 40px;
	thead td{background: #ececec;}
	td{padding: 5px 20px; font-size: 15px; line-height: 1.4; border-right: 1px solid #fff;}
	tr:nth-child(2n) td{background: #f5f5f5;}
}
.cd-documents-dl{width: 220px;}
.cd-documents-format{width: 125px; text-align: center;}
tbody .cd-documents-title{font-weight: bold;}
.cd-documents-dl{text-align: center;}
.btn-documents-dld{
	text-decoration: none; color: @textColor;
	span{
		position: relative; padding: 0 0 0 25px;
		&:before{.icon-download; font: 17px/17px @fonti; position: absolute; left: 0; top: -1px;}
	}
	&:hover{text-decoration: underline; color: @red;}
}
.cd-services{
	padding: 30px 0 0;
	@media(max-width: @m){padding-top: 25px;}
}
.cd-services-title{font-weight: bold; padding-bottom: 7px;}
.cd-services-items{
	p{
		padding: 0 0 3px;
		@media (max-width: 990px){padding: 0 0 10px;}
		&:last-child{padding: 0;}
	}
}
.cd-services-price{color: @red;}

.cd-extra-price-lowest{color: rgba(49,49,49,0.6);
	strong{color: rgba(49,49,49,0.7);}
}
/*------- /catalog detail -------*/

/*------- catalog zoom gallery -------*/
.fancybox-active{position: fixed; width: 100%; overflow: hidden;}
.gallery { overflow: hidden; }
.ratioHolder {width:100%; padding-bottom:66.666667%; position:relative;}
.sliderHolder {position: absolute; top: 0px; left: 0px;}
.superCaption {padding:5px; }
.sliderHolder {width:100%; min-height:100%; height:100%; position:relative; overflow:hidden; -ms-touch-action:none; touch-action:none; }
.slider {overflow:hidden; position:absolute; top:0px; left:0px; width:100%; min-height:100%; background: #fff; }
.sliderBg {width:100%; min-height:100%; height:100%; position:absolute; left:0px; top:0px; }
.slides, .thumbs { overflow:hidden; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }
.slides{width:100%; min-height:100%; position:absolute; margin-right: 80px; }

.gsThumbsHolder { position: absolute; width: 100%; left: 0; right: 0; bottom:0; }
.gsThumbs {
	&>div>div { background: #fff; border: @gray; }
	img { max-width: 95%!important; height: auto!important; display: block; margin: auto; }
}
.fancybox-close {
	top:10px; right: 30px; background: #fff; width: 45px; height: 45px; text-decoration: none;
	&:before { .icon-close; font: 18px/45px @fonti; color: #000; position: absolute; left: 0; top: 0; width: 100%; text-align: center; }
	&:hover{text-decoration: none;}
}
.fancybox-gallery{
	.fancybox-close{
		border-radius: 100px; top: 15px; right: 15px; background: @red; .transition(all);
		&:before{color: #fff;}
		&:hover{background: darken(@red,10%);}
	}
}
.fancybox-skin{border-radius: 0!important;}
.gsZoom {
	position: absolute; top: 65px; right: 0; width: 45px; height: 45px; right: 15px; border-radius: 100px; background: @yellow; .transition(all);
	&:before { .icon-zoom-in; font: 20px/45px @fonti; text-indent: 2px; color: @textColor; position: absolute; left: 0; top: 0; width: 100%; text-align: center; }
	&:hover{background: darken(@yellow,10%);}
}
.gsZoomOut { top: 115px;
	&:before { .icon-zoom-out; }
}
.gsControl {
	position: absolute; right: 15px; top: 48%; background: #fff; width: 50px; height: 50px; border-radius: 100px; box-shadow: inset 0 0 0 3px @yellow; .transition(all);
	&:before { .icon-arrow-right; font: 13px/50px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; color: #000;}
	&:hover{background: @yellow;}
}
.gsPrev { right: auto; left: 15px;}
.gsPrev:before{.scaleX(-1);}
.zopim.fancybox-margin { display: none!important; }
/*------- /catalog zoom gallery -------*/

/*------- image zoom -------*/
.marker,.zoomHolder img{display:block}
.fullscreenToggle,.zoomIn,.zoomOut{background:url(images/pz_sheet.png) no-repeat;width:30px;height:30px;overflow:hidden;position:relative;display:block;cursor:pointer;z-index:100;margin:5px 0}
.zoomIn{background-position:0 0}
.zoomOut{background-position:-30px 0}
.fullscreenToggle{background-position:-60px 0;cursor:pointer}
.zoomIn.on,.zoomOut.on{opacity:1;-moz-opacity:1;-khtml-opacity:1;cursor:pointer}
.zoomIn.off,.zoomOut.off{opacity:.5;-moz-opacity:.5;-khtml-opacity:.5;cursor:auto}
.controlHolder{background: rgba(255,255,255,.7);border:1px solid lighten(@gray,5%);border-radius:1px;position:absolute;right:10px;top:10px;padding:5px;z-index:100}
.fullscreenToggle { display: none; }
.fullscreenDiv,.marker{left:0!important;top:0!important}
.fullscreenToggle.on{background-position:-90px 0}
.fullscreenToggle.off{background-position:-60px 0}
.fullscreenDiv{width:auto!important;height:auto!important;right:0!important;bottom:0!important;position:absolute!important;background:#FFF;z-index:999999; overflow:hidden}
.zoomHolder{width:100%;height:auto;position:relative;overflow:hidden}
.marker{position:absolute!important;bottom:auto!important;right:auto!important}
#zoom-container { position:absolute; left:0; top:0; z-index: 200;}
.zoomContainer { z-index:120; height:0!important; }
/*------- /image zoom -------*/

/*------- toolbar -------*/
.toolbar{display: flex; margin: 0 0 30px; padding-top: 30px; border-top: 1px solid @borderColor;}
.toolbar-col1{flex-grow: 1;}
.toolbar-col2{flex-grow: 0; display: flex;}

.c-layout{
	display: flex; text-transform: uppercase; font-weight: bold; font-size: 16px; line-height: 28px; align-items: center; margin-left: 40px;
	a{
		width: 40px; height: 40px; background: @borderColor; position: relative; font-size: 0; text-decoration: none; .transition(all);
		&:after{.icon-grid; font: 17px/40px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center;}
		&.active{background: @yellow;}
		&:not(.active):hover{background: darken(@borderColor,10%);}
	}
	.c-layout-list:after{.icon-list;}
}
.c-layout-label{padding-right: 20px; padding-top: 4px;}
.c-counter{font-size: 17px; padding: 7px 0 0;}

.sort{
	margin-left: 35px;
	select{height: 40px; border: 1px solid @borderColor; font-size: 17px; width: 200px;}
}
.toolbar-filter{
	position: relative; font-weight: bold; text-decoration: none; padding: 0 0 0 32px; margin-top: 9px;
	&:before{.pseudo(20px,20px); border: 1px solid @borderColor; font: 10px/20px @fonti; left: 0; top: 1px; text-align: center;}
	span{color: @red;}
	&:hover{text-decoration: none;}
	&.active:before{.icon-check; background: @yellow; border-color: @yellow;}
}
.toolbar-filter-mobile-cnt{display: none;}
.toolbar-filter-qty{padding-right: 45px;}
/*------- /toolbar -------*/

/*------- filter -------*/
.cf-sidebar-title, .cf-btns{display: none;}
.nav-sidebar-arrow{
	position: absolute; right: 0; top: 0; width: 40px; height: 40px; cursor: pointer; display: none;
	&:before, &:after{.pseudo(15px,3px); top: 20px; right: 0; background: @yellow;}
	&:after{width: 3px; height: 15px; left: 31px; top: 14px;}
}
.cf-item{
	border: 1px solid @borderColor; margin-top: -1px;
	&.active{
		.cf-item-wrapper{max-height: 5000px; visibility: visible; overflow: visible; padding-bottom: 15px;}
		.nav-sidebar-arrow:after{display: none;}
	}
	.nav-sidebar-arrow{right: 20px; top: 5px;}
}
.c-sidebar-title-filter{border-bottom: 1px solid @borderColor;}
.cf-title{
	font-size: 18px; line-height: 22px; padding: 15px 20px; text-transform: uppercase; font-weight: bold; position: relative; cursor: pointer; color: #000;
	.nav-sidebar-arrow{display: block;}
	&.active .nav-sidebar-arrow:after{display: none;}
}
.cf-item-wrapper{
	max-height: 0; padding: 0 20px; overflow: auto; visibility: hidden; .transition(all);
	&::-webkit-scrollbar{-webkit-appearance: none; width: 3px; background: @borderColor;}
	&::-webkit-scrollbar-thumb{background-color: @gray; border-radius: 5px;}
}
.cf-counter{color: #9B9B9B; font-size: 13px; font-weight: 300;}
.cf-clear{padding: 20px 0;}
.cf-row{
	margin-bottom: 4px; position: relative;
	input[type=checkbox]+label{display: block; padding-right: 26px;}
	input[type=checkbox]:checked+label .cf-counter{color: #000;}
}
.cf-row-not-available .cf-counter{display: none;}
.btn-cf-clear{padding: 0; width: 100%;}
.btn-apply-filters{display: none; width: 100%;}
.nav-cf-categories{
	list-style: none; padding: 0; margin: 0; font-size: 17px; line-height: 21px;
	li{margin: 2px 0;}
	a{
		text-decoration: none; color: @textColor; display: block; padding: 1px 15px 1px 0;
		&:hover, &.active{text-decoration: underline;}
	}
	ul{list-style: none; padding: 3px 0 5px 10px; margin: 0; font-size: 13px;}
}
.c-sidebar-title-categories{padding-bottom: 10px;}
.cf-active{font-size: 16px;}
.cf-active-item{
	position: relative; text-decoration: none; padding: 0 20px 0 20px; margin: 0 0 10px 20px; display: block; line-height: 19px;
	&:before{.icon-close2; font: 12px/12px @fonti; color: @red; position: absolute; left: 0; top: 3px;}
	&:hover{text-decoration: none;}
}
.btn-cf-active-clear{height: 40px; text-transform: uppercase; font-size: 12px; padding: 3px 12px 0; text-decoration: none; display: inline-flex; vertical-align: top; border: 1px solid @red; color: @red; justify-content: center; align-items: center; min-width: 160px; font-size: 16px; font-weight: bold; margin: 7px 0 20px 20px; .transition(all);
	&:hover{color: #fff; background: @red; text-decoration: none;}
}
.btn-m-cf-active-clear{display: none;}

.cf-tooltip{
	position: absolute; display: none; padding: 15px; box-shadow: 0 0 35px 0 rgba(49,49,49,0.25); top: -82px; right: -290px; width: 270px; z-index: 200; background: #fff; font-size: 14px; line-height: 1.3; color: @gray;
	@media (max-width: 990px){right: 32px;}
	@media (max-width: 1240px){
		&.active{display: block;}
	}
	img{display: block; margin-bottom: 15px; max-width: 100%; height: auto;}
	&:before{
		.pseudo(10px,10px); background: #fff; .rotate(45deg); top: 90px; left: -5px;
		@media (max-width: 990px){left: auto; right: -5px;}
	}
}
.cf-tooltip-title{font-weight: bold; font-size: 17px; line-height: 1.2; padding: 0 5px 5px 5px;}
.cf-tooltip-desc{padding: 0 5px;}
.cf-tooltip-footer{border-top: 1px solid @borderColor; padding: 15px 20px 0; font-size: 13px; font-weight: bold; line-height: 1.2; margin: 13px -15px 0;}
.cf-row-detail{
	position: absolute; right: -1px; top: 4px; width: 16px; height: 16px; z-index: 10;
	&:after{
		content:"i"; display: flex; align-items: center; justify-content: center; width: 17px; height: 17px; border: 2px solid @gray; border-radius: 100px; font: 12px/1 Arial; color: @gray; position: absolute; right: 0; top: 0; font-weight: bold; box-sizing: border-box; transition: background-color .3s, color .3s; text-indent: 1px;
		@media (max-width: 990px){text-indent: 0;}
	}
	@media (min-width: 1200px){
		&:hover + .cf-tooltip{display: block;}
		&:hover:after{background: @gray; color: @yellow;}
	}
	@media (max-width: 1200px){
		&.active:after{background: @gray; color: @yellow;}
	}
}
@media (max-width: 1200px){
	.btn-close-tooltip{
		display: block; position: absolute; right: 0; top: 0; width: 26px; height: 26px; background: @gray;
		&:before{.icon-close; font: 11px/27px @fonti; color: #fff; position: absolute; left: 0; top: 0; right: 0; bottom: 0; text-align: center; text-indent: 1px;}
	}
}
/*------- /filter -------*/

/*------- brands -------*/
.page-brands{
	.main-wrapper{padding-bottom: 30px;}
}
.m-wrapper{width: 100%;}
.m-header{text-align: center;}
.m-items{padding-top: 40px; font-size: 0; display: flex; flex-wrap: wrap; width: 100%;}
.m-column{width: 16.666%; padding: 0 10px 55px; text-align: center;}
.m-list{
	list-style: none; padding: 0; margin: 0;
	li{
		padding: 0 0 5px 0; font-size: 16px; line-height: 21px;
		a{text-decoration: none;}
		&:before{display: none !important;}
	}
	a{
		display: inline-block; vertical-align: top; color: @textColor;
		&:hover{text-decoration: underline;}
	}
}
.m-letter{font-size: 26px; line-height: 56px; background: @gray; color: @yellow; display: inline-block; margin: 0 0 15px 0; padding: 0; width: 56px; height: 56px; text-align: center;}
.m-special{display: flex; flex-wrap: wrap; justify-content: center; margin: 20px 0;}
.m-special-item{
	border: 1px solid @borderColor; height: 88px; width: 16.666%; margin: -1px 0 0 -1px; display: flex; justify-content: center; align-items: center;
	img{max-width: 100%; max-height: 30px; width: auto; height: auto; opacity: .6; display: block; .transition(opacity); .grayscale();}
	span{max-width: 100%; display: block;}
	&:hover{
		img{opacity: 1;}
	}
}
.cm-logo{
	margin: 40px 0;
	img{max-width: 100%; max-height: 60px; width: auto; height: auto; display: block; margin: auto;}
}
/*------- /brands -------*/

/*------- publish widget -------*/
.pw{
	padding: 75px 0 80px;  content-visibility: auto; contain-intrinsic-size: 1300px;
	.pp-cnt{padding: 25px 0 0;}
	.pp-featured{
		.pp-title{padding: 0 0 11px; text-transform: uppercase;}
		.pp-cnt{padding-top: 36px; padding-bottom: 0;}
	}
	.pp-featured-small{
		.pp-cnt{padding-bottom: 0;}
	}
}

.pw-title{
	font-size: 48px; line-height: 50px; font-weight: 300; padding: 0 0 35px 73px;
	a{
		text-decoration: none;
		&:hover{color: @textColor;}
	}
}
.p-featured{display: grid; grid-template-rows: auto; grid-template-columns: calc(66.666% - 21px) calc(100% / 3 - 35.5px); column-gap: 46px; row-gap: 40px; padding-bottom: 100px; position: relative;}
.pw-btns{text-align: center; padding: 50px 0 0;}
.btn-all{min-width: 240px;}
/*------- /publish widget -------*/

/*------- publish -------*/
.p-header{padding: 45px 60px 30px 60px;}
.p-items{display: flex; flex-wrap: wrap;}
.p-items-blog{
	.p-featured{padding-bottom: 40px;}
	.pp-featured-small:last-child{margin-bottom: 0;}
}
.pw .pp{margin-bottom: 0;}
.load-more-container{text-align: center;}
.btn-load-more{min-width: 240px; margin: 0 0 70px;}
.load-more-loader{
	width: 100%; text-align: center; margin: 0 0 55px;
	span{display: inline-block; vertical-align: top; background: url(images/loader.gif) no-repeat center top; background-size: 50px auto; padding: 45px 0 0;}
}
.blog-label{top: 165px; left: -75px; bottom: auto; .rotate(180deg);}
/*------- /publish -------*/

/*------- publish post -------*/
.pp{
	display: block; text-decoration: none; width: calc(~"100% / 3 - 33.5px"); margin: 0 47px 50px 0; text-align: center; font-size: 18px; line-height: 26px;
	&:hover{text-decoration: none; color: @yellow;}
	&:nth-of-type(3n){margin-right: 0;}
}
.pp-image{
	img{max-width: 100%; height: auto; display: block;}
}
.pp-cnt{
	margin: 0 45px; padding: 20px 0 0; position: relative;
	&:before{.pseudo(100%,6px); background: @yellow; top: -4px; left: 0;}
}
.pp-title{font-size: 28px; line-height: 36px; font-weight: bold; margin: 0; padding: 0; text-transform: none;}
.pp-short-desc{font-weight: 300; color: @textColor!important;}

.pp-featured{
	width: 100%; text-align: left; font-weight: 300; margin-bottom: 0; grid-area: ~"1 / 1 / 3 / 2";
	.pp-title{font-size: 42px; line-height: 50px; padding: 0 0 8px;}
	.pp-cnt{
		margin: 0 65px 0 70px;
		&:before{height: 10px; top: -6px;}
	}
}

.pp-featured-small{
	margin-right: 0!important; width: 100%!important;
	.pp-title{padding: 0 0 5px;}
	.pp-cnt{padding-bottom: 0;}
	&:first-child{ grid-area: ~"1 / 2 / 2 / 3"; }
	&:last-child{ grid-area: ~"2 / 2 / 3 / 3"; }
}
/*------- /publish post -------*/

/*------- publish detail -------*/
.pd-header{text-align: center; padding: 50px 0 25px;}
.pd-content{
	margin: auto;
	img{margin-left: -60px; max-width: calc(~"100% - -120px");}
}
.pd-hero-image{
	margin-bottom: 40px;
	img{display: block; max-width: 100%; height: auto; margin: auto;}
}
.pd-title{text-transform: none;}
.pd-info{
	font-size: 0; line-height: 20px;
	&>div{display: inline-block; vertical-align: top; font-size: 14px;}
	.cp-rate{
		padding-left: 40px; margin-left: 10px; position: relative; padding-top: 0; padding-right: 0;
		&:after{.pseudo(30px,3px); background: @yellow; top: 8px; left: 0;}
	}
}
.pd-comments{
	position: relative; padding: 0 0 0 40px; margin-left: 10px;
	&:after{.pseudo(30px,3px); background: @yellow; left: 0; top: 8px;}
}
.pd-thumbs{
	margin-top: 20px;
	a{margin: 0 0 15px; display: block;}
	img{display: block;}
}
.pd-related{
	border-top: 1px solid @borderColor; margin-top: 80px; padding: 55px 0 0;
	.pp{margin-bottom: 0;}
}
.pd-related-title{text-align: center; font-size: 42px; line-height: 50px; padding: 0 0 30px; font-weight: 300;}
.pw-related-btns{padding: 35px 0 80px; text-align: center;}
/*------- /publish detail -------*/

/*------- search -------*/
.s-header{background:@gray; color: #fff; margin: 30px 0 40px; text-align: left; padding: 0; max-width: 100%;}
.s-h1{font-size: 26px; line-height: 28px; padding: 30px 0 15px; color: #fff;}
.s-headline{color: @yellow; text-transform: uppercase; display: block; font-size: 18px; line-height: 23px;}
.s-keyword{text-transform: none; font-weight: 300;}
.s-nav{
	position: absolute; display: flex; bottom: 0; right: 0; font-size: 0; font-weight: bold; text-transform: uppercase; font-size: 18px; list-style: none; padding: 0; margin: 0;
	li{
		margin-left: 1px;
		&.selected a{
			background: #fff; color: @textColor;
			.s-counter{color: @textColor;}
		}
	}
	a{display: flex; justify-content: center; align-items: center; height: 54px; min-width: 180px; background: #000; text-decoration: none; color: #fff;}
}
.s-item{padding: 0 0 50px;}
.s-counter{font-size: 13px; color: @yellow; line-height: 15px; padding-left: 5px;}
.s-items{max-width: 760px; margin: auto; padding-bottom: 30px;}
.s-item-title{font-size: 24px; line-height: 35px; padding: 0 0 5px; text-decoration: underline;}
.s-item-cnt{font-size: 18px;}
.s-no-results{padding: 0 0 50px;}
/*------- /search -------*/

/*------- wishlist -------*/
.wishlists-title{
	position: relative; padding: 0 0 0 45px	;
	&:before{.icon-wishlist; color: @yellow; position: absolute; left: 0; top: 11px; font: 27px/27px @fonti;}
}
.wishlist-title-counter{font-size: 25px; line-height: 32px; font-weight: normal;}
/*------- /wishlist -------*/

/*------- comments -------*/
.comments{padding: 65px 0 0;}
.comments-2{
	.comments-subtitle, .comments-list-title{display: none;}
}
.comments-title{font-size: 30px; line-height: 33px; font-weight: bold; text-transform: uppercase; border-bottom: 1px solid @borderColor; margin: 0 0 20px; padding: 0 0 10px;}
.comments-subtitle{font-size: 19px; line-height: 27px; font-weight: bold; padding: 0 0 15px;}
.comments-list-title{text-transform: uppercase; font-weight: bold; font-size: 20px; line-height: 26px; border-bottom: 1px solid @borderColor; padding: 0 0 7px;}
.no-comments{padding-top: 10px;}
.comment{
	font-size: 15px; line-height: 21px; padding: 12px 0; border-bottom: 1px solid @borderColor;
	&:last-of-type{padding-bottom: 0; border: 0;}
	.comment-form-container{padding-top: 25px; margin-bottom: 0;}
}
.comment-form{font-size: 0;}
.comment-field{
	display: inline-block; vertical-align: top; width: 48%; margin-right: 4%; padding-bottom: 20px;
	textarea{display: block;}
}
.comment-field-email{margin-right: 0;}
.comment-field-rate{
	width: 100%; margin: 12px 0 10px; padding-left: 20px;
	input{visibility: hidden;}
	&>span{display: inline-block; vertical-align: top; margin: -3px 0 0 0;}
	.label{font-size: 15px; line-height: 24px; display: inline-block; vertical-align: top; padding-right: 10px;}
}
.comment-field-message textarea{height: 120px;}
.comment-rate, .cp-rate{
	font-size: 0!important; padding: 1px 5px 0 0; height: 20px; display: inline-block; vertical-align: top;
	span{
		display: inline-block; vertical-align: top; position: relative; width: 16px; height: 16px; margin-right: 4px;
		&:after{.icon-star; font: 16px/16px @fonti; color: @borderColor; position: absolute; left: 0; top: 0;}
		&.icon-star:after{color: @yellow;}
	}
}
.comment-rate-item{
	input[type=radio]+label{
		padding: 0; font-size: 0; width: 21px; height: 21px; margin: 0 2px;
		&:before{display: none;}
		&:after{.icon-star; font: 20px/20px @fonti; position: absolute; left: 0; top: 0; border: 0; border-radius: 0; color: @borderColor; background: none;}
	}
	input[type=radio]:checked+label:after, &.active input[type=radio]+label:after{color: @yellow;}
}
.comment-field-message{display: block; width: 100%; padding-bottom: 20px;}
.btn-send-comment{width: 100%;}
.comment-form-note{font-size: 12px; line-height: 16px; color: @gray; padding: 20px 30px 0 20px; float: left; width: 70%;}
.comment-review{font-size: 0; line-height: 32px; padding: 20px 0 0 0; float: right;}
.comment-review-label{font-size: 13px; padding: 0 15px 0 0;}
.review_comment{float: right;}
.review_links{display: inline-block; vertical-align: top;}
.review-icon{
	color: #fff; text-decoration: none; display: inline-block; font-size: 13px; padding: 0 15px; min-width: 80px; height: 32px; background: #000; line-height: 32px; position: relative; text-align: left; .transition(all);
	.label{padding-right: 24px; margin-right: 6px; position: relative;}
}
a.review-icon:hover{color: #fff; text-decoration: none; background: #000;}
.comment-header{padding: 0 0 7px 0; font-size: 15px;}
.comment-success-message{padding-bottom: 20px;}
.btn-comment-replay{
	text-decoration: underline; text-transform: uppercase; font-weight: 500; display: inline-block; vertical-align: top; margin-top: 25px; font-size: 12px;
	&.hide{display: none;}
}
.comment-buttons{text-align: center; float: right; width: 30%;}
.btn-comment-cancel{
	text-decoration: underline; display: inline-block; vertical-align: top; text-transform: uppercase; margin-top: 12px; font-size: 12px; font-weight: 500;
	&:hover{text-decoration: underline;}
}
.comment-child{padding: 10px 0 25px 50px; border: 0;}
.comment-form-container{margin-bottom: 30px;}
.comment-form-login{padding-right: 130px; padding-bottom: 10px;}
.comment-review-success{font-size: 12px; font-weight: bold; text-transform: uppercase; color: @green;}
.comment_success{font-size: 16px; line-height: 20px; padding-bottom: 15px;}
.comments-load-more{text-align: center; font-size: 16px;}
.btn-load-more-comments{display: inline-block; vertical-align: top; margin: 55px 0 0;}
/*------- /comments -------*/

/*------- sweepstake -------*/
.swsp-title{font-size: 20px;}
.page-sweepstake-detail{
	.main-wrapper{display: block;}
	.footer{border-top: 1px solid @borderColor;}
}
.sws-title{
	font-size: 42px; line-height: 55px; position: relative; padding: 0 0 30px 80px;
	&:before{.pseudo(60px,70px); background: url(images/icons/face-black.svg) no-repeat left top; background-size: contain; left: 0; top: -17px;}
}
.sweepstake-title{font-size: 32px; line-height: 38px; padding: 0 0 25px; text-transform: none;}
.sweepstake-subtitle{padding: 0 0 10px; font-size: 14px; line-height: 18px; text-transform: uppercase;}
.sweepstake-results-title { font-size: 44px; font-weight: bold; line-height: 54px; text-align: center;}
.sweepstake-no-results-title{font-size: 25px; line-height: 35px;}
.sweepstake-fields{margin-bottom: 55px; position: relative;}
.sweepstake-results-qty{padding: 15px 0 5px;}
.sweepstake-field{
	&>span { display: block; margin-bottom: 5px; }
	input[type=checkbox]+label, input[type=radio]+label{
		font-size: 17px; line-height: 22px; padding: 0px 0 0 35px;
	}
	input[type=checkbox]+label{padding-top: 1px;}
	img{float: left; margin: 0 15px 0 0; display: block;}
	input,textarea{width: 400px; display: block;}
}
.layer {
	visibility: hidden; height: 0; overflow: hidden;
	&.show{ -webkit-animation: show 1s forwards ease;  animation: show 1s forwards ease; visibility: visible; height: auto; }
	&.hide { -webkit-animation: hide 1s forwards ease; animation: hide 1s forwards ease; }
}
.btn-sweepstake{
	&:after{.pseudo(36px,auto); top: 0; bottom: 0; right: 0; text-align: center; background: @yellow; .icon-arrow-right; font: 12px/55px @fonti; color: #000;}
}
.btn-sweepstake-next{
	float: right; padding-right: 60px; padding-left: 30px;
}
.btn-sweepstake-prev{
	padding-right: 30px; padding-left: 60px; background: none; border: 1px solid @borderColor; color: @textColor;
	&:after{right: auto; left: 0; .scaleX(-1); background: #fff; border-left: 1px solid @borderColor; .transition(all);}
	&:hover{
		&:after{background: @yellow;}
	}
}
.btn-sweepstake-disabled{
	cursor: default; display: none;
	&:hover{text-decoration: none; color: #F1F0CC;}
}
.sweepstake-error{background: @red; color: #fff; display: inline-block; padding: 6px 13px; margin: 0 0 20px;}
.sweepstake-pager-cnt{
	height: 35px; border-bottom: 1px solid @borderColor; border-top: 1px solid @borderColor; line-height: 34px; margin: 0 0 35px;
	.wrapper{
		position: relative;
		&:before, &:after{.pseudo(500px,auto); top: 0; bottom: 0; background: @gray; left: -500px;}
		&:after{left: 100%; background: none; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; top: -1px;}
	}
}
.sweepstake-pager{
	list-style: none; padding: 0; margin: 0; display: flex;
	li{
		color: @textColor; align-items: center; justify-content: center; font-weight: bold; font-size: 16px;padding: 0 8px 0 30px; color: #8f8f8f; position: relative; z-index: 0;
		&:after, &:before{.pseudo(24px,24px); border: 1px solid @borderColor; border-left: 0; border-bottom:0; .rotate(45deg); position: absolute; right: -12px; top: 4px;  z-index: 1;}
		&.active, &.completed{
			background: @gray; z-index: 5; padding-left: 30px; color: #fff; position: relative;
			&:after{background: @gray; border-color: #fff;}
			&:before{right: auto; left: -13px; border-color: #fff; background: @gray; z-index: 10;}
		}
		&.completed{z-index: 11; overflow: hidden;}
		&.active{
			background: @yellow; color: #000; z-index: 10;
			&:after{background: @yellow;}
		}
		span{position: relative; z-index: 12;}
	}
}
.btn-sweepstake-cancel{position: absolute; top: 0; right: 0; text-decoration: none; text-transform: uppercase; font-size: 16px; font-weight: bold; color: @textColor; padding: 2px 22px 0 0;
	&:hover{text-decoration: none;}
	&:after{.icon-close2; font: 13px/13px @fonti; position: absolute; right: 0; top: 11px; color: @red;}
}
.sweepstake-results-recommend{background: @green; width: 75px; height: 75px; line-height: 75px; color: #fff; border-radius: 100px; display: inline-block; vertical-align: top; margin: 10px 0; text-align: center; font-size: 18px; font-weight: bold;}
.sweepstake-thank-you{text-align: center; padding: 120px 0 0;}

.c-sweepstake-header{
	background: @gray; color: #fff; margin: 30px 0 35px;
	h1{font-size: 24px; line-height: 32px; text-transform: none; padding: 0; font-weight: normal; color: #fff;}
}
.c-sweepstake-label{
	position: relative; padding: 0 0 0 60px; font-weight: bold;
	&:before{.pseudo(45px,55px); background: url(images/icons/face.svg) no-repeat left top; background-size: contain; left: 0; top: -7px;}
	.btn{margin-left: 15px;}
}
.c-sweepstake-wrapper{display: flex; height: 90px; align-items: center;}
.c-sweepstake-heading-left{flex-grow: 1;}
.c-sweepstake-heading-right{flex-grow: 0;}
/*------- /sweepstake -------*/

/*------- auth -------*/
.btn-forgotten{
	text-decoration: none;
	span{display: block; text-decoration: underline;}
	&:hover{text-decoration: none;}
}
.forgotten-password-title{font-size: 35px; line-height: 40px;}
.auth-dashboard-msg{width: 100%; margin-bottom: 20px;}
.page-auth{
	.main-content{order: 1;}
	.sidebar{order: 2; width: 400px;}
	.bc{display: none;}
	.cms-content{max-width: none; margin-left: 0; padding: 30px 150px 0 0;}
}
.auth-wrapper{width: 1060px; margin: auto; display: flex;}
.a-auth-title{text-transform: none; font-size: 42px; line-height: 55px;}
.auth-edit-profile-form{
	display: flex;flex-wrap: wrap;
	.global-error{width: 100%;}
	.field{width: 100%;}
	.field-address, .field-b_address{width: calc(~"100% - 130px");}
	.field-house_number, .field-b_house_number{
		width: 120px; margin-left: 10px;
		input{width: 100%!important;}
	}
}
.field-accept_terms{max-width: 310px; font-weight: bold;}
.btn-signup{width: 220px; margin-top: 15px;}

.page-auth-login, .page-auth-signup, .page-webshop-login{
	.main-wrapper{padding: 30px 0 90px;}
}
.page-auth-login{
	.a-col .btn{width: 200px;}
	.submit{float: left; padding: 0;}
	.auth-links{float: right; width: 45%; padding: 9px 0 0 0; text-align: center;}
	.remember{padding: 10px 0 25px;}
}
.page-auth-signup{
	.auth-links{
		float: right; padding: 8px 0 0; width: 45%; text-align: center;
		a{
			display: block; color: #000;
		}
	}
}

.a-col{flex-grow: 0; flex-shrink: 0; width: 50%;}
.a-col1{border-right: 1px solid @borderColor; padding-right: 100px;}
.a-col2{
	padding-left: 100px;
	ul{
		.list; margin-bottom: 40px;
	}
	.btn{min-width: 220px;}
}
.a-col2 h2, .a-subtitle{font-size: 24px; line-height: 28px; font-weight: bold; padding: 0 0 20px; color: #000;}
.btn-login-signup{min-width: 250px; margin-top: 5px;}
.a-form-title-personal{padding-top: 40px;}
.form-wrapper-other{padding-top: 10px;}
#field-error-accept_terms, #field-error-accept_terms_2{
	position: relative; padding: 8px 0 0 30px; margin-left: 0;
	&:before{.icon-danger-red; font: 21px/21px @fonti; position: absolute; left: 0; top: 6px; color: @red;}
}
.auth-links{
	font-size: 15px; line-height: 20px; text-decoration: none; padding: 10px 0 0;
	a{margin-right: 10px;}
}
.a-intro{
	font-size: 16px; z-index: 10; margin-bottom: 45px; display: flex; position: relative; flex-wrap: wrap;
	ul{
		.list;
	}
}
.a-menu li{font-size: 17px;}
.a-btn-edit{padding-top: 2px;}

.a-intro-title{padding: 0 0 10px 0; font-size: 21px; line-height: 24px; font-weight: bold; position: relative;}
.a-intro-user-title{
	&:before{.icon-user; color: @yellow; font: 22px/22px @fonti; left: 0; top: 0; position: absolute; left: -32px; top: 0;}
}
.a-intro-left{width: 60%; float: left;}
.a-intro-user{float: right; width: 40%;}
.a-section-title{
	font-size: 24px; line-height: 26px; text-transform: uppercase; padding: 0 0 20px;
	a{color: @textColor;}
}
#field-company_oib{width: 100%!important;}
.field-newsletter{margin-top: 6px; margin-bottom: 5px;}
.forgotten-password-wrapper{width: 400px; margin: auto;}
.auth-coupons-empty{font-size: 17px;}
/*------- /auth -------*/

/*------- compare -------*/
.t-compare-attributes, .compare-pager, .c-compare-m-btns{display: none;}
.page-compare{
	.main-wrapper{display: block;}
	.main{margin-top: 85px;}
}
.page-compare-title{
	display: block; padding: 0; font-size: 36px; line-height: 40px; color: @textColor; position: absolute; top: 15px; left: 0; width: 435px; text-align: center; padding: 0 80px;
	&:before{position: absolute; .icon-compare; width: 225px; height: 225px; background: @yellow; border-radius: 400px; font: 115px/225px @fonti; color: @borderColor; left: 50%; margin-left: -113px; top: 180px; color: #fff;}
}
.c-compare-items{
	margin-left: 530px; position: relative; display: flex; z-index: 10; min-height: 480px;
	.cp{width: calc(~"100% / 3");}
}
.cp-compare{
	&:not(.cp-new-compare){height: 822px; background: #fff;}
	.cp-btn-compare{
		background: none; border-color: @borderColor; text-decoration: none;
		&:before{.icon-bin; color: #000;}
		&:hover{
			background: @gray; border-color: @gray;
			&:before{color: @yellow;}
		}
	}
	.cp-badges{top: 60px;}
	.cp-cnt{min-height: 439px;}
	.cp-brand{top: 65px;}
	.cp-attrs{
		li{
			display: block;
			&:first-child{
				padding-left: 11px;
				&:before{display: block;}
			}
		}
	}
	.cp-btn-addtocart{
		span{
			padding: 0;
			&:before{display: none;}
		}
	}
	@media (max-width: @m){
		.cp-extra-price-lowest{max-width: 100%;}
	}
}

.cp-compare-header{
	font-size: 13px; line-height: 17px; border-bottom: 1px solid @borderColor;
	.autocomplete-showall{display: none!important;}
	.autocomplete-container{width: 100%; z-index: 200; top: 50px;}
	.ui-autocomplete {
		font-size: 12px; text-align: left;
		.search-title { font-size: 13px; line-height: 16px; padding-top: 4px; }
		img{opacity: 1;}
	}
	input{border: 0;}
}
.cp-new-compare{
	border: 0;
	.cp-compare-header{border: 1px solid @borderColor;}
}
.cp-compare-desc{padding: 5px 0 0 22px; font-size: 13px;}
.cp-compare-title{font-weight: bold; position: absolute; left: 20px; right: 0; top: -25px; font-size: 14px;}
.cp-compare-form{position: relative;}
.cp-compare-input{
	width: 100%; padding-right: 50px; padding-left: 20px; font-size: 15px; background: none;
	.placeholder(@textColor,@borderColor);
}
.cp-compare-btn{
	width: 42px; height: 42px; background: @yellow; position: absolute; top: 4px; right: 4px; padding: 0;
	&:before{position: absolute; .icon-search; font: 18px/42px @fonti; color: #000; left: 0; top: 0; width: 100%;}
	&:hover{background: darken(@yellow,20%);}
}
.table-cp-attributes{
	font-size: 15px; line-height: 18px; width: calc(~"100% - -1px"); background: #fff; margin-top: 1px;
	.col-attribute-title{display: none;}
	.attr-row{
		display: none;
		&.active{display: table-row;}
	}
	tbody{
		tr:nth-child(2n){
			td{background: #F6F6F6;}
		}
		tr.activeRow1{
			td{background: #fff;}
		}
		tr.activeRow2{
			td{background: #F6F6F6;}
		}
	}
	td{padding: 10px 20px; background: #fff; border-bottom: 1px solid @borderColor; border-right: 1px solid @borderColor;}
}
.table-c-all-attributes{
	margin: 0; width: 529px; text-align: left; border-left: 1px solid @borderColor; margin-top: -1px; font-size: 17px; font-weight: bold;
	td{padding-left: 20px;}
}
.c-compare-sidebar-attributes{
	position: relative; padding-top: 1px;
	&:before{.pseudo(auto,80px); background: @gray; left: -800px; right: -800px; top: -80px;}
}
.c-compare-btns-cnt{font-size: 0; position: absolute; top: -54px; left: 0; display: flex; width: 420px;}
.c-compare-btn{
	text-decoration: none; width: 50%; display: flex; justify-content: center; align-items: center; height: 54px; background: #000; color: @yellow; font-size: 19px; font-weight: bold;
	&.active{
		background: #fff; color: @textColor; height: 56px;
		&:hover{color: @textColor;}
	}
	&:hover{text-decoration: none; color: #fff;}
}
/*------- /compare -------*/

/*------- checkout -------*/
.page-checkout-step1{
	.a-subtitle{padding-bottom: 10px;}
	.field-remember{margin-top: 10px; padding-bottom: 15px;}
}
.wc-step-col1 .wc-col-cnt{padding-top: 10px;}
.wc-step-col2 .wc-col-cnt{padding-top: 20px;}
.btn-wc-guest{min-width: 200px;}
.wc-auth-links{
	float: right; width: 45%; padding: 5px 0 0; text-align: center;
	a{margin: 0; text-decoration: none;}
	span{display: block; text-decoration: underline;}
}
.wc-step1-submit{padding-top: 5px;}
/*------- /checkout -------*/

/*------- thank you -------*/
.wt-title{font-size: 45px; line-height: 50px;}
.thank-you-safe {font-size:12px; line-height:20px; padding:30px 0 0 0;}
.invoice-container {
	padding: 15px 25px 10px; margin:25px 0 0 0; font-size:18px; line-height:32px; border: 1px solid @borderColor; border-radius: 5px;
}
.thank-you-wrapper { margin-top: 35px; }
.thank-you-content { padding: 0 0 20px 0; max-width: 470px; }
.field-show-password {
	margin: 5px 0 0 0;
	label{text-align: left; width: 100%;}
}
.thank-you-login {
	max-width: 400px;
	button { width:auto!important; }
}
#thankyou_signup_success {font-weight: bold; font-size: 16px; line-height: 25px;}
.btn-print {
	&:before {/*.icon-print;*/ font: 20px/20px @fonti; margin: 0px 10px 0 0; position: relative; top:3px;}
}
.btn-w-print{margin-top: 20px;}
.payment-transfer-container{width: 100%; overflow: auto;}
/*------- /thank you -------*/

/*------- orders -------*/
.page-orders .main{margin-bottom: 80px;}
.orders { border-bottom: 1px solid @borderColor; }
.auth-box{padding-bottom: 100px;}
.auth-box-coupons{padding: 0;}
.order-row {
	position: relative; font-size: 17px; border-top: 1px solid @borderColor;
	&.active .order-details { display: block; }
}
.table-header{font-size: 14px; font-weight: bold; line-height: 15px; text-transform: uppercase; background: @gray; height: 40px; display: flex; align-items: center; color: #fff;}
.table-order{
	display: flex; width: 100%;
	.table-col {vertical-align: top;}
}
.table-col{flex-grow: 0; flex-shrink: 0; padding: 10px 0;}
.col-order-terms {
	width: 60px; text-align: right;
	.btn-download-link {
		width: 18px; margin: auto; font-size: 0; vertical-align: top;
		span {
			padding-left: 20px;
			&:before { text-indent: 0; font-size: 20px; }
		}
		.icon{margin-bottom: 0;}
	}
}
.btn-terms-download{
	display: inline-block; vertical-align: top; margin: 3px 0 0; font-size: 0; position: relative; text-decoration: none; width: 20px; height: 20px;
	&:before{.icon-pdf; font: 20px/20px @fonti; color: @textColor;}
	&:hover{text-decoration: none;}
}
.col-order-num{width: 160px; padding-left: 20px; font-weight: bold;}
.col-order-date{width: 140px;}
.col-order-total{
	flex-grow: 1;
	ins{color: @borderColor;}
}
.col-order-status{width: 145px;}
.col-order-tracking{width: 170px;}
.col-order-btns {text-align: right; width: 150px;}
.btn-order-details {
	text-decoration: none; position: relative; font-size: 14px; cursor: pointer; text-transform: uppercase; font-weight: bold; .transition(all);
	&:hover{text-decoration: none; color: @textColor;}
	.btn-active { display: none; }
	&>span{padding-right: 22px; position: relative;}
	.toggle-icon{position: absolute; right: 0; top: 1px;}
	&.active{
		.btn-inactive{display: none;}
		.btn-active{display: inline;}
		.toggle-icon:before{display: none;}
	}
}
.order-details{display: none; padding-right: 180px;}
.show-all-orders {
	text-align: right; text-transform: uppercase; font-size: 10px; padding: 13px 0 0; font-weight: bold;
	a { text-decoration: none; }
}
.wp-details{border: 0; padding: 0 0 20px 0;}
.w-table-details{border: 0;}
.wp-sum{
	text-align: right; font-size: 20px; font-size: 26px;
	span{display: inline-block; min-width: 300px; vertical-align: top; border-top: 1px solid @borderColor; padding: 15px 0 15px 10px;}
}
.wp-details-attrs{padding: 0;}
.wp-total{justify-content: flex-end;}
.wp-attr-energy{
	display: block; margin: -8px 0 0 10px;
	@media (max-width: @m){font-size: 12px; display: inline-block; margin: 5px 0 0;}
	img{
		display: block; margin: 0 auto 7px;
		@media (max-width: @m){height: 35px; width: auto; margin: 0 auto 4px;}
	}
}
/*------- /orders -------*/

/*------- share -------*/
.share{
	margin: 40px 0 0; font-size: 0; display: flex; flex-wrap: wrap;
	.item{
		background: @gray; width: 36px; height: 36px; cursor: pointer; margin-right: 1px; display: flex; justify-content: center; align-items: center; .transition(all);
		&:hover{background: @yellow;}
		&:after{.icon-facebook; font: 16px/17px @fonti; color: #fff;}
	}
	.ss_whatsapp:after{.icon-whatsapp; font-size: 17px; margin-top: -1px;}
	.ss_viber:after{.icon-viber;}
	.ss_email{
		padding-top: 2px;
		&:after{.icon-envelope; font-size: 13px; line-height: 18px;}
	}
}
.share-label{width: 100%; text-transform: uppercase; font-size: 14px; line-height: 18px; font-weight: bold; padding: 0 0 4px;}
.cd-share{margin: 25px 0 0;}
/*------- /share -------*/

/*------- tell friend -------*/
.tellfriend{
	padding: 20px 30px;
	h1{padding: 0 0 20px 0; font-size: 20px; line-height: 28px; color: #000;}
	label, input[type=text]{display: block; width: 100%;}
	label{padding: 0 0 4px 0;}
	.error{top: 0;}
}
/*------- /tell friend -------*/

/*------- sidebar -------*/
.sidebar-container{background: @gray; color: #fff; padding: 50px 50px 40px; margin-bottom: 1px;}
.sidebar-cols{display: flex; color: #fff;}
.sidebar-col{
	width: 50%; background: @gray; text-align: center; font-weight: 300;
	a{
		color: #fff; text-decoration: none; padding: 15px; .transition(all);
		&:hover{background: darken(@gray,20%);}
	}
}

.sidebar-flyer{
	font-size: 17px; line-height: 21px;
	strong{color: @yellow;}
	p{padding: 0;}
	span{display: block; font-size: 13px; line-height: 19px; text-transform: uppercase;}
	img{display: block; margin: 5px auto 0; box-shadow: 0 2px 30px 0 rgba(0,0,0,0.25); max-height: 115px; width: auto;}
	a{display: block; width: 100%; color: #fff; text-decoration: none; }
}
.sidebar-advisor{
	border-left: 1px solid #fff; font-size: 15px; line-height: 20px;
	a{display: block; display: block; width: 100%; height: 100%; color: #fff;}
	img{display: block; margin: 10px auto 15px;}
	strong{display: block; color: @yellow; font-size: 18px;}
}
.c-sidebar-advisor{
	font-size: 18px; position: relative; font-weight: 300;
	strong{font-size: 19px;}
	.btn{height: 40px; position: absolute; top: 25px; right: 20px; font-size: 18px; padding-top: 2px;}
	a{
		text-decoration: none; padding: 25px 0 0 82px; min-height: 90px; background: @gray; .transition(all);
		&:hover{background: darken(@gray,20%);}
	}
	img{position: absolute; left: 23px; top: 9px; max-width: 43px; height: auto;}
}

.sidebar-help{
	padding: 35px 40px 32px; background: @yellow; line-height: 24px; margin-top: 1px;
	p:not(.sidebar-help-footer){padding: 0;}
	a{text-decoration: none;}
	p.sidebar-help-title{padding: 0 0 5px;}
}
.sidebar-help-title{color: #000; font-weight: bold; font-size: 22px; line-height: 28px;}
.sidebar-help-footer{font-size: 14px; line-height: 22px; padding: 4px 0 0 0;}
/*------- /sidebar -------*/

/*------- breadcrumbs -------*/
.bc{
	padding: 0 0 10px; font-size: 13px; color: #A8A8A8; line-height: 18px;
	a{
		text-decoration: none; padding: 0 10px 0 0; margin: 0 3px 0 0; position: relative;
		&:after{.icon-arrow-right; font: 7px/8px @fonti; position: absolute; right: 0; top: 4px; color: @yellow;}
		&:hover{text-decoration: underline;}
	}
}
/*------- /breadcrumbs -------*/

/*------- newsletter -------*/
.nw{
	text-align: center; background: @gray; min-height: 400px; font-size: 20px; display: flex; overflow: hidden; position: relative; font-weight: 300; color: #fff; text-align: left; content-visibility: auto; contain-intrinsic-size: 410px;
	//&:before{.pseudo(350px,350px); background: @yellow; border-radius: 100%; left: -175px; top: -175px;}
	//&:after{.icon-news; font: 54px/54px @fonti; position: absolute; top: 40px; left: 40px; color: @gray;}
}
.nw-title{
	font-size: 50px; line-height: 56px; padding: 0 0 10px;
	strong{color: @yellow;}
}
.nw-col{width: 50%;}
.nw-col1{
	padding: 60px 0 50px; position: relative; overflow: hidden;
	&:after{.pseudo(20px,20px); background: #dce0e3; .rotate(45deg); right: -10px; top: 84px;}
}
.nw-col1-cnt{width: 700px; margin: 0 0 0 auto;}
.nw-col2{
	background: url(images/nl.jpg) no-repeat left top; background-size: cover; width: 50%;
	p{padding: 0;}
	img{width: 100%; height: auto; display: block;}
	&:before{.pseudo(100px, 100px); background: url(images/nl_icon.svg) no-repeat; background-size: contain; top: 52px; right: 54px;}
}
.nw-form-cnt{
	background: #fff; height: 65px; width: 495px; position: relative;
	//&:before{.icon-mail; font: 16px/16px @fonti; color: @yellow; position: absolute; left: 21px; top: 23px;}
}
.nw-subtitle{padding-bottom: 20px; font-weight: normal; max-width: 510px;}
.nw-input{border: none; width: 100%; background: transparent; height: 65px; padding-right: 165px; font-size: 18px; font-weight: normal; padding-left: 25px; .placeholder(@textColor, @borderColor); color: @textColor;}
.nw-button{position: absolute; top: 0px; right: 0px; box-shadow: none; width: 118px; height: 65px; display: flex; align-items: 	center; justify-content: center; color: @textColor; padding: 0; background: @yellow;
	&:hover{background: darken(@yellow,10%); color: @textColor;}
}
.nw-checkbox{
	margin-top: 16px; color: white; width: 480px;
	input[type=checkbox]+label{font-size: 14px; line-height: 18px; padding-top: 4px; padding-bottom: 0; padding-left: 32px;}
	input[type=checkbox]+label:before{background: white; color: white; width: 22px; height: 22px; top: 1px; font: 12px/22px @fonti;}
	input[type=checkbox]:checked + label:before{background: white; border: unset !important; color: @textColor;}
	a{
		color: white; text-decoration: underline; text-decoration-color: @yellow; text-underline-offset: 4px;
		@media screen and (min-width: 1320px) {
			&:hover{text-decoration: none;}
		}
	}
}
.gdpr_accept-error{padding-left: 32px; color: @white; font-size: 13px; padding-top: 0;}
.nw-note{font-size: 14px; padding: 22px 0 0; position: relative; color: #9B9B9B; font-weight: normal;}
.nw-error{text-align: left; padding: 8px 25px 0; font-size: 13px; line-height: 18px;}
.nw-success{font-size: 18px; font-weight: normal; max-width: 450px;}
/*------- /newsletter -------*/

/*------- footer -------*/
.footer{
	font-size: 17px; content-visibility: auto; contain-intrinsic-size: 750px;
	a[href^=tel]{text-decoration: none;}
}
.wrapper-footer{padding: 80px 0;}
.footer-row{display: flex; column-gap: 32px;}
.footer-col{flex-grow: 1; width: calc(~"100%/4");}
.footer-title{
	font-size: 24px; font-weight: bold; color: #4A4A4A; position: relative; padding-bottom: 6px; margin-bottom: 15px;
	&:after{.pseudo(50px,2px); background: @yellow; left: 0; bottom: 0;}
}
.footer-col-cnt{position: relative;}
.footer-row1 .footer-col1{
	a:not(a[href^=tel]){text-decoration-color: @yellow!important; text-decoration: underline; text-underline-offset: 3px;}
}
.nav-footer-col{
	list-style: none; padding: 0; margin: 0; line-height: 22px;
	li{padding: 6px 0;}
	a{
		text-decoration: none; position: relative;
		&:after{.pseudo(100%,1px); left: 0; bottom: 2px; background: @yellow; .scaleX(0); .transition(all); transform-origin: left;}
		&:hover, &.active{
			&:after{.scaleX(1);}
		}
	}
}
.footer-col4{
	ul{.list; margin-left: 10px; margin-bottom: 17px;}
}
.footer-row1 .footer-col4 .footer-col-cnt{position: absolute;}
.social-footer{
	padding-top: 15px;
	a{
		width: 128px; height: 40px; display: flex; padding: 0 16px; text-decoration: none!important; justify-content: center; align-items: center; background: #3B5998; color: #fff; font-size: 16px; line-height: 21px; font-weight: bold; position: relative; .transition(background);
		&:before{.icon-facebook; font: 14px/16px @fonti; color: @white; margin-right: 30px; .transition(color);}
		&:after{.pseudo(1px, 100%); background: rgba(255,255,255,0.2); left: 39px; top: 0;}
	}
}

//ROW2
.footer-row2{
	padding: 34px 0 0; align-items: center;
	p{padding-bottom: 0;}
	.footer-col3{
		display: flex; align-items: center;
		p{margin-right: 7px; padding-bottom: 0;}
	}
	img{display: block;}
	.footer-col4{position: relative;}
}
.cards{
	font-size: 0; position: absolute; top: -10px; width: 345px;
	p{display: flex; align-items: flex-end; gap: 2px;}
	p a:first-child{
		img{max-height: 30px;}
	}
}
.footer-col3-safe-purchase{
	max-width: 314px; display: flex; align-items: center;
	p img{max-width: 92px; width: 100%; max-height: 52px;}
}

//ROW3
.footer-row3{
	padding-top: 46px;
	align-items: center;
	.footer-col1{width: calc(100%/4 - 30px);}
	.footer-col-locations{width: calc(75% - 20px);}
}
.exchange-rate{font-size: 16px;}
.nav-footer{
	list-style: none; padding: 0; margin: 0; display: flex; font-size: 20px; line-height: 25px; font-weight: bold;
	li{
		position: relative; padding: 0 20px 0 0; margin-right: 12px;
		a{
			position: relative; text-decoration: none; color: @white;
			&:after{.pseudo(100%,2px); left: 0; bottom: 2px; background: @yellow; .scaleX(0); .transition(all); transform-origin: left;}
			&:hover{
				&:after{.scaleX(1);}
			}
		}
		&:after{.pseudo(8px,8px); background: @yellow; border-radius: 100px; top: 8px; right: 0;}
		&:last-child{
			margin-right: 0; padding-right: 0;
			&:after{display: none;}
		}
	}
}
.nav-footer-locations{
	font-size: 24px; line-height: 32px;
	li{
		padding-right: 24px; margin-right: 19px;
		&:after{width: 10px; height: 10px; top: 9px;}
		a{color: @gray;}
	}
}

//ROW4
.footer-row4{
	align-items: center; padding: 32px 0 0;
	.footer-col1{width: calc(100%/4 - 30px);}
	.footer-col-dev{width: calc(75% - 20px);}
}
.copy, .dev{
	display: inline-block; vertical-align: top; padding: 0; font-size: 14px;
	a{
		text-decoration: none; color: @textColor;
		&:hover{text-decoration: underline;}
	}
}
.copy{position: relative;}
/* .safe-purchase{
	display: inline-block; vertical-align: top; position: relative; background: url(images/icons/shield.svg) no-repeat left top; background-size: 72px auto; font-size: 20px; line-height: 27px; text-decoration: none; width: 165px; padding: 3px 0 3px 95px;
	&:hover{text-decoration: none;}
} */

//ROW5
.footer-row5{font-size: 14px; line-height: 20px; color: #4A4A4A; opacity: 0.5; padding-top: 54px;}

.free-shipping{
	padding-top: 35px;
	a{
		display: inline-block; vertical-align: top; position: relative; text-decoration: none; padding: 8px 0 10px 80px; font-weight: bold; font-size: 20px; line-height: 25px;
		&:before{.pseudo(60px,60px); background: @yellow; border-radius: 100px; .icon-box; font: 28px/30px @fonti; text-align: center; line-height: 60px; left: 0; top: 0;}
		span{display: block; font-size: 14px; line-height: 18px; font-weight: normal;}
	}
}
.ontop{
	position: fixed; right: 80px; bottom: -500px; width: 60px; height: 60px; background: @yellow; border-radius: 100px; text-decoration: none; text-align: center; display: flex; justify-content: center; align-items: center; text-indent: 1px; box-shadow: 0 15px 30px 0 rgba(0,0,0,0.2); z-index: 100; .transition(all);
	&:before{.icon-arrow-down; font: 10px/10px @fonti; .scaleY(-1); color: @textColor;}
	&:hover{text-decoration: none; background: darken(@yellow,10%);}
	&.active{bottom: 90px;}
}
.admin_toolbar_box{z-index: 9999!important;}
.tally-chat-btn{
	position: fixed; right: 20px; bottom: 15px; width: auto; height: 45px; padding: 15px 25px; font-size: 17px; line-height: 1.5; letter-spacing: 0.6px; color: #63540F; text-transform: initial; font-weight: bold; background: @yellow; border-radius: 100px; text-decoration: none; text-align: center; display: flex; justify-content: center; align-items: center; z-index: 9999999;
	&:hover{text-decoration: none; background: @yellow; color: #63540F;}
	&:before{.icon-question; position: relative; top: -2px; font: 20px/20px @fonti; color: #63540F; margin-right: 8px; font-weight: bold;}
	@media (max-width: 1240px){
		font-size: 0; letter-spacing: unset; padding: 14px;
		&:before{margin-right: 0; top: -1px;}
	}
}
.index-module_layoutDefault__2IbL4{bottom: 15px!important; right: 15px!important;}

/*------- /footer -------*/

/*------- gdpr -------*/
body.gdpr-popup{
	.cookie-warning-wrapper{
		padding-bottom: 0!important;
	}
	.gdpr-popup-content{padding-top: 40px!important;}
}
#gdpr_configurator{
	label{font-size: 18px; line-height: 21px; padding-top: 2px;}
	input[type=checkbox]+label:before{top: 0; border-color: @gray; background: #fff;}
	input[type=checkbox]:checked+label:before{background: @gray!important; border-color: @gray; color: @yellow;}
}
.gdpr-container-wrapper{position: fixed; z-index: 100000000!important; font-size: 20px; line-height: 28px; left: 32px!important; right: auto!important; bottom: 32px!important; width: 730px; background: @white; box-shadow: 0 5px 40px 0 rgba(0, 0, 0, 0.25)!important;}
.gdpr-cookie-btns{
	padding: 0; font-size: 0;
	.btn{
		padding: 18px 40px 17px; font-size: 18px; margin: 0 8px 0 0; height: auto;
		&:last-child{margin-right: 0;}
	}
}
.cookie-warning-wrapper{
	padding: 40px!important;
	.text-decoration-yellow{
		text-decoration: underline; text-decoration-color: @yellow; text-underline-offset: 5px;
		&:hover{text-decoration: none;}
	}
	p:first-child{padding-right: 15px; padding-bottom: 18px;}
}
.gdpr-popup-close{top: 0px!important; right: 0px!important;}
.gdpr-popup-content{
	margin: auto; padding: 0 40px 40px!important; font-size: 16px!important; line-height: 24px!important;
	ul{.list;}
}
.gdpr-popup-cnt{
	padding: 24px!important; background: #F4F4F4!important; border: none!important; max-height: 35vh!important;
	&::-webkit-scrollbar{-webkit-appearance: none; width: 5px; background: @gray; cursor: pointer; }
	&::-webkit-scrollbar-thumb{background-color: @yellow; cursor: pointer;}
}
.gdpr-popup-btn{padding: 0 40px!important; height: 55px!important; font-size: 18px!important; margin-top: 23px!important;}
.gdpr-popup-object-cnt{
	a{
		text-decoration: underline; text-decoration-color: @yellow; text-underline-offset: 3px;
		&:hover{text-decoration: none;}
	}
}
.gdpr-popup-close{
	width: 50px!important; height: 50px!important; background: transparent!important;
	span{
		width: 24px!important; height: 24px!important; top: 17px!important;
		&:before, &:after{background: @red!important;}
		&:before{height: 2.4px!important; top: 11px!important;}
		&:after{width: 2.4px!important; left: 11px!important;}
	}
}
@media (max-width: @t){
	body.gdpr-popup{
		.gdpr-popup-content{padding-top: 30px!important;}
	}
	#gdpr_configurator{
		label{font-size: 16px; line-height: 20px; padding-top: 2px;}
	}
	.gdpr-container-wrapper{font-size: 16px; line-height: 24px; left: 15px!important; bottom: 15px!important; width: 600px;}
	.gdpr-cookie-btns{
		.btn{padding: 15px 33px 14px; font-size: 16px; margin: 0 8px 0 0;}
	}
	.cookie-warning-wrapper{
		padding: 20px!important;
		p:first-child{padding-bottom: 12px;}
	}
	.gdpr-popup-content{padding: 0 20px 20px!important; font-size: 14px!important; line-height: 19px!important;}
	.gdpr-popup-cnt{
		padding: 16px!important; max-height: 30vh!important; margin-top: 0;
		&::-webkit-scrollbar{width: 4px;}
	}
	.gdpr-popup-btn{padding: 0 40px!important; height: 48px!important; font-size: 16px!important; margin-top: 20px!important;}

	.gdpr-popup-close{
		width: 40px!important; height: 40px!important;
		span{
			width: 20px!important; height: 20px!important; top: 10px!important;
			&:before{top: 9px!important;}
			&:after{left: 9px!important;}
		}
	}
}
@media (max-width: 700px){
	body.gdpr-popup{
		.cookie-warning-wrapper{
			text-align: left;
			p:first-child{padding-right: 25px!important;}
		}

		.gdpr-popup-content{padding-top: 15px!important;}
	}

	#gdpr_configurator{
		label{line-height: 19px; padding-top: 2px; padding-left: 28px;}
		input[type=checkbox]+label:before{width: 18px; height: 18px; text-indent: 1px; line-height: 18px;}
	}
	.gdpr-container-wrapper{left: 0!important; right: 0!important; bottom: 0!important; width: auto!important; font-size: 14px!important; line-height: 19px!important;}
	.gdpr-cookie-btns{
		.btn{
			margin: 0!important; margin-bottom: 8px!important; line-height: 20px; text-align: center;
			&:last-child{margin-bottom: 0!important;}
		}
	}
	.cookie-warning-wrapper{
		padding: 15px!important; text-align: center;
		p{padding-right: 0!important;}
		.text-decoration-yellow{text-underline-offset: 4px;}
	}
	.gdpr-popup-content{padding: 0 15px 15px!important;}
	.gdpr-popup-cnt{
		max-height: 15.5vh!important;
		&::-webkit-scrollbar{width: 3px!important;}
		&::-webkit-scrollbar-thumb{width: 3px!important;}
	}
	.gdpr-popup-btn{margin-top: 16px!important;}
	.gdpr-popup-close{
		width: 35px!important; background: @white!important; height: 35px!important;
		span{
			top: 8px!important; left: 8px!important;
			&:before{height: 2px!important;}
			&:after{width: 2px!important;}
		}
	}
}
/*------- /gdpr -------*/

/*------- advent calendar -------*/
.page-calendar{
	.main{margin-bottom: 0;}
	.corner-info{display: none;}
}
.advent-calendar{
	background: #223248; color: white; position: relative; padding: 340px 0 515px;
	p{font-family: 'Inter', sans-serif; font-size: 18px; line-height: 24px; color: rgba(255, 255, 255, .75);}
	&:before{.pseudo(auto,360px); left: 0; right: 0; top: 0; background: url(images/advent-calendar/bg/header-bg.jpg) top left; background-size: contain; background-repeat: no-repeat; background-repeat: repeat-x; pointer-events: none;}
	&:after{.pseudo(auto,485px); left: 0; right: 0; bottom: 0; background: url(images/advent-calendar/bg/footer-bg.jpg) bottom left; background-size: contain; background-repeat: no-repeat; background-repeat: repeat-x; pointer-events: none;}
	.wrapper{max-width: 1480px;}
	.btn-green2{
		text-decoration: none;
		&:hover{text-decoration: none;}
	}
	@media(max-width: @l){
		.wrapper{max-width: 1400px;}
	}
	@media(max-width: 1440px){
		.wrapper{max-width: unset; margin: 0 50px;}
	}
	@media(max-width: 1300px){
		padding: 250px 0 320px;
		&:before{height: 190px; background-size: contain;}
		&:after{height: 300px; background-size: contain;}
		.wrapper{max-width: unset; margin: 0 30px;}
	}
	@media(max-width: @m){
		padding: 95px 0 125px;
		&:before{height: 90px;}
		&:after{height: 110px;}
		.wrapper{margin: 0 10px;}
	}
}
.ac-intro{
	text-align: center; max-width: 860px; margin: 0 auto; position: relative; padding-bottom: 40px;
	h1{font-size: 74px; line-height: 82px; padding-bottom: 38px; color: white;}
	p{max-width: 700px; margin: 0 auto; padding-bottom: 0;}
	@media(max-width: @m){
		h1{font-size: 24px; line-height: 26px; padding-bottom: 18px;}
		p{font-size: 16px; line-height: 24px;}
	}
	@media(max-width: @ms){
		p{max-width: 320px;}
	}
}
.ac-intro-ended{
	@media(max-width: @l){padding-bottom: 0;}
	@media(max-width: @m){
		p{max-width: 280px;}
	}
}
.ac-items{
	display: grid; grid-template-columns: repeat(4, 1fr); grid-template-rows: 1fr; grid-column-gap: 25px; grid-row-gap: 25px; position: relative; z-index: 1; padding: 0 42.5px;
	@media(max-width: @l){grid-column-gap: 20px; grid-row-gap: 20px; padding: 0;}
	@media(max-width: 1300px){grid-template-columns: repeat(3, 1fr); grid-column-gap: 12px; grid-row-gap: 12px; margin-right: 3px;}
	@media(max-width: @ms){grid-template-columns: repeat(2, 1fr); }
}

.advent-item{
	min-height: 330px; background: var(--red); display: block; border-radius: 15px; box-shadow: 0 5px 20px 0 rgba(0,0,0,0.25); position: relative; padding-top: 250px;
	&:before{.pseudo(auto,auto); background: var(--red); left: 5px; top: 5px; bottom: -5px; right: -5px; border-radius: 15px; box-shadow: 0 5px 20px 0 rgba(0,0,0,0.25); z-index: -1;}
	text-decoration: none !important;
	@media(min-width: 1300px){
		&:hover{
			text-decoration: none !important;
			.back-side{opacity: 1; visibility: visible;}
			.aci-icon{opacity: 0; visibility: hidden;}
		}
	}
	@media(max-width: @m){
		min-height: 160px; padding-top: 118px;
		&:before{top: 3px; left: 3px; bottom: -3px; right: -3px;}
	}
}
.advent-item-disabled{
	.back-side{
		padding: 65px 20px; display: block;
		h4{padding-bottom: 22px; font-size: 30px; line-height: 30px;}
		p{padding: 0 40px;}
	}
	background: #5f2f3b;
	&:before{opacity: .35;}
	.aci-number,.aci-icon{opacity: .35;}
	@media(max-width: 1300px){
		&.active{
			.back-side{opacity: 1; visibility: visible;}
			.aci-icon{opacity: 0; visibility: hidden;}
		}
	}
	@media(max-width: @m){
		.back-side{
			padding: 25px 15px;
			h4{font-size: 16px; line-height: 16px; padding-bottom: 8px;}
			p{max-width: 135px; margin: 0 auto; padding: 0; font-size: 12px; line-height: 16px;}
		}
	}
}

.aci-number{
	font-family: 'Grand Hotel', cursive; font-weight: normal; font-size: 64px; line-height: 64px; text-align: center; display: block; color: #fff;
	@media(max-width: @m){font-size: 32px; line-height: 32px;}
}
.back-side{
	visibility: hidden; opacity: 0; transition: visibility .3s, opacity .3s; position: absolute; top: 0; right: 0; left: 0; bottom: 0; display: flex; align-items: center; padding: 87px 40px; flex-flow: column; text-align: center; color: white;
	h4{font-size: 24px; line-height: 32px; text-transform: unset;}
	@media(max-width: @m){display: none;}
}
//icons
.aci-icon{
	display: block; min-height: 170px; max-height: 170px; position: absolute; top: 36px; text-align: center; display: flex; align-items: center; left: 0; right: 0; visibility: visible; opacity: 1; transition: visibility .3s, opacity .3s;
	&:after{.pseudo(100%,170px); position: relative; margin: 0 auto; background: url(images/advent-calendar/1.png) center no-repeat; background-size: contain; pointer-events: none;}
	@media(max-width: @m){
		min-height: 84px; max-height: 84px; top: 18px;
		&:after{height: 84px; background-size: contain !important;}
	}
}
.aci-icon2{
	&:after{background: url(images/advent-calendar/2.png) center no-repeat;}
}
.aci-icon3{
	&:after{background: url(images/advent-calendar/3.png) center no-repeat;}
}
.aci-icon4{
	&:after{background: url(images/advent-calendar/4.png) center no-repeat; margin-left: -10px;}
	@media(max-width: @m){
		&:after{margin-left: -5px;}
	}
}
.aci-icon5{
	&:after{background: url(images/advent-calendar/5.png) center no-repeat;}
}
.aci-icon6{
	&:after{background: url(images/advent-calendar/6.png) center no-repeat;}
}
.aci-icon7{
	&:after{background: url(images/advent-calendar/7.png) center no-repeat;}
}
.aci-icon8{
	&:after{background: url(images/advent-calendar/8.png) center no-repeat;}
}
.aci-icon9{
	&:after{background: url(images/advent-calendar/9.png) center no-repeat;}
}
.aci-icon10{
	&:after{background: url(images/advent-calendar/10.png) center no-repeat;}
}
.aci-icon11{
	&:after{background: url(images/advent-calendar/11.png) center no-repeat;}
}
.aci-icon12{
	&:after{background: url(images/advent-calendar/12.png) center no-repeat;}
}
.aci-icon13{
	&:after{background: url(images/advent-calendar/13.png) center no-repeat;}
}
.aci-icon14{
	&:after{background: url(images/advent-calendar/14.png) center no-repeat;}
}
.aci-icon15{
	&:after{background: url(images/advent-calendar/15.png) center no-repeat;}
}
.aci-icon16{
	&:after{background: url(images/advent-calendar/16.png) center no-repeat;}
}
.aci-icon17{
	&:after{background: url(images/advent-calendar/17.png) center no-repeat;}
}
.aci-icon18{
	&:after{background: url(images/advent-calendar/18.png) center no-repeat;}
}
.aci-icon19{
	&:after{background: url(images/advent-calendar/19.png) center no-repeat;}
}
.aci-icon20{
	&:after{background: url(images/advent-calendar/20.png) center no-repeat;}
}
.aci-icon21{
	&:after{background: url(images/advent-calendar/21.png) center no-repeat;}
}
.aci-icon22{
	&:after{background: url(images/advent-calendar/22.png) center no-repeat;}
}
.aci-icon23{
	&:after{background: url(images/advent-calendar/23.png) center no-repeat;}
}
.aci-icon24{
	&:after{background: url(images/advent-calendar/24.png) center no-repeat;}
}

//colors
.aci2,.aci8,.aci9,.aci15,.aci20,.aci23{
	background: var(--beige);
	&:before{background: var(--beige);}
	.aci-number{color: #3E3D42;}
	&.advent-item-disabled{
		background: #6d7076;
		.aci-number{opacity: .8;}
	}
	&:not(.advent-item-disabled){
		.back-side{
			h4{color: #3E3D42;}
		}
	}
}
.aci3,.aci5,.aci11,.aci13,.aci19,.aci22{
	background: var(--darkGreen);
	&:before{background: var(--darkGreen);}
	&.advent-item-disabled{background: #244659;}
}
.aci4,.aci6,.aci12,.aci14,.aci17,.aci24{
	background: var(--lightGreen);
	&:before{background: var(--lightGreen);}
	&.advent-item-disabled{background: #1b5554;}
}

//promotion end
.btn-green2{
	display: inline-flex; align-items: center; justify-content: center; min-height: 64px; font-size: 18px; line-height: 24px; background: var(--lightGreen); box-shadow: 0 5px 20px 0 rgba(0,0,0,0.25); font-weight: bold; font-family: 'Inter', sans-serif; color: white !important; text-transform: unset; margin-top: 30px; border-radius: 8px; box-shadow: 0 5px 20px 0 rgba(0,0,0,0.25); .transition(background); padding: 0 38px;
	@media(min-width: 1300px){
		&:hover{background: #0e7b58;}
	}
	@media(max-width: @m){min-height: 54px; font-size: 16px; line-height: 24px; padding: 0 34px; margin-top: 28; margin-bottom: 8px;}
}
/*------- /advent calendar -------*/

/*------- advent calendar detail -------*/
.advent-calendar-detail{
	.btn-green2{margin-top: 45px;}
	@media(max-width: @l){
		padding: 340px 0 450px;
		.ac-intro{padding-bottom: 0;}
	}
	@media(max-width: 1300px){padding: 250px 0 320px;}
	@media(max-width: @m){
		padding: 95px 0 125px;
		.btn-green2{margin-top: 28px;}
	}
	.coupon_message{
		margin: 30px 0 -30px 0; display: none !important; font-weight: bold; color: white !important; font-size: 18px; line-height: 28px; background: unset; text-align: center;
		&.coupon_message_response_error{display: block !important; color: @red !important;}
		@media(max-width: 1300px){font-size: 16px; line-height: 24px;}
		@media(max-width: @m){margin: 30px 0 -10px 0;}
		@media(max-width: @ms){max-width: 320px; margin: 30px auto -10px auto;}
	}
}

.calendar-coupons{
	&.active{
		.ww-coupons-input,.calendar-btn-coupon{display: none;}
		.calendar-btn-coupon-active2{
			display: flex; padding-top: 40px;
			@media(max-width: @m){padding-top: 25px;}
		}
	}
	.ww-coupons-title2{
		font-family: 'Inter', sans-serif; color: white; font-weight: bold; display: block; font-size: 18px; line-height: 28px;
		@media(max-width: 1300px){font-size: 16px; line-height: 24px;}
	}
}
.calendar-btn-coupon-homepage{
	margin-top: 20px !important;
	@media(max-width: @m){margin-top: 15px !important;}
}
.calendar-btn-coupon-active{
	padding-top: 40px; display: flex; flex-flow: column; align-items: center;
	@media(max-width: 1300px){
		.ww-coupons-title2{font-size: 16px; line-height: 24px;}
	}
	@media(max-width: @m){padding-top: 25px;}
	@media(max-width: @ms){max-width: 320px; margin: 0 auto;}
}
.calendar-btn-coupon-active2{
	display: none; padding-top: 0; flex-flow: column; align-items: center;
	@media(max-width: 1300px){
		.ww-coupons-title2{font-size: 16px; line-height: 24px;}
	}
	@media(max-width: @ms){max-width: 320px; margin: 0 auto; padding-top: 0;}
}
/*------- /advent calendar detail -------*/


/*------- loading -------*/
.form-loading{position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 999999; color: #fff; font-size: 17px;}
.form-loading span{position: absolute; padding: 60px 20px 20px 20px; width: 200px; left: 50%; margin-left: -100px; top: 40%; text-align: center; box-shadow: 0px 0px 30px #000; color: #000; background: #fff url(images/loader.gif) no-repeat center 20px; background-size: 45px auto;}
.form-loading:before{content: ""; top: 0; bottom: 0; left: 0; right: 0; position: absolute; background: #000; background: rgba(0, 0, 0, .5);}
/*------- /loading -------*/

/*------- old browser info -------*/
.browser-note{
	background: #FC3; border-bottom: 1px solid #F90; padding: 8px 15px; font-size: 14px; font-weight: bold; text-align: center; position: fixed; z-index: 9999; top: 0; left: 0; right: 0;
	a{color: #000; text-decoration: underline;}
	a:hover{color: #000;}
	img{margin: 10px 0;}
}
/*------- /old browser info -------*/

@media screen and (max-width: 1680px) {
	.corner-info{display: none;}

	/*------- 1680 newsletter widget -------*/
	.nw{
		&:before{width: 280px; height: 280px;}
		&:after{top: 17px; left: 17px; font-size: 32px; line-height: 40px;}
	}
	.nw-col1-cnt{width: 580px;}
	.nw-form-cnt{width: 480px;}
	/*------- /1680 newsletter widget -------*/
}

@media screen and (max-width: 1400px) {
	/*------- 1400 helpers -------*/
	.wrapper{margin: 0 30px;}
	/*------- /1400 helpers -------*/

	/*------- 1400 slick -------*/
	.slick-carousel{
		.slick-next{right: -25px;}
		.slick-prev{left: -25px;}
	}
	.slick-arrow{
		width: 50px; height: 50px; top: 25%;
		&:before{line-height: 50px;}
	}
	.slick-carousel .slick-list{width: calc(~"100% - -4px");}
	/*------- /1400 slick -------*/

	/*------- 1400 header -------*/
	.p-ticker{width: auto; margin: 0 60px;}
	.hp-header-label{display: none;}
	.top-contact{right: 110px;}
	.hp-header-col1{width: 950px;}
	.hp-header-col2{flex-grow: 1; flex-shrink: 1;}
	/*------- /1400 header -------*/

	/*------- 1400 navigation -------*/
	.submenu-col2{flex-basis: 610px;}
	/* .nav-submenu{
		font-size: 15px;
		&>li{margin: 0 40px 0 0;}
	} */
	//.nav-categories>li:nth-child(n+6) .submenu-container{width: 100%;}
	//.nav-categories{font-size: 18px;}
	/*------- /1400 navigation -------*/

	/*------- 1400 hero slider -------*/
	.hero-slider-container{width: auto; margin-left: 30px; margin-right: 30px;}
	/*------- /1400 hero slider -------*/

	/*------- 1400 search widget -------*/
	.sw{width: auto; right: 488px;}
	/*------- /1400 search widget -------*/

	/*------- 1400 search -------*/
	.s-header{margin: 30px 0 30px 0;}
	/*------- /1400 search -------*/

	/*------- 1400 main -------*/
	.main-wrapper{width: auto; margin: 0 30px;}
	/*------- /1400 main -------*/

	/*------- 1240 support - stores -------*/
	.panel-support-footer{padding: 48px 25px 0 43px; }
	/*------- /1400 support - stores -------*/

	/*------- 1400 catalog post -------*/
	.cp{width: calc(~"100% / 3 - -1px");}

	.cp-list-attrs{height: 130px;}
	.cp-list-attr-value{font-size: 16px; line-height: 20px;}
	.cp-list-col1{width: 250px;}
	.cp-list-col3{width: 300px;}
	.cp-badges{top: 10px; right: 10px;}
	/*------- /1400 catalog post -------*/

	/*------- 1400 catalog detail -------*/
	.cd-col1{width: 600px;}
	.cd-col2{padding-left: 50px;}
	.cd-col4{padding-left: 70px;}
	/*------- /1400 catalog detail -------*/

	/*------- 1400 cart -------*/
	.w-col1{padding-right: 70px;}
	/*------- /1400 cart -------*/

	/*------- 1400 auth -------*/
	.page-auth .cms-content{padding-right: 75px;}
	/*------- /1400 auth -------*/

	/*------- 1400 hp promo section -------*/
	.section-gray{
		padding: 91px 30px 120px;
		.wrapper{margin: 0;}
	}
	.benefits{margin: 55px 50px;}
	.benefit-item{max-width: 24%;}
	/*------- /1400 hp promo section -------*/

	/*------- 1400 promo -------*/
	.wrapper-promo{margin: 0 6px;}
	/*------- /1400 promo -------*/

	/*------- 1400 newsletter widget -------*/
	.nw-col1-cnt{width: 540px;}
	/*------- /1400 newsletter widget -------*/

	/*------- 1400 footer -------*/
	.footer-row1 .footer-col4 .footer-col-cnt{position: relative;}
	.ontop{
		right: 30px;
		&.active{bottom: 70px;}
	}
	.nav-footer{font-size: 19px;}
	.cards{
		width: auto;
	}
	/*------- /1400 footer -------*/
}
@media screen and (max-width: 1350px) {
	/*------- 1400 header -------*/
	.top-contact{display: none;}
	/*------- /1400 header -------*/
}

@media screen and (max-width: 1240px) {
	/*------- 1240 selectors -------*/
	h1{font-size: 40px; line-height: 44px;}
	h2{font-size: 32px; line-height: 37px;}
	h3{font-size: 28px; line-height: 33px;}
	/*------- /1240 selectors -------*/

	/*------- 1240 slick -------*/
	.slick-carousel{
		.slick-list{width: calc(~"100% - -1px");}
		.slick-prev{left: -20px;}
		.slick-next{right: -20px;}
	}
	.slick-dots{
		li{width: 18px; height: 18px;}
		button{width: 100%; height: 100%;}
		.slick-active button:after{width: 10px; height: 10px; top: 4px; left: 4px;}
	}
	.slick-arrow{
		box-shadow: inset 0 0 0 3px @yellow; width: 40px; height: 40px;
		&:before{line-height: 40px; font-size: 12px;}
	}
	/*------- /1240 slick -------*/

	/*------- 1240 helpers -------*/
	.extra2{font-size: 20px; line-height: 30px;}
	.extra2, .extra3{padding: 25px 30px;}
	.testimonial{
		img{max-width: 200px; height: auto;}
	}
	.testimonial-cnt{
		margin-left: 170px; padding: 40px; min-height: 200px; font-size: 15px; line-height: 21px;
		&:after{width: 260px; height: 260px;}
		&:before{font-size: 25px; line-height: 30px; top: 12px;}
	}
	/*------- /1240 helpers -------*/

	/*------- 1240 header -------*/
	.p-ticker{font-size: 12px; height: 30px; padding-top: 3px;}
	.logo{width: 115px; height: 73px; left: 0;}
	.top-flyer{
		height: 47px; font-size: 12px; margin-right: 10px;
		a{
			padding-top: 10px;
			&:before{width: 22px; height: 30px;}
		}
	}
	.wrapper-header{height: 156px;}
	//.hp-header{padding-bottom: 30px;}
	.nav{
		&>li{margin-right: 15px;}
	}
	/*------- /1240 header -------*/

	/*------- 1240 search widget -------*/
	.sw{
		height: 47px; left: 150px; right: 412px;
		button{width: 80px; height: 47px; font-size: 16px;}
		&:before{font-size: 17px; line-height: 17px; left: 14px; top: 14px;}
	}
	.sw-input{height: 47px; font-size: 15px; padding-right: 90px; padding-left: 45px;}
	.sw-toggle:after{top: calc(50% - 8px); left: calc(50% - 8px);}
	.sw-list-container{top: 46px; padding: 20px 25px 25px; width: 500px;}
	.sw-list{
		a{font-size: 14px; line-height: 18px;}
	}
	.autocomplete-container{top: 45px;}
	.sw-autocomplete-container{top: 46px; width: 740px;}
	.autocomplete-col1{width: 370px;}
	.autocomplete-col-row:not(.autocomplete-col-publish){padding: 20px 25px 25px;}
	.autocomplete-col-publish{padding: 20px 25px 25px;}
	/*------- /1240 search widget -------*/

	/*------- 1240 cart widget -------*/
	.ww{
		margin-left: 10px; width: 150px;
		&.active{
			width: 150px;
			.ww-items{
				padding: 0 10px 0 48px; line-height: 16px;
				&:after{font-size: 18px; line-height: 36px; left: 17px;}
			}
			.ww-counter{font-size: 10px; line-height: 13px; bottom: 6px;}
		}
	}
	.ww-items{
		height: 47px; padding: 0 5px 0 35px;
		&:before{display: none!important;}
		&:after{line-height: 43px; font-size: 18px; left: 10px;}
	}
	.ww-preview{display: none!important;}
	/*------- /1240 cart widget -------*/

	/*------- 1240 compare widget -------*/
	.cw-compare{
		width: 47px; height: 47px;
		a:before{line-height: 47px; font-size: 19px;}
	}
	.cw-compare-counter{font-size: 12px; line-height: 15px; top: 5px;}
	/*------- /1240 compare widget -------*/

	/*------- 1240 wishlist widget -------*/
	.wishlist{
		width: 47px; height: 47px;
		a:before{line-height: 47px; font-size: 17px;}
	}
	.wishlist-counter{font-size: 11px; top: 4px;}
	/*------- /1240 wishlist widget -------*/

	/*------- 1240 navigation -------*/
	.nav-container{left: 150px;}
	.nav-categories{
		height: 50px; font-size: 15px;
		.nav-home{display: none;}
	}
	.nav-sidebar{
		font-size: 15px;
		li{padding: 0 0 6px;}
	}
	.submenu-container{top: 50px; padding: 20px 25px 0; right: 0;}
	.submenu-col2{display: none;}
	/* .nav-categories>li:nth-child(n+6){
		.submenu-col1{padding-left: 0;}
		&.nav-category-grijanje_hladenje .submenu-container{width: 100%;}
	} */

	.nav-submenu{
		font-size: 14px; line-height: 18px;
		&>li{font-size: 16px; line-height: 18px;}
		ul{font-size: 14px; line-height: 18px;}
	}
	.nav-category-smartphone{
		.nav-submenu{
			column-count: 3;
			&>li{
				&:nth-child(2), &:nth-child(3){
					&>ul{break-after: column; padding-bottom: 0;}
				}
				&:nth-child(4){
					&>ul{break-after: unset; padding-bottom: 0;}
				}
			}
		}
	}
	.nav-category-televizori{
		.nav-submenu{
			column-count: 5;
			&>li{
				&:nth-child(2), &:nth-child(4), &:nth-child(5), &:nth-child(6){
					&>ul{break-after: column; padding-bottom: 0;}
				}
				&:nth-child(3), &:nth-child(7){
					&>ul{break-after: unset; padding-bottom: 0;}
				}
			}
		}
	}
	/*------- /1240 navigation -------*/

	/*------- 1240 hero slider -------*/
	/* .hero-slider-container{
		.sidebar-flyer{
			font-size: 15px; line-height: 20px;
			img{max-height: 75px;}
			span{font-size: 12px;}
		}
		.sidebar-advisor{
			font-size: 13px; line-height: 17px;
			a{padding: 10px;}
			img{width: 40px; margin-bottom: 10px; margin-top: 5px;}
			strong{font-size: 17px; line-height: 21px;}
			.btn{font-size: 14px;}
			.btn-small{height: 25px;}
		}
		.slick-dots{
			left: 45px; bottom: 20px;
			li{margin: 0 9px 0 0;}
		}
	} */
	//.hp-header-col1{width: 685px;}
	.hp-header{padding-bottom: 270px;}
	.c-promo{min-height: 416px; max-height: 416px;}
	.wrapper-hp-promo{min-height: 416px;}
	.c-promo-container{max-height: 416px; min-height: 416px; min-width: 440px; max-width: 440px; padding-bottom: 50px; margin-left: 60px; font-size: 18px; line-height: 24px;}
	.c-promo-headline{font-size: 16px; line-height: 20px;}
	.c-promo-title{font-size: 48px; line-height: 65px;}
	.c-promo-btns{margin-top: 15px;}
	.c-promo-btn{width: 180px;}
	.c-promo-center{
		.c-promo-container{min-width: 440px; max-width: 440px;}
	}
	/*------- /1240 hero slider -------*/

	/*------- 1240 popular categories -------*/
	.popular-categories-cnt{margin: -209px auto 0;}
	.pop-hp-categories-title{font-size: 42px; line-height: 42px; padding-bottom: 30px;}
	.cat-item{
		padding: 10px; font-size: 14px; line-height: 18px;
		img{display: block; width: auto; height: auto; max-width: 100%; max-height: 90px;}
	}
	/*------- /1240 popular categories -------*/

	/*------- 1240 hp-promos -------*/
	.hp-promos-cnt{padding-top: 65px; padding-bottom: 125px;}
	.c-pr-cnt{row-gap: 35px;}
	.c-pr-row{column-gap: 20px;}
	.c-pr-content{padding: 16px 15px 0;}
	.c-pr-title{font-size:28px; line-height: 35px;}
	.c-pr-subtitle{font-size: 14px; line-height: 18px;}

	.hp-promos-cnt-dark{padding-top: 0; padding-bottom: 60px;}
	/*------- /1240 hp-promos -------*/

	/*------- 1240 hp-brands -------*/
	.hp-brands{
		position: relative;
		.mwp{
			a{padding: 0 15px;}
			img{max-height: 35px;}
		}
	}
	/*------- /1240 hp-brands -------*/

	/*------- 1240 main -------*/
	.main{margin-top: 30px;}
	.main-wrapper{padding: 0 0 50px;}
	.cms-content{margin-left: 30px;}
	/*------- /1240 main -------*/

	/*------- 1240 breadcrumbs -------*/
	.bc{
		font-size: 12px; line-height: 17px;
		span{display: none;}
		a:last-of-type:after{display: none;}
	}
	/*------- /1240 breadcrumbs -------*/

	/*------- 1240 locations -------*/
	.l-items{max-width: 650px;}
	.lp{font-size: 15px;}
	.lp-col2{padding-top: 10px;}
	.lp-title{font-size: 26px;}
	/*------- /1240 locations -------*/

	/*------- 1240 sidebar -------*/
	.sidebar{width: 260px;}
	.sidebar-container{padding: 30px 30px 20px;}
	.sidebar-help{padding: 26px 30px 28px;}
	.sidebar-flyer{
		font-size: 15px;
		span{font-size: 11px; line-height: 16px;}
		img{max-height: 100px;}
	}
	.sidebar-advisor{
		font-size: 13px; line-height: 19px;
		strong{font-size: 15px;}
	}
	.sidebar-col a{padding: 10px;}
	/*------- /1240 sidebar -------*/

	/*------- 1240 brands widget -------*/
	.mw{
		height: 45px;
		img{max-height: 18px;}
	}
	/*------- /1240 brands widget -------*/

	/*------- 1240 hp promo section -------*/
	.section-gray{
		//padding: 40px 0;
		padding: 90px 0;
		.wrapper{margin: 0 30px;}
	}
	.panel{
		width: calc(~"100% / 3 - 20px"); margin: 0 30px 0 0; padding: 20px 20px 115px; min-height: 445px;
		ul{
			font-size: 13px; line-height: 16px; padding: 0;
			li{padding: 4px 0;}
		}
		&:before{width: 140px; height: 140px; right: -80px; top: -80px;}
		&:after{font-size: 18px; line-height: 18px; top: 14px; right: 14px;}
	}
	.panel-title{font-size: 22px; line-height: 27px; padding: 0 0 10px;}
	.panel-footer{
		height: 45px;
		img{max-width: 85%;}
	}
	.panel-footer-cnt{bottom: 0;}
	
	/*------- /1240 hp promo section -------*/

	/*------- 1240 benefits -------*/
	.benefits{margin: 40px 0;}
	.benefit{
		padding: 6px 0 0 65px; font-size: 14px; line-height: 19px; height: 50px;
		&:before{width: 50px; height: 50px; line-height: 50px; font-size: 17px;}
		span{font-size: 12px;}
	}
	.benefit-item{
		max-width: 25%; margin: 0px 20px 0 0; text-align: left;
		&:last-child{padding-left: 0; margin-right: 0;}
		&:nth-child(2){padding-left: 0;}
	}
	.benefit-payment{padding-top: 0;}
	.benefit-kekspay:before{background-size: 23px 23px!important;}
	/*------- /1240 benefits -------*/

	/*------- 1240 miele -------*/
	.panel-miele{  
		font-size: 14px; line-height: 18px;
		.flex-col{padding: 25px 20px 15px 30px;}
		ul a:before{top: 8px;}
		.icon:before{font-size: 14px;}
		.icon-mail:before{font-size: 12px; top: 4px;}
		.flex-col1{
			line-height: 18px;
			p:first-child{font-size: 18px; }
			a{line-height: 30px; font-size: 14px;}
		}
	}
	.logo-miele{width: 120px; height: 47px; margin-left: -60px;}
	/*------- /1240 miele -------*/

	/*------- 1240 support - stores -------*/
	.locations-col1{margin-right: 30px; width: calc(~"33.333% - 20px");}
	.panel-stores{
		height: 200px;
		.nav-stores{bottom: 13px;}
	}
	.panel-support-header{
		padding: 20px; font-size: 22px; line-height: 28px;
		p{padding: 0;}
	}
	.panel-support-footer{
		padding: 20px 20px 0; font-size: 13px; line-height: 19px;
		h4{font-size: 17px; line-height: 23px;}
		p:last-child{font-size: 12px;}
	}
	.panel-stores-title{
		font-size: 20px; padding: 30px 0 0;
		&:after{width: 80px; height: 80px; margin-left: -40px;}
		&:before{width: 52px; height: 36px; margin-left: -26px; top: 24px;}
		span:last-child{padding-left: 65px;}
	}
	.locations{margin-bottom: 30px;}
	.panel-row{margin-bottom: 30px;}
	/*------- /1240 support - stores -------*/

	/*------- 1240 promo -------*/
	.wrapper-promo{margin: 0 15px;}
	.promo-col{margin: 0 15px 30px; width: calc(~"100% / 3 - 30px");}
	.promo-col-promo-half{width: calc(~"50% - 30px");}
	/*------- /1240 promo -------*/

	/*------- 1240 publish -------*/
	.p-header{padding: 30px 45px 10px;}
	/*------- /1240 publish -------*/

	/*------- 1240 publish widget -------*/
	.pw{
		padding: 50px 0;
		.pp-cnt{padding-top: 15px;}
		.pp-featured{
			.pp-cnt{padding-top: 25px;}
		}
	}
	.pw-title{font-size: 36px; line-height: 47px; padding: 0 0 20px;}
	.p-featured{padding-bottom: 25px; grid-template-columns: calc(66.666% - 10px) calc(100% / 3 - 19.5px); column-gap: 30px; row-gap: 30px;}
	.pw-btns{padding: 35px 0 0;}
	/*------- /1240 publish widget -------*/

	/*------- 1240 publish post -------*/
	.pp{font-size: 15px; line-height: 22px; margin: 0 30px 30px 0; width: calc(~"100% / 3 - 20px");}
	.pp-cnt{
		margin: 0 20px; padding: 15px 0;
		&:before{height: 4px; top: -3px;}
	}
	.pp-title{font-size: 19px; line-height: 24px;}
	.pp-featured{
		margin-right: 30px; width: 100%;
		.pp-cnt{
			margin: 0 45px;
			&:before{height: 6px; top: -3px;}
		}
		.pp-title{font-size: 28px; line-height: 36px;}
	}
	.pp-featured-small{margin-right: 0;}
	/*------- /1240 publish post -------*/

	/*------- 1240 publish detail -------*/
	.pd-header{padding-top: 30px;}
	.pd-content{margin: auto; max-width: 680px;}
	.pd-related-title{font-size: 36px; line-height: 44px;}
	.pd-related{margin-top: 65px; padding: 50px 0 0;}
	.pw-related-btns{padding: 25px 0 65px;}
	/*------- /1240 publish detail -------*/

	/*------- 1240 brands -------*/
	.m-special{margin: 10px 0 0;}
	.m-special-item{
		height: 45px;
		img{max-height: 15px;}
	}
	.m-letter{width: 50px; height: 56px;}
	.m-column{padding-bottom: 30px;}
	.m-letter{font-size: 23px;}
	/*------- /1240 brands -------*/

	/*------- 1240 catalog -------*/
	.c-items{padding-bottom: 50px;}
	.c-sidebar{width: 230px;}
	.c-counter{font-size: 15px;}
	.toolbar, .c-layout{font-size: 15px;}
	.sort{
		margin-left: 25px;
		select{font-size: 15px;}
	}
	.c-sidebar-advisor{
		a{padding: 13px 0 0 60px; min-height: 65px;}
		img{max-width: 30px; top: 5px; left: 15px;}
		.btn{height: 30px; font-size: 14px; right: 15px; padding: 0 12px; top: 17px;}
	}
	.c-desc{
		font-size: 20px; line-height: 28px; max-width: 880px;
		iframe{margin-top: 15px;}
	}
	/*------- /1240 catalog -------*/

	/*------- 1240 catalog widget -------*/
	.cw{padding: 50px 0 60px;}
	.cw-title{font-size: 36px; line-height: 44px; padding: 0 0 25px;}
	.btn-cw-all{margin: 25px 0 0;}
	/*------- /1240 catalog widget -------*/

	/*------- 1240 catalog post -------*/
	.cp-badge{width: 50px; height: 50px; font-size: 15px;}
	.cp-badge-gift:before{font-size: 20px; line-height: 20px;}
	.cp-btn-details span:before{content:"i"; font-family: Georgia; font-weight: bold; font-size: 21px;}
	.cp-title{font-size: 14px;}
	.cp-code{font-size: 12px;}
	.cp-attrs{font-size: 13px;}
	.cp-current-price{font-size: 27px;}
	.cp-header{padding: 0 15px;}
	.cp-save{font-size: 14px;}
	.cp-brand{
		img{max-height: 15px; max-width: 80px; width: auto; height: auto;}
	}
	.cp-image figure{height: 220px;}
	.cp-unavailable{top: 240px;}
	.cp-payment{line-height: 14px;}
	.cp-header{height: 35px;}
	.cp-cnt{padding: 0 15px 15px;}
	.cp-card-discount{margin-right: 3px;}

	.cp-btns{display: flex; position: relative; margin-top: 6px;}
	.btn-cp-add-detail{
		padding: 0 5px;
	}
	.cp-btn-addtocart{
		span{
			padding: 0;
			&:before{display: none;}
		}
	}
	.btn-cp-add-detail{
		&.cp-btn-addtocart{
			padding: 0 5px;
		}
	}

	.cp-list-col1{width: 180px;}
	.cp-list-col3{width: 250px; padding: 15px;}
	.cp-list-col2{padding: 13px 0 15px 15px;}
	.cp-list-title{font-size: 14px; line-height: 17px;}
	.cp-list-code{padding: 0;}
	.cp-list-badge, .cp-list-badge-gift{width: 45px; height: 45px;}
	.cp-list-badge-list{
		img{max-width: 32px; max-height: 32px;}
	}
	.cp-list-badge-gift:before{font-size: 17px;}
	.cp-list-brand{
		left: 15px; top: 10px;
		img{max-height: 12px; width: auto; height: auto;}
	}
	.cp-list-badge-special{font-size: 12px; top: 13px;}
	.cp-list-col3-bottom{display: flex; font-size: 0;}

	.cp-list{
		.cp-list-btn-compare, .cp-list-wishlist-btn{
			width: 40px; height: 40px; font-size: 0; margin: 0;
			&:before{margin: 0; color: #000;}
		}
		.cp-list-btn-compare small{font-size: 0;}
		.cp-list-btn-addtocart{
			font-size: 0; width: 40px; height: 40px; padding: 0;
			span{
				padding: 0; display: block; width: 100%; height: 100%;
				&:before{top: 0; width: 100%; line-height: 40px;}
			}
		}
		.cp-list-btns{order: 1;}
		.cp-list-wishlist{order: 2;}
		.cp-list-compare{order: 3; margin-left: -1px;}
	}
	.cp-list-payment{padding: 12px 5px;}
	.cp-list-cert{
		height: 30px;
		&:after{border-width: 16px 0 14px 15px;}
	}
	.cp-list-attr{
		font-size: 12px; line-height: 15px;
		&:after{top: 15px; bottom: 15px;}
	}
	.cp-list-attr-value{font-size: 12px; line-height: 13px;}
	.cp-list-attr-image{
		margin: 0 0 10px; height: 35px;
		img{max-height: 35px; width: auto;}
	}
	.cp-list-rating{
		.scale(.8); transform-origin: left;
	}
	.cp-list-price{
		display: block;
		.cp-price-regular{text-align: center; padding-top: 15px;}
	}
	.cp-list-code{display: inline-block; vertical-align: top;}
	.cp-list-badge-special{position: relative; top: auto; right: auto; display: inline-block; vertical-align: top; line-height: 18px; padding-left: 10px;}
	.cp-compare{
		.cp-save{font-size: 12px;}
		.cp-brand img{max-width: 65px;}
	}
	.cp-extra-price-lowest{
		strong{display: block;}
	}
	/*------- /1240 catalog post -------*/

	/*------- 1240 catalog detail -------*/
	.cd-col1{width: 470px; margin: 0;}
	.cd-col3{padding: 25px 50px 0 0;}
	.cd-col2, .cd-col4{padding-left: 40px;}
	.cd-row2{
		padding-bottom: 50px;
		&:before{height: 70px;}
	}

	.cd-thumbs-slider{
		&.has-slider{padding: 0 28px;}
		.slick-arrow{
			width: 28px;
			&:before{line-height: 85px;}
		} 
	}
	.cd-thumb{
		span{
			height: 85px;
			img{max-height: 85%; max-width: 85%; width: auto; height: auto;}
		}
	}
	.cd-hero-slide span{height: 580px;}
	.cd-badge-gift span{font-size: 11px; line-height: 17px; width: 120px; left: -37px; top: 56px;}

	.cd-related-color-item{height: 85px;}
	.cd-short-description{font-size: 16px; line-height: 24px; padding: 0 0 20px;}
	.cd-related-color-title{font-size: 15px; line-height: 21px;}
	.cd-related-color-products{padding: 0 0 20px;}
	.cd-title{font-size: 20px; line-height: 24px;}
	.cd-badge-discount{width: 50px; height: 50px; font-size: 16px;}
	.cd-price{margin-right: 15px;}
	.cd-payment-installments-cards p{padding-bottom: 4px;}
	.info-icon{
		display: inline-block; width: 14px; height: 14px; margin: 1px 0 0 5px; background: @gray; border-radius: 100px; text-align: center;
		&:before{content:"i"; font: 11px/11px Georgia; color: #fff;}
	}
	.cd-wishlist{
		.cp-list-wishlist-btn{font-size: 13px; min-width: 160px; padding: 0;}
	}
	.cd-compare{
		.cp-list-btn-compare small{font-size: 13px;}
	}
	.cd-btn-add{font-size: 13px;}
	.cd-btn-compare{padding: 0; min-width: 100px;}
	.cd-btns{padding: 25px 0;}

	.tabs{
		font-size: 13px; height: 45px; font-weight: normal;
		li{min-width: 0;}
		a{padding: 0 10px;}
	}
	.tab-counter{font-size: 11px;}
	.tabs-content{font-size: 15px; line-height: 23px; padding: 25px 0 0;}
	.cd-inquiry-title{
		font-size: 13px; line-height: 19px; padding: 25px 0 15px;
		h2{font-size: 22px; line-height: 29px;}
	}

	.cd-related-products{padding: 0;}
	.cd-related-title{font-size: 28px; line-height: 36px; padding: 0 0 10px;}
	.cd-benefits{position: absolute; right: 0; padding: 21px 0 0; min-height: 70px;}
	.cd-benefit{
		font-size: 13px; line-height: 16px; padding: 0 0 0 38px;
		span{font-size: 12px; line-height: 17px;}
		&:before{font-size: 30px; line-height: 30px;}
		&.benefit-shipping{
			margin-left: 30px; padding-left: 44px;
			&:before{font-size: 26px;}
		}
	}
	.cd-benefits-wrapper{min-height: 100px;}
	.cd-related-slider .slick-arrow{
		width: 40px; height: 40px;
		&:before{line-height: 40px;}
	}
	.cd-related-slider .slick-list{padding-right: 2px;}
	.cd-upsale-slider .slick-list{width: calc(~"100% - -2px");}
	.cd-upsale{padding: 40px 0;}
	.cd-upsale-title{font-size: 32px; line-height: 42px; padding: 0 0 15px;}
	.tab-comments{
		.comment-form-note{padding: 12px 20px 0 0;}
	}
	.cd-attribute{flex-wrap: wrap; font-size: 15px;}
	.cd-attribute-group{
		width: 100%; border-right: 0; padding: 7px 15px;
		&.empty{display: none;}
		&:not(.empty){border-bottom: 1px solid @borderColor;}
	}
	.cd-attribute-title, .cd-attribute-value{width: 50%; text-align: left; padding: 7px 15px;}
	.cd-attribute-brand{
		.cd-attribute-title{display: none;}
		.cd-attribute-value{width: 100%;}
	}
	.cp-list-attr-energetski-razred{
		&.active .attr-tooltip{display: block;}
	}
	.attr-tooltip{width: 400px;}
	.attr-tooltip-close{display: flex; justify-content: center; align-items: center; width: 30px; height: 30px; background: @red; border-radius: 100px; position: absolute; top: -12px; right: -12px;
		&:before{.icon-close; color: #fff; font: 12px/12px @fonti;}
	}
	.cd-documents{
		thead{display: none;}
		table, tbody, tr, td{display: block;}
		tr{
			padding: 15px 0; border-bottom: 1px solid @borderColor;
			&:first-child{padding-top: 0;}
		}
		td{padding: 0; text-align: left;}
		.cd-documents-format{width: 100%; padding: 3px 0; text-transform: none;}
	}
	/*------- /1240 catalog detail -------*/

	/*------- 1240 compare -------*/
	.page-compare .main{margin-top: 60px;}
	.page-compare-title{
		font-size: 30px; line-height: 35px; width: 330px; padding: 0 30px;
		&:before{width: 140px; height: 140px; font-size: 75px; line-height: 145px; margin-left: -70px; top: 110px;}
	}
	.c-compare-items{margin-left: 330px;}
	.cp-compare{
		.cp-cnt{padding: 0 10px 15px;}
		.cp-badge{left: 10px;}
		.cp-badge-gift{right: 10px;}
		&:not(.cp-new-compare){height: 772px;}
	}
	.cp-compare-title{font-size: 12px; left: 10px;}
	.cp-compare-input{font-size: 13px; padding-left: 12px; height: 45px;}
	.cp-compare-btn{
		width: 37px; height: 37px;
		&:before{font-size: 14px; line-height: 37px;}
	}
	.cp-compare-header{
		.autocomplete-container{top: 45px;}
	}
	.table-c-all-attributes{width: 330px;}
	.c-compare-sidebar-attributes:before{height: 64px; top: -64px;}
	.c-compare-btn{
		height: 50px; font-size: 17px;
		&.active{height: 50px;}
	}
	.c-compare-btns-cnt{width: 295px; top: -50px;}
	/*------- /1240 compare -------*/

	/*------- 1240 sweepstake -------*/
	.sws-title{
		font-size: 36px; line-height: 40px; padding: 10px 0 20px 65px; margin: 0;
		&:before{width: 47px; line-height: 55px; top: -4px;}
	}
	.sweepstake-title{font-size: 24px; line-height: 31px;}
	.btn-sweepstake-cancel{font-size: 14px; padding-top: 1px;}
	.c-sweepstake-wrapper{height: 75px;}
	.c-sweepstake-header h1{font-size: 18px;}
	.c-sweepstake-label{
		font-size: 14px; padding: 0;
		&:before{display: none;}
		.btn{font-size: 14px;}
	}
	/*------- /1240 sweepstake -------*/

	/*------- 1240 auth -------*/
	.page-auth-login, .page-auth-signup, .page-webshop-login{
		.main-wrapper{padding: 20px 0 55px;}
	}
	.page-auth{
		.cms-content{padding-top: 10px;}
		.sidebar{width: 260px;}
	}
	.a-col1{padding-right: 45px;}
	.a-col2{
		padding-left: 45px;
		ul{margin-bottom: 15px;}
	}
	.a-col2 h2, .a-subtitle{font-size: 20px; line-height: 23px;}
	.a-col2 .a-have-account{padding-bottom: 13px; padding-top: 15px;}
	.forgotten-password-title{font-size: 28px; line-height: 30px;}
	.a-section-title{font-size: 22px; line-height: 26px;}
	.a-auth-title{font-size: 36px; line-height: 46px;}
	.a-intro-left{width: 68%;}
	.a-intro-user{width: 30%;}
	.a-intro-title{font-size: 17px;}
	.auth-box{padding-bottom: 80px;}
	.a-intro ul{margin-left: 0!important;}
	/*------- /1240 auth -------*/

	/*------- 1240 coupon widget -------*/
	.ww-coupons-label{font-size: 15px;}
	.ww-coupons-add{width: 290px;}
	.ww-auth-coupons-table{
		td{font-size: 14px;}
		.col-code{width: 140px;}
		.col-valid{width: 150px;}
		.col-value{width: 120px;}
	}
	.auth-box-coupons{padding: 0;}
	/*------- /1240 coupon widget -------*/

	/*------- 1240 orders -------*/
	.order-row{font-size: 14px;}
	.col-order-num{width: 140px;}
	.col-order-date{width: 100px;}
	.col-order-status, .col-order-tracking{width: 115px;}
	.col-order-btns{width: 150px;}
	.order-details{padding-right: 150px;}
	.wp-sum{font-size: 22px;}
	/*------- /1240 orders -------*/

	/*------- 1240 add to cart modal -------*/
	/* .modal-box{
		padding: 30px 40px; width: 670px; top: 80px;
		.message{
			font-size: 19px; padding: 6px 0 20px 50px;
			&:before{width: 35px; height: 35px; line-height: 35px; font-size: 14px;}
			&.product_message_response_error:before{font-size: 12px;}
		}
		.close-button{
			width: 40px; height: 40px;
			&:before{line-height: 40px; font-size: 12px;}
		}
	}
	.modal-price{font-size: 23px; line-height: 24px;}
	.modal-title{font-size: 17px;} */
	/*------- /1240 add to cart modal -------*/

	/*------- 1240 cart -------*/
	.w-col1{padding-right: 35px;}
	.w-col2{width: 300px;}
	.w-totals{padding: 25px; font-size: 13px; line-height: 22px;}
	.cart-total{font-size: 18px;}
	.w-title{padding-top: 10px; padding-bottom: 15px;}
	.payment-note{
		font-size: 13px; line-height: 19px;
		ul{
			margin-left: 0;
			li{
				padding-left: 20px;
				&:before{top: 7px;}
			}
		}
	}
	.w-totals-title{padding: 0 0 5px;}
	.w-counter{font-size: 22px;}
	/*------- /1240 cart -------*/

	/*------- 1240 filter -------*/
	.cf-title{font-size: 16px;}
	.nav-cf-categories{font-size: 15px;}
	.cf-active{font-size: 14px;}
	.btn-cf-active-clear{font-size: 14px;}
	/*------- /1240 filter -------*/

	/*------- 1240 brands -------*/
	.cm-logo{
		margin: 20px 0;
		img{max-height: 40px; width: auto;}
	}
	/*------- /1240 brands -------*/

	/*------- 1240 newsletter widget -------*/
	.nw{
		min-height: 210px; font-size: 13px; line-height: 18px;
		&:before{width: 150px; height: 150px; top: -85px; left: -85px;}
		&:after{font-size: 25px; line-height: 25px; left: 11px; top: 13px;}
	}
	.nw-title{font-size: 26px; line-height: 35px; padding-bottom: 5px;}
	.nw-form-cnt{height: 47px;}
	.nw-input{height: 47px; font-size: 15px; padding-left: 15px; padding-right: 105px;}
	.nw-button{height: 47px; width: 90px; font-size: 14px; padding-top: 2px;}
	.nw-form-cnt{
		width: 100%;
		&:before{font-size: 13px; line-height: 13px; top: 17px; left: 15px;}
	}
	.nw-note{font-size: 13px; padding: 20px 0 0;}
	.nw-col1{
		padding: 18px 50px 20px 80px; display: flex; align-items: center;
		&:after{width: 15px; height: 15px; top: 30px;}
	}
	.nw-col2:before{top: 20px; right: 30px; width: 80px; height: 80px;}
	.nw-error{padding-left: 15px;}
	.nw-col1-cnt{width: auto; margin: 0;}
	.nw-subtitle{padding-bottom: 15px;}
	.nw-success{font-size: 15px;}

	.nw-checkbox{
		width: auto;
		input[type=checkbox]+label{font-size: 12px; line-height: 15px; padding-top: 4px; padding-bottom: 0; padding-left: 28px;}
		input[type=checkbox]+label:before{width: 20px; height: 20px; border: none; top: 1px; font: 10px/20px @fonti;}
	}
	.gdpr_accept-error{padding-left: 28px; text-align: left; padding-top: 2px;}
	/*------- /1240 newsletter widget -------*/

	/*------- 1240 wishlist -------*/
	.wishlist-header{margin: 50px 0 25px 0px;}
	.btn-wishslit-delete{top: -80px;}
	.wishlists-title{
		padding: 0 0 0 40px;
		&:before{font-size: 24px; top: 6px;}
	}
	.wishlist-items .cp{width: calc(~"100% / 4 - -1px");}
	/*------- /1240 wishlist -------*/

	/*------- 1240 footer -------*/
	.footer{font-size: 15px;}
	.footer-title{font-size: 17px; line-height: 23px; padding-bottom: 2px; margin-bottom: 10px;}
	.wrapper-footer{padding: 40px 0 35px;}
	.footer-row{column-gap: 20px;}
	.footer-row1 .footer-col4, .footer-row3 .footer-col2{flex-shrink: 1; flex-grow: 1; flex-basis: auto;}
	/* .footer-col2, .footer-col3{padding-right: 25px;} */
	.nav-footer-col{
		line-height: 19px;
		li{padding: 3px 0;}
	}
	.footer-col4{
		padding-right: 0; width: 260px;
		ul{
			line-height: 19px;
			li:before{top: 8px;}
		}
	}
	.footer-row2{padding: 15px 0 0;}

	.footer-row3{
		padding-top: 15px;
		.footer-col1{width: calc(100%/4 - 40px);}
		.footer-col-locations{width: calc(75% - 0px);}
	}

	//ROW4
	.footer-row4{
		padding: 15px 0 0;
		.footer-col1{width: calc(100%/4 - 40px);}
		.footer-col-dev{width: calc(75% - 0px);}
	}
	.nav-footer-locations{
		li:after{width: 8px; height: 8px; top: 11px;}
	}

	//ROW5
	.footer-row5{font-size: 12px; line-height: 20px; padding-top: 30px;}

	.exchange-rate{font-size: 13px;}
	.nav-footer{
		font-size: 15px;
		li{padding: 0 16px 0 0; margin-right: 11px;}
	}
	.copy, .dev{font-size: 12px;}
	.free-shipping a{
		font-size: 15px; line-height: 19px; padding: 5px 0 10px 55px;
		span{font-size: 12px; line-height: 15px;}
		&:before{width: 42px; height: 42px; line-height: 42px; font-size: 20px;}
	}
	.safe-purchase{font-size: 15px; line-height: 19px; background-size: 50px auto; width: 125px; padding-left: 65px;}
	.ontop{
		width: 40px; height: 40px;
		&:before{font-size: 7px; line-height: 9px; margin-top: -3px;}
		&.active{bottom: 70px;}
	}
	.social-footer{
		a{padding: 0 15px; margin-right: 5px;}
	}
	.free-shipping{padding-top: 5px;}
	/*------- /1240 footer -------*/
}

@media screen and (max-width: 990px) {
	/*------- 990 selectors -------*/
	body{font-size: 15px; line-height: 23px;}
	h1{font-size: 36px; line-height: 40px;}
	h2{font-size: 27px; line-height: 32px;}
	h3{font-size: 23px; line-height: 28px;}
	/*------- /990 selectors -------*/

	/*------- 990 forms -------*/
	input, select, textarea{padding: 0 15px;}
	textarea{padding-top: 10px; padding-bottom: 10px;}
	select{background-position: right 15px center;}
	/*------- /990 forms -------*/

	/*------- 990 helpers -------*/
	.extra{font-size: 19px; line-height: 27px;}
	.extra2, .extra3{padding: 20px 25px; font-size: 18px; line-height: 25px;}
	.testimonial{
		img{width: 160px;}
	}
	.testimonial-cnt{
		margin-left: 125px; padding: 20px 45px 20px 20px; min-height: 150px;
		&:after{width: 200px; height: 200px; top: -150px; right: -130px;}
		&:before{font-size: 20px; line-height: 30px; top: 3px; right: 9px;}
	}
	.testimonial-name{font-size: 15px;}
	.testimonial-title{font-size: 12px;}
	.wrapper{margin: 0 20px;}
	/*------- /990 helpers -------*/

	/*------- 990 buttons -------*/
	.btn, button, input[type=submit]{font-size: 16px;}
	/*------- /990 buttons -------*/

	/*------- 990 tables -------*/
	.table-wrapper { overflow-x: scroll; -webkit-overflow-scrolling:touch; }
	::-webkit-scrollbar {-webkit-appearance: none;width: 4px;}
	::-webkit-scrollbar-thumb {border-radius: 4px;background-color: rgba(0,0,0,.5); box-shadow: 0 0 1px rgba(255,255,255,.5); -webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);}
	::-webkit-scrollbar-track {border: 1px solid #eaeaea; }
	/*------- /990 tables -------*/

	/*------- 990 header -------*/
	.p-ticker{margin: 0 20px;}
	.ticker-items{
		li{
			padding: 0 16px 0 0;
			&:after{width: 6px; height: 6px; top: 9px;}
		}
	}
	.social-top{display: none;}
	.logo{width: 100px; height: 65px; top: 15px;}
	.top-flyer{
		height: 40px; margin-right: 7px;
		a{
			padding: 5px 13px 0 35px;
			&:before{width: 18px; line-height: 25px; left: 9px; top: 7px;}
		}
	}
	.top-container{top: 39px;}
	.wrapper-header{height: 131px;}
	.nav{padding-top: 5px;}
	/*------- /990 header -------*/

	/*------- 990 auth widget -------*/
	.aw{top: 6px;}
	.aw-btn{
		height: 26px; font-size: 12px; line-height: 26px;
		span{
			padding-left: 30px;
			&:before{top: 4px; left: 8px; font-size: 16px; line-height: 16px;}
		}
	}
	.aw-login{
		&:before{width: 24px; height: 24px; font-size: 12px; line-height: 25px;}
	}
	/*------- /990 auth widget -------*/

	/*------- 990 search widget -------*/
	.sw{
		height: 40px; left: 120px; width: auto; right: 333px; top: 39px;
		&:before{font-size: 14px; line-height: 17px; top: 11px;}
		button{height: 40px; width: 60px; font-size: 14px;}
	}
	.sw-input{height: 39px; font-size: 13px; padding-left: 40px; padding-right: 65px;}
	.sw-list-container{top: 39px; width: 360px; padding: 14px 16px 16px;}
	.sw-list{
		column-count: 2;
		li{display: block; padding: 0 0 5px;}
		a{font-size: 14px; line-height: 22px;}
	}
	/*------- /990 search widget -------*/

	/*------- 990 autocomplete -------*/
	.ui-autocomplete{font-size: 13px;}
	.ui-autocomplete .search-title{padding: 0;}
	.autocomplete-container{top: 38px;}
	.sw-autocomplete-container{top: 39px; width: 665px; left: -121px;}
	.autocomplete-col1{width: 340px;}
	/* .autocomplete-col-row:not(.autocomplete-col-publish){padding: 20px 25px 25px;}
	.autocomplete-col-publish{padding: 20px 25px 25px;} */
	.sw-toggle{
		left: 11px;
		&:after{width: 12px; height: 12px; font-size: 12px; line-height: 12px; top: calc(50% - 6px); left: calc(50% - 6px);}
	}
	.catalogproduct{
		.ui-menu-item{
			a{padding: 12px 16px;}
		}
		.autocomplete-showall{
			padding: 12px 15px 32px;
			a{padding: 14px 13px; font-size: 16px; line-height: 17px;}
		}
	}
	.autocomplete-col-row:not(.autocomplete-col-publish){padding: 24px 15px 24px;}
	.autocomplete-col-publish{padding: 24px 15px 25px;}
	/*------- /990 autocomplete -------*/

	/*------- 990 cart widget -------*/
	.ww{margin-left: 7px; width: 40px;}
	.ww-items{
		height: 40px;
		&:after{line-height: 40px; text-indent: 2px;}
	}
	.ww-items{
		//width: 40px;
		.cart-empty{display: none;}
		&:after{font-size: 17px; text-indent: 0;}
	}
	.ww.active{
		width: 120px;
		.ww-items{
			font-size: 12px; line-height: 13px; padding: 0 10px 0 45px;
			&:after{font-size: 15px; line-height: 30px; left: 14px;}
		}
		.ww-counter{width: 40px; bottom: 5px; font-size: 9px;}
	}
	.free-delivery-missing{height: 25px;}
	.free-delivery-missing-num{font-size: 12px;}
	.ww-preview-note-label{
		padding: 0 0 0 29px;
		&:before{font-size: 20px; line-height: 20px; top: -3px;}
	}
	/*------- /990 cart widget -------*/

	/*------- 990 compare widget -------*/
	.cw-compare{
		width: 40px; height: 40px;
		a:before{line-height: 39px; font-size: 17px;}
	}
	.cw-compare-counter{font-size: 11px; top: 4px; right: 6px;}
	/*------- /990 compare widget -------*/

	/*------- 990 wishlist widget -------*/
	.wishlist{
		width: 40px; height: 40px;
		a:before{line-height: 40px; font-size: 14px;}
	}
	.wishlist-counter{top: 4px; right: 6px;}
	/*------- /990 wishlist widget -------*/

	/*------- 990 navigation -------*/
	.nav-container{top: 7px;}
	.nav{
		&>li{font-size: 12px; margin: 0 8px 0 0;}
	}
	.nav-categories{font-size: 12px; height: 45px;}
	.nav-container{left: 120px;}

	.submenu-container{top: 45px; padding: 15px 20px 0;}
	.nav-submenu{
		font-size: 13px; line-height: 20px;
		li{padding: 2px 0;}
	}
	/*------- /990 navigation -------*/

	/*------- 990 sidebar -------*/
	.sidebar{width: 210px;}
	.sidebar-container{padding: 20px 20px 15px;}
	.sidebar-flyer{
		font-size: 13px;
		img{max-height: 85px;}
	}
	.sidebar-advisor{
		img{max-width: 40px; height: auto; margin: 8px auto 10px;}
		strong{font-size: 13px;}
	}
	.sidebar-help{padding: 15px 20px 15px; font-size: 14px; line-height: 18px;}
	.sidebar-help-title{font-size: 17px; line-height: 20px;}
	.sidebar-help-footer{font-size: 12px; line-height: 19px;}
	.nav-sidebar{
		a{font-size: 13px;}
	}
	/*------- /990 sidebar -------*/

	/*------- 990 newsletter -------*/
	.nw{font-size: 14px; line-height: 16px; min-height: initial;}
	.nw-col{width: 100%;}
	.nw-col1{
		padding: 0; text-align: center;
		&:after{display: none;}
	}
	.nw-col1-cnt{max-width: 360px; width: 100%; padding: 40px 16px 23px; margin: 0 auto;}
	.nw-col2{display: none;}
	.nw-title{font-size: 26px; line-height: 29px; padding-bottom: 7px;}
	.nw-subtitle{padding-bottom: 12px;}
	.nw-note{font-size: 12px; line-height: 15px;}
	.nw-button{width: 80px;}
	/*------- /990 newsletter -------*/

	/*------- 990 brands widget -------*/
	.mw{height: 35px;}
	/*------- /990 brands widget -------*/

	/*------- 990 hero slider -------*/
	.hero-slider-container{margin-left: 20px; margin-right: 20px; margin-top: 9px;}
	/*.hp-header-col1{width: 530px;}
	.hero-slider-container{
		.sidebar-flyer{
			font-size: 12px; line-height: 16px;
			span{font-size: 10px; line-height: 14px;}
			img{margin-top: 5px;}
			a{padding: 8px 8px 0;}
		}
		.sidebar-advisor{
			font-size: 12px; line-height: 16px;
			strong{font-size: 14px; line-height: 16px;}
			.btn-small{height: 21px; font-size: 13px;}
			a{padding: 8px 8px 0;}
		}
		.slick-dots{left: 35px; bottom: 17px;}
	} */
	.hp-header{padding-bottom: 230px;}
	.c-promo{min-height: 308px; max-height: 308px;}
	.wrapper-hp-promo{min-height: 308px;}
	.c-promo-container{max-height: 308px; min-height: 308px; min-width: 390px; max-width: 390px; padding-bottom: 40px; margin-left: 40px; font-size: 16px; line-height: 22px;}
	.c-promo-headline{line-height: 17px;}
	.c-promo-title{font-size: 32px; line-height: 35px;}
	.c-promo-btn{width: 160px; padding: 2px 15px 0;}
	.c-promo-center{
		.c-promo-container{min-width: 390px; max-width: 390px;}
	}
	/*------- /990 hero slider -------*/

	/*------- 990 popular categories -------*/
	.popular-categories-cnt{margin: -188px auto 0;}
	.pop-hp-categories-title{font-size: 28px; line-height: 32px; padding-bottom: 16px;}
	.cat-item{
		width: calc(~"100%/4"); padding: 16px 16px 12px; line-height: 16px;
		&:nth-child(6n){border-right: 1px solid @borderColor;}
		&:nth-child(-n+6){border-top: 1px solid @borderColor;}
		&:nth-child(4n){border-right: 0;}
		&:nth-child(-n+4){border-top: 0;}
		img{display: block; width: auto; height: auto; max-width: 100%; max-height: 88px;}
	}
	/*------- /990 popular categories -------*/

	/*------- 990 hp-promos -------*/
	.hp-promos-cnt{padding-top: 40px; padding-bottom: 40px;}
	.c-pr-cnt{row-gap: 32px;}
	.c-pr-row{flex-flow: column; column-gap: unset; row-gap: 32px;}
	.c-pr-item-img:after{height: 6px; bottom: -3px; right: 16px; left: 16px;}
	.c-pr-item-two-smaller,.c-pr-item-three{
		.c-pr-item-img:after{right: 16px; left: 16px;}
	}
	.c-pr-title{font-size: 24px; line-height: 26px;}
	.c-pr-subtitle{font-size: 16px; line-height: 20px; padding-top: 2px;}

	.hp-promos-cnt-dark{padding-top: 0;}
	/*------- /990 hp-promos -------*/


	/*------- 990 hp-brands -------*/
	.hp-brands{
		.wrapper{margin: 0;}
		.mw{
			margin-top: 0; flex-wrap: nowrap; justify-content: unset; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; height: 70px; contain-intrinsic-size: 70px; box-shadow: none; position: relative; z-index: 2; overflow-x: auto; overflow-y: hidden; white-space: normal;
			&::-webkit-scrollbar, &::-webkit-scrollbar-thumb, &::-webkit-scrollbar-track{background: transparent; display: none;}
			&::-webkit-scrollbar-track{border: none; display: none;}
		}
		.mwp{
			width: auto; border-top: 0;
			&:nth-child(9){border-right: 1px solid @borderColor;}
			img{max-width: 70px; max-height: 50px;}
		}
	}
	/*------- /990 hp-brands -------*/

	/*------- 990 hp promo section -------*/
	.section-gray{
		padding: 0; margin-top: 0; padding-bottom: 40px; padding-top: 32px;
		.wrapper{margin: 0 20px;}
	}
	.panel{
		width: calc(~"100% / 3 - 8px"); margin: 0 12px 10px 0; padding: 15px 15px 100px;
		&:before{top: -85px; right: -85px;}
		&:after{top: 11px; right: 11px;}
	}
	.panel-row{margin-bottom: 10px;}
	.panel-title{font-size: 18px; line-height: 22px;}
	/*------- /990 hp promo section -------*/

	/*------- 990 benefits -------*/
	.benefits{
		margin: 0 0 32px; height: 56px;
		&.slick-initialized .slick-slide{display: flex!important; justify-content: center!important;}
	}
	.benefit-item{
		margin: 0; width: auto; max-width: 100%; display: none;
	}
	.benefit{
		padding-top: 8px; font-size: 18px; line-height: 20px; width: 250px; height: 56px;
		&:before{width: 56px; height: 56px; line-height: 56px;}
		span{font-size: 14px;}
	}
	.benefit-payment{padding-top: 0;}
	.benefit-payment:before{font-size: 22px;}
	.benefit-flyer:before{font-size: 22px;}
	/*------- /990 benefits -------*/

	/*------- 990 support - stores -------*/
	.locations-col1{margin-right: 12px; width: calc(~"33.333% - 8px");}
	.panel-support-header{padding: 2px 13px; font-size: 21px; line-height: 25px; display: flex; align-items: center;}
	.panel-support-footer{
		padding: 13px 13px 13px; font-size: 12px; line-height: 17px;
		h4{padding: 0 0 2px; font-size: 15px; line-height: 20px;}
	}
	.panel-stores-title{
		font-size: 18px; padding: 22px 0 0; top: 40px;
		&:after{width: 60px; height: 60px; margin-left: -35px;}
		&:before{width: 45px; height: 29px; top: 16px; margin-left: -27px;}
		span:first-child{padding-right: 30px;}
	}
	.panel-stores{
		height: 165px;
		.nav-stores{
			font-size: 12px; bottom: 10px;
			li{
				padding: 0 11px 0 0; margin-right: 7px;
				&:after{width: 5px; height: 5px; top: 11px;}
				&:last-child{margin-right: 0; padding-right: 0;}
			}
		}
	}
	.locations{margin-bottom: 20px;}
	/*------- /990 support - stores -------*/

	/*------- 990 miele -------*/
	.panel-miele{
		font-size: 12px; line-height: 17px;
		.flex-col{padding: 16px 15px 10px 20px;}
		.icon{
			padding-left: 25px;
			&:before{top: 2px;}
		}
		ul a:before{top: 7px;}
		.flex-col1{
			a{
				line-height: 25px; font-size: 14px;
			}
		}
	}
	/*------- /990 miele -------*/

	/*------- 990 slick -------*/
	.slick-carousel{
		.slick-list{width: calc(~"100% - -5px");}
		.slick-prev{left: -15px;}
		.slick-next{right: -15px;}
	}
	.slick-arrow{top: 14%;}
	/*------- /990 slick -------*/

	/*------- 990 contact -------*/
	.contact-col1{padding-right: 50px;}
	.contact-row h2{font-size: 20px; line-height: 25px;}
	.contact-col2{width: 250px;}
	.nav-locations-contact{font-size: 13px;}
	.btn-locations{font-size: 14px; padding: 0;}
	/*------- /990 contact -------*/

	/*------- 990 locations -------*/
	.page-locations .cms-content{max-width: 490px;}
	.lp-title{font-size: 24px; line-height: 32px; padding-bottom: 10px;}
	.lp-images .slick-arrow{top: 37%;}
	.lp{font-size: 13px; line-height: 18px;}
	.lp-col2{padding-top: 0;}
	/*------- /990 locations -------*/

	/*------- 990 faq -------*/
	.fp{padding: 0 0 10px;}
	.fp-title{font-size: 18px; line-height: 25px; padding-top: 1px;}
	.fp-cnt{font-size: 16px; line-height: 23px;}
	/*------- /990 faq -------*/

	/*------- 990 share -------*/
	.share{margin: 30px 0 0;}
	/*------- /990 share -------*/

	/*------- 990 catalog widget -------*/
	.cw{padding: 35px 0 40px;}
	.cw-title{font-size: 30px; line-height: 36px;}
	.cw-title{padding: 0 0 15px;}
	/*------- /990 catalog widget -------*/

	/*------- 990 promo -------*/
	.promo-col{margin: 0 10px 30px; width: calc(~"100% / 3 - 20px");}
	.wrapper-promo{margin: 0 10px;}
	.panel2 .panel-footer-cnt{bottom: 0;}
	//.panel-cols{flex-wrap: wrap;}
	.panel ul{width: 50%; margin-bottom: 10px;}
	/*------- /990 promo -------*/

	/*------- 990 catalog post -------*/
	.cp{width: calc(~"100% / 3 - -1px");}
	.cp-image figure{height: 160px;}
	.cp-header-cnt{padding-top: 35px;}
	.cp-unavailable{top: 170px;}
	.cp-header{padding: 0 10px;}
	.cp-brand{
		img{max-width: 55px;}
	}
	//.cp-btns{bottom: 10px; right: 10px;}
	.cp-save{font-size: 12px;}
	.cp-badge{left: 10px; width: 40px; height: 40px; font-size: 14px;}
	.cp-badge-gift{
		right: 10px; left: auto;
		&:before{font-size: 17px;}
	}
	.cp-cnt{padding: 0 10px 10px;}
	.cp-title{font-size: 12px; line-height: 17px;}
	.cp-current-price{font-size: 22px; line-height: 24px;}
	.cp-price{padding-right: 15px;}
	.cp-price-note{font-size: 10px; padding: 0;}
	.cp-payment{font-size: 10px; line-height: 13px; bottom: 2px; min-width: 0;}
	.cp-price-label{min-height: 17px;}
	.cp-card-prices{flex-wrap: nowrap; flex-flow: column;}
	.cp-card-price{width: 100%;}

	.btn-cp-add-detail{
		text-align: center; font-size: 10px;
	}

	.cp-list-attr{width: auto; flex-grow: 1;}

	.formated-price{line-height: 23px;}
	.formated-price .p-k, .formated-price .p-n{bottom: 2px;}
	/*------- /990 catalog post -------*/

	/*------- 990 publish -------*/
	.p-header{padding: 30px 20px 10px;}
	.p-items-blog{
		.p-featured{padding-bottom: 25px;}
	}
	/*------- /990 publish -------*/

	/*------- 990 publish widget -------*/
	.pw{padding: 35px 0 50px;}
	.pw-title{font-size: 30px; line-height: 35px;}
	.btn-all{min-width: 200px;}
	.p-featured{grid-template-columns: calc(66.666% - 8px) calc(100% / 3 - 17.5px); column-gap: 25px; row-gap: 25px;}
	/*------- /990 publish widget -------*/

	/*------- 990 publish post -------*/
	.pp{font-size: 13px; line-height: 18px; margin: 0 25px 25px 0; width: calc(~"100% / 3 - 17px");}
	.pp-cnt{margin: 0 15px; padding: 12px 0;}
	.pp-title{font-size: 17px; line-height: 20px;}

	.pp-featured{
		margin-right: 25px; width: 100%;
		.pp-cnt{margin: 0 20px;}
		.pp-title{font-size: 24px; line-height: 30px; padding: 0 0 6px;}
	}
	.pp-featured-small{margin-right: 0;}
	/*------- /990 publish post -------*/

	/*------- 990 publish detail -------*/
	.pd-title{font-size: 28px; line-height: 36px;}
	.pd-info>div{font-size: 12px;}
	.pd-header{padding-bottom: 15px;}
	.pd-hero-image{margin-bottom: 25px;}
	.pd-related{padding: 35px 0 0;}
	.pd-content{max-width: none; padding: 0 110px;}
	.pd-related-title{font-size: 36px; line-height: 37px; padding: 0 0 20px;}
	.pw-related-btns{padding: 20px 0 55px;}
	/*------- /990 publish detail -------*/

	/*------- 990 comments -------*/
	.comments-title{font-size: 25px; line-height: 27px;}
	.comment-form-note{padding-top: 14px;}
	/*------- /990 comments -------*/

	/*------- 990 brands -------*/
	.m-special-item{width: 20%;}
	.m-special{justify-content: left;}
	.m-letter{width: 45px; height: 45px; line-height: 45px;}
	.m-column{width: 20%;}
	/*------- /990 brands -------*/

	/*------- 990 promo -------*/
	.promo-col{margin: 0 10px 20px;}
	.promo-col-promo-half{width: calc(~"50% - 20px");}
	/*------- /990 promo -------*/

	/*------- 990 catalog -------*/
	.promo{padding: 0 0 10px;}
	.c-sidebar{display: none;}
	.category-level2 .c-header{text-align: left; padding: 30px 20px 5px;}
	.c-empty{padding: 0 0 30px;}
	/*------- /990 catalog -------*/

	/*------- 990 toolbar -------*/
	.toolbar-col1{display: none;}
	.toolbar{margin: 0 0 20px; padding-top: 20px;}
	.toolbar-filter-qty{padding-right: 15px;}
	.btn-toggle-filter{
		height: 40px; width: 25%; flex-grow: 0; flex-shrink: 0; border: 1px solid @borderColor; text-decoration: none; font-size: 15px; padding: 0 15px; margin-right: 15px; display: flex; align-items: center; color: @textColor; position: relative;
		&:hover{text-decoration: none;}
		&:after{.icon-arrow-down; font: 7px/8px @fonti; position: absolute; right: 15px; top: 15px;}
	}
	.sort{
		width: 25%; flex-shrink: 0; flex-grow: 0; margin: 0 0 0 15px;
		select{width: 100%;}
	}
	.toolbar-filter{margin-top: 10px;}
	.toolbar-col2{width: 100%; display: flex;}
	.c-counter, .c-layout, .toolbar{font-size: 12px;}
	.c-layout{margin: 0; flex-grow: 1; justify-content: flex-end;}
	.c-layout-label{padding-right: 10px; display: none;}
	/*------- /990 toolbar -------*/

	/*------- 990 filter -------*/
	.btn-toggle-filter.wide{width: 100%;}
	.active-filter{
		overflow: hidden; position: fixed;
		.zopim{display: none!important;}
	}
	.c-sidebar.active{
		display: block; position: fixed; width: auto; left: 0; top: 0; right: 0; bottom: 0; z-index: 10000; background: #fff; padding: 0;
		.c-sidebar-advisor{display: none;}
		.cf-sidebar-title{display: block; background: @gray; height: 40px; color: #fff; color: #fff; font-size: 17px; line-height: 40px; text-transform: none; padding: 0 15px;}
	}
	.btn-close-filter{
		position: absolute; top: 0; right: 0; font-size: 0; width: 40px; height: 40px; display: block; color: #fff; text-decoration: none;
		&:before{.icon-close; font: 13px/40px @fonti; position: absolute; left: 0; top: 0; width: 100%; text-align: center; color: #fff;}
	}
	.cf-items-wrapper{display: block; height: calc(~"100vh - 160px"); overflow: auto;}
	.cf-item{
		border: 0; width: 100%; position: relative;
		&.active .cf-item-wrapper{padding-bottom: 15px;}
		&:after{.pseudo(auto,1px); background: @borderColor; left: 0; right: 0;}
	}
	.cf-title{
		padding: 13px 15px 9px;
		.nav-sidebar-arrow{display: block; right: 13px; top: 0;}
	}
	.cf-item-wrapper{max-height: 0; height: auto; padding: 0 15px;}
	.cf-btns{
		display: flex; width: auto; position: fixed; bottom: 15px; left: 15px; right: 15px; justify-content: flex-end;
		.btn{width: 49%; font-size: 15px;}
		.btn span{display: none;}
	}
	.btn-confirm-filters{
		background: #000; color: #fff; text-transform: none; visibility: hidden;
		&:hover{color: #fff;}
		&.active{visibility: visible;}
	}
	.btn-cf-active-clear{width: 100%; height: 35px; margin: 0;}
	.btn-cf-m-active-clear{
		background: none; border: 1px solid @yellow; color: #000; margin-right: 2%; text-transform: none;
		&:hover{color: #000;}
	}
	.cf-active{
		width: 100%; margin: 0; border: 0; display: flex; flex-wrap: wrap; align-items: center; font-size: 13px; margin: 15px 0 20px; justify-content: center;
		&:after{display: none;}
	}
	.cf-active-title{display: none;}
	.cf-active-item{padding: 0 0 0 20px; margin: 5px 8px;}
	.active-filter{
		.tally-chat-btn, .cnj-trust-mark-vertical{display: none!important;}
	}
	/*------- /990 filter -------*/

	/*------- 990 catalog detail -------*/
	.cd-wrapper{padding-top: 25px;}
	.cd-col1{width: 360px;}
	.cd-col2, .cd-col4{padding-left: 20px;}
	.cd-title{font-size: 17px; line-height: 22px; padding: 0 0 5px;}
	.cd-price-container{display: block; .clear;}
	.cd-shipping-price{width: 100%; text-align: left;}
	.cd-payment{text-align: right;}
	.cd-payment-installments{
		left: auto; right: 0;
		&:before{left: auto; right: 17px;}
	}
	.cd-price{text-align: left;}
	.cd-btns{flex-wrap: wrap;}
	.add-to-cart-container{order: 1; width: 100%; margin-bottom: 10px;}
	.cd-wishlist{order: 2; flex-grow: 1;}
	.cd-compare{order: 3; flex-grow: 1;}
	.cd-btn-compare{margin: 0;}
	.cd-short-description{font-size: 15px; line-height: 21px;}
	.cd-row1{padding-bottom: 20px;}
	.cd-row2{
		margin-top: 20px;
		&:before{display: none;}
	}
	.cd-btn-add{font-size: 17px;}
	.cd-related-color-item{height: 70px;}
	.cd-related-size-item{width: 33%; max-width: none; height: 80px;}
	.cd-thumb span{height: 60px;}
	.cd-thumbs-slider .slick-arrow:before{line-height: 63px;}
	.tabs{
		font-size: 13px; font-weight: normal;
		a{padding: 0 15px;}
	}
	.cd-col3{padding: 9px 15px 0 0;}
	.cd-related-slider{
		.slick-arrow{top: 75px;}
		.slick-next{right: -15px;}
		.slick-prev{left: -18px;}
	}
	.cd-related-title{font-size: 26px; line-height: 33px;}
	.cd-benefits{display: none;}
	.cd-benefits-wrapper{min-height: 0;}
	.cd-upsale-title{font-size: 29px; line-height: 36px;}
	.cd-badge-discount{margin-top: -9px;}
	.cd-badge{right: 0;}
	.cd-badge-gift span{top: 46px; width: 75px; left: -20px; line-height: 15px;}
	.cd-attribute{font-size: 13px;}
	.cd-attribute-title{font-weight: normal; padding-right: 15px; padding-left: 15px;}
	.cd-attribute-value{padding-left: 15px;}
	.btn-cd-inquiry{width: 100%;}
	.cd-inquiry-title h2{font-size: 19px; line-height: 25px;}
	.attr-tooltip{width: 310px;}

	.tabs{display: none;}
	.tab{
		display: block;
		&.active{
			.toggle-icon:before{display: none;}
			.tab-content{max-height: 30000px; padding-bottom: 15px;}
			.btn-tab-toggle{background: none; color: @textColor; border-top-color: @borderColor;}
		}
	}
	.tab-content{overflow: hidden; max-height: 0; padding: 0 15px; transition: max-height .3s, padding-bottom .3s;}
	.tabs-content{padding: 0;}
	.tab-specs{
		.tab-content{padding: 0!important;}
	}

	.btn-tab-toggle{
		display: block; background: #000; color: #fff; font-size: 17px; font-weight: bold; padding: 11px 15px; text-decoration: none; position: relative; border-top: 1px solid #464852; transition: color .3s, background .3s, border-color .3s;
		&:hover{text-decoration: none; color: #fff;}
		.toggle-icon{position: absolute; right: 15px; top: 15px;}
	}
	.cd-documents{margin: 0 15px 40px;}
	/*------- /990 catalog detail -------*/

	/*------- 990 comments -------*/
	.comment-field{width: 100%; margin-right: 0; margin-left: 0;}
	.comment-buttons, .comment-form-note{float: none; width: 100%;}
	.tab-comments .comment-form-note{padding: 10px 40px 0; text-align: center;}
	.comment{font-size: 13px; line-height: 18px;}
	.comment-header{font-size: 13px;}
	.comment-rate{padding-top: 0; margin-top: -1px;}
	.comments-subtitle{font-size: 16px; line-height: 23px; padding: 0 0 10px;}
	/*------- /990 comments -------*/

	/*------- 990 brands -------*/
	.cm-logo{
		margin: 10px 0 15px;
		img{max-height: 30px;}
	}
	.c-desc{
		font-size: 17px; line-height: 24px; padding: 0 70px;
		iframe{height: 300px;}
	}
	/*------- /990 brands -------*/

	/*------- 990 cart -------*/
	.w-col2{width: 280px;}
	.w-totals-title{font-size: 20px; line-height: 26px;}
	.wp-image{width: 75px;}
	.wp-title{font-size: 14px; line-height: 16px;}
	.wp-attrs, .wp-qty-container{padding: 0;}
	.wp-total{
		width: 90px; display: block;
		.formated-price{font-size: 24px;}
		&.no-energy-badge{display: flex; align-items: center; margin-top: -7px;}
	}
	.wp-qty-container{width: 126px;}
	.wp-row-col2{padding-left: 10px;}
	.wp-qty-container{padding-top: 6px;}
	.wp-qty-count .product-qty-second{
		display: block;
		ins{display: none;}
	}
	/*------- /990 cart -------*/

	/*------- 990 coupon widget -------*/
	.ww-coupons{float: left; text-align: left;}
	.ww-coupons-add{width: 100%;}
	.ww-coupons-label{padding: 0 0 10px 0;}
	.ww-coupons-list{width: 100%; float: none;}
	.w-totals{padding: 15px;}
	.w-btn-finish{margin: 15px 0 0;}
	.payment-note{padding: 0;}

	.ww-auth-coupons-form{position: relative; top: auto; right: auto; margin-bottom: 25px;}
	.ww-auth-coupons-table{
		.col-code{width: 100px;}
		.col-valid{width: 125px;}
		.col-value{width: 90px;}
	}
	/*------- /990 coupon widget -------*/

	/*------- 990 wishlist -------*/
	.btn-wishslit-delete{height: 50px; font-size: 14px; top: -70px;}
	.wishlist-header{margin: 35px 0 25px;}
	/*------- /990 wishlist -------*/

	/*------- 990 compare -------*/
	.c-compare-sidebar-attributes:before{display: none;}
	.c-compare-btns-cnt{display: none;}
	.c-compare-items{margin-left: 200px;}
	.page-compare-title{
		font-size: 24px; line-height: 28px; width: 200px; padding: 0;
		&:before{width: 100px; height: 100px; line-height: 100px; font-size: 50px; top: 80px; margin-left: -50px;}
	}
	.table-c-all-attributes{width: 200px; font-size: 15px; border-top: 1px solid @borderColor;}
	.cp-compare:not(.cp-new-compare){
		height: 565px;
		.cp-card-prices{display: none;}
		.cp-cnt{min-height: 307px;}
		.cp-extra-price-lowest{max-width: 65px;}
	}
	.cp-compare-input, .cp-compare-title{font-size: 11px;}
	/*------- /990 compare -------*/

	/*------- 990 sweepstake -------*/
	.sws-title{
		font-size: 30px; line-height: 35px; margin-left: 0; padding: 8px 0 20px 55px;
		&:before{width: 40px; line-height: 40px;}
	}
	.wrapper-sweepstake{margin: 0;}
	.sweepstake-title{font-size: 21px; line-height: 27px;}
	.sweepstake-pager li{font-size: 14px; padding: 0 4px 0 25px;}
	.sweepstake-pager-cnt .wrapper, .sweepstake-buttons{margin: 0;}
	.sweepstake-pager-cnt{margin: 0 0 25px;}
	.page-sweepstake-detail .main-wrapper{padding: 0 0 30px;}
	.sweepstake-fields{margin-bottom: 35px;}
	.btn-sweepstake-cancel{
		font-size: 12px; padding-right: 19px;
		&:after{font-size: 11px; line-height: 11px;}
	}

	.c-sweepstake-header{
		margin-bottom: 20px;
		h1{font-size: 15px; line-height: 20px;}
	}
	.c-sweepstake-label{
		position: relative; padding: 0 0 0 74px; font-size: 15px;
		.btn{margin-left: 0; display: flex; margin-top: 3px;}
		&:before{.pseudo(60px,68px); background: url(images/icons/face.svg) no-repeat left top; background-size: contain; left: 0; top: 0px;}
	}
	.c-sweepstake-wrapper{height: 95px;}
	/*------- /990 sweepstake -------*/

	/*------- 990 auth -------*/
	.page-auth-login, .page-auth-signup, .page-webshop-login{
		.main-wrapper{padding: 0px 0 30px;}
	}
	.a-col1{padding-right: 20px;}
	.a-col2{
		padding-left: 20px;
		.btn{min-width: 150px;}
	}
	.a-col2 h2, .a-subtitle{font-size: 18px; line-height: 21px;}
	.page-auth-login .a-col .btn{min-width: 150px; width: 150px;}
	.btn-signup{width: 150px;}

	.page-auth .cms-content{padding-right: 25px;}
	.a-auth-title{font-size: 32px; line-height: 42px;}
	.a-menu li, .a-intro, .a-intro-title{font-size: 15px;}
	.a-section-title{padding: 0 0 15px;}
	.table-header{font-size: 14px;}
	.col-order-num{width: 115px;}
	.col-order-date, .col-order-status, .col-order-tracking{width: 80px;}
	.col-order-status{width: 70px;}
	.order-row, .btn-order-details{font-size: 12px;}
	.col-order-btns{width: 110px;}
	.btn-order-details{
		&>span{padding-right: 19px;}
		.toggle-icon{top: 0;}
	}
	.a-intro{margin-bottom: 30px;}
	.a-intro-left, .a-intro-user{width: 100%;}
	.a-intro-user{padding-left: 30px; margin-top: 10px;}
	.a-intro-user-title:before{font-size: 18px; line-height: 18px; left: -28px; top: 2px;}
	.auth-box{padding-bottom: 45px;}
	.auth-box-coupons{padding: 0;}
	/*------- /990 auth -------*/

	/*------- 990 orders -------*/
	.order-details{padding-right: 115px;}
	.wp-sum{
		font-size: 18px;
		span{padding: 15px 0 0px 10px;}
	}
	/*------- /990 orders -------*/

	/*------- 990 search -------*/
	.s-headline{font-size: 15px; line-height: 21px;}
	.s-nav{
		font-size: 14px;
		a{min-width: 150px;}
	}
	.s-h1{padding: 17px 0 15px;}
	.s-item{padding: 0 0 30px;}
	.s-items{max-width: 90%;}
	.s-item-cnt{font-size: 16px;}
	.s-header{margin: 20px 0;}
	/*------- /990 search -------*/

	/*------- 990 thank you -------*/
	.invoice-container{font-size: 15px; line-height: 24px;}
	/*------- /990 thank you -------*/

	/*------- 990 footer -------*/
	.footer{font-size: 12px; line-height: 18px;}
	.wrapper-footer{padding: 30px 0 35px;}
	.footer-title{font-size: 15px; line-height: 20px; margin-bottom: 7px;}
	.footer-col1 .footer-col-cnt{padding-top: 4px;}
	.footer-col4{
		width: calc(100%/4);
		ul{margin-left: 0;}
		ul li{
			line-height: 16px; padding-left: 17px;
			&:before{top: 6px;}
		}
	}
	.footer-col2, .footer-col3{padding: 0;}
	//.footer-col2{padding-right: 20px;}
	.social-footer{padding-top: 5px;}
	.free-shipping{padding-top: 5px;}
	.cards {
		position: initial;
		p{flex-wrap: wrap;}
		img{max-width: 20px; height: auto;}
	}
	.nav-footer{font-size: 13px;}
	.footer-row2{padding: 10px 0;}
	.footer-row3{
		.footer-col1{font-size: 11px; line-height: 17px;}
		.footer-col2{
			p{margin-right: 10px;}
			p:last-child{margin-right: 0;}
			img{max-height: 55px; width: auto;}
		}
	}
	.footer-row3{
		.footer-col1{width: calc(100%/4 - 20px);}
	}
	.exchange-rate{font-size: 11px;}

	//ROW4
	.footer-row4{
		.footer-col1{width: calc(100%/4 - 20px);}
	}
	.nav-footer-locations{
		li:after{width: 8px; height: 8px; top: 11px;}
	}
	.safe-purchase{background-size: 36px auto; font-size: 12px; line-height: 14px; padding-left: 50px; width: 105px;}
	/*------- /990 footer -------*/
}

@media screen and (max-width: 750px) {
	/*------- 750 selectors -------*/
	body{line-height: 22px;}
	h1{font-size: 30px; line-height: 31px;}
	h2{font-size: 24px; line-height: 28px;}
	h3{font-size: 21px; line-height: 25px;}
	/*------- /750 selectors -------*/

	/*------- 750 helpers -------*/
	.extra{font-size: 18px; line-height: 25px;}
	.extra2, .extra3{padding: 15px; font-size: 17px;}
	.testimonial-cnt{margin: 0; min-height: 0; padding: 55px 20px 20px 20px;}
	.testimonial{
		padding-top: 60px;
		img{z-index: 5; max-width: none; width: auto; height: 120px; left: 50%; margin-left: -60px;}
	}
	.wrapper{margin: 0 15px;}
	.lloader{background-size: 45px auto;}
	/*------- /750 helpers -------*/

	/*------- 750 slick -------*/
	.slick-arrow{
		width: 35px; height: 35px;
		&:before{line-height: 35px;}
	}
	/*------- /750 slick -------*/

	/*------- 750 header -------*/
	.p-ticker, .top-flyer{display: none;}
	.logo{width: 53px; height: 29px; top: 9px; left: 16px;}
	.wrapper-header{height: 96px; margin: 0;}
	.top-container{top: 0; right: 48px;}
	.header{background: #fff;}
	.fixed-header{
		.header-placeholder{height: 90px;}
		.header{position: fixed; left: 0; top: 0; right: 0; box-shadow: 0 3px 10px rgba(0,0,0,.2);}
		.wrapper-header{height: 48px;}
		.sw{display: none;}
		&:not(.active-autocomplete).sw{display: none;}
	}
	.social-top{
		padding: 0 15px 15px;
		a{
			height: 37px; width: 50%; background: @gray; color: #fff; font-size: 14px; border: 0;
			&:before{color: @yellow; margin-right: 7px;}
		}
		a.in{border-left: 1px solid #fff;}
	}
	.top-contact{width: 100%; text-align: center;}
	/*------- /750 header -------*/

	/*------- 750 navigation -------*/
	.nav-container{display: none;}
	.btn-toggle-nav{
		display: block; position: absolute; top: 0; right: 0; background: @yellow; width: 48px; height: 48px; line-height: 48px;
		span, span:after, span:before { .pseudo(20px,2px); background: @gray; left: 0; top: 0; .transition(all); }
		span {
			top: 24px; left: 15px;
			&:before {top: -6px;}
			&:after {top: 6px;}
		}
		&.active {
			span {background: transparent;}
			span:after {.rotate(45deg); top: 0;}
			span:before {.rotate(-45deg); top: 0;}
		}
		&:hover{text-decoration: none; color: @red;}
	}
	.m-nav-title{
		display: none; align-items: center; font-size: 20px; line-height: 23px; font-weight: bold; color: @gray; padding: 0 50px 0 40px; height: 100%;
		&:before{.icon-arrow-right; position: absolute; left: 16px; top: 18px; font: 12px/12px @fonti; color: @gray; .rotate(180deg);}
	}
	.nav-categories{display: none;}
	.active-nav{
		position: fixed;
		.header{position: fixed; height: 100vh; left: 0; right: 0;}
		.zopim{display: none!important;}
		.wrapper-header{height: 48px;}
		.top-container, .nav-container, .logo{display: none;}
		.nav-categories{display: flex;}
		.social-top{
			display: flex; margin-left: 0; padding: 0 15px 24px; margin-top: 20px;
			a{
				height: 40px; width: auto; background: #3B5998; font-size: 16px; padding-left: 16px; padding-right: 16px;
				&:before{color: @white; margin-right: 31px; font-size: 14px; line-height: 14px;}
				&:after{left: 39px;}
			}
		}
		.nav{
			display: flex; padding-bottom: 0;
			li{font-size: 16px;}
		}
		.sw{display: none;}
		.top-contact{
			display: block; background: @gray; color: @white; position: absolute; height: 66px; top: auto; right: auto; bottom: 0; text-align: left; padding: 22px 15px 23px; font-size: 16px; line-height: 17px; 
			a{color: @white; font-size: 16px; line-height: 17px;}
		}
		.header-m{height: calc(~"100vh - 47px"); padding-bottom: 66px; margin-top: -1px; overflow-y: auto; overflow-x: hidden; border-top: 1px solid @borderColor;}
		.nc-m-cnt{
			display: flex; width: 300vw; .transition(all);
			&.lvl2{
				transform: translate3d(-100vw, 0, 0);
				.nc-m-lvl1{height: 54px; overflow: hidden;}
			}
			&.lvl3{
				transform: translate3d(-200vw, 0, 0);
				.nc-m-lvl1{height: 54px; overflow: hidden;}
				.nc-m-lvl2{height: 54px; overflow: hidden;}
			}
		}
		.m-nav-title{display: flex;}
		.nc-m{display: block;}
		.nc-m-lvl2, .nc-m-lvl3{
			display: none; z-index: 1;
			&.active{display: block; overflow-y: auto; width: 100vw;}
		}
		.menu-lvl-active{
			.nav, .social, .top-contact{display: none;}
		}
	}
	.nc-m{
		width: 100vw; position: static; flex-grow: 0; padding: 0; margin: 0; list-style: none; flex-shrink: 0; display: none; position: relative; left: auto; top: auto; right: auto; bottom: auto; height: auto; box-shadow: none; font-size: 18px; text-transform: initial; font-weight: normal;
		&>li{
			border: 0; border-bottom: 1px solid @borderColor; height: auto; display: block; position: relative;
			&>a{display: flex; text-decoration: none; align-items: center; justify-content: left; background: @white; padding: 15px; color: @gray; z-index: 1;}
			&.red{
				a{color: @red;}
			}
			&:last-child{border-bottom: 1px solid @borderColor;}
		}
	}
	.nc-m .has-children&>a{
		position: relative;
		&:after{.icon-arrow-right; position: absolute; right: 21px; top: 21px; font: 12px/12px @fonti; color: @gray;}
	}
	.submenu-col2{
		display: block; padding: 32px 16px 24px; margin: 0;
		&:before{display: none;}
	}
	.nav-bestsellers-items-title{font-size: 24px; line-height: 32px; padding-bottom: 8px;}
	.bs-show-more{display: none;}
	.nav-bestsellers-items{

		.cp-small{
			width: 100%; flex-flow: column;
			.cp-small-attrs{
				display: inline-block!important; font-size: 12px; line-height: 16px;
				.cp-attr{margin-right: 5px;}
			}
		}
		.cp-small-col1{display: block; position: relative; width: initial;}
		.cp-small-image{
			position: absolute; top: 0; left: 0; width: 110px; display: flex; align-items: center; justify-content: center; padding-bottom: 15px;
			a{height: 104px; margin: auto; width: 95%;}
			img{display: block; margin: auto;max-height: 100%;}
		}
		.cp-small-col2{padding-left: 0;}
		.cp-small-header{padding-left: 125px; min-height: 125px; flex-grow: 1;}
		.cp-small-brand{
			position: relative; top: auto; left: auto; margin-bottom: 6px;
			img{max-width: 55px;}
		}
		.cp-small-title{font-size: 14px; line-height: 18px;}
		.cp-small-code{padding-top: 2px;}
		.cp-small-card-prices{
			font-size: 13px; flex-wrap: wrap;
		}
		.cp-card-price{width: 100%; max-width: 120px;}
		
		.cp-small-price{
			.cp-small-card-prices{
				flex-wrap: wrap; font-size: 13px;
				.cp-card-price{
					width: 100%; max-width: 115px; padding-bottom: 4px;
					.cp-card-discount{width: 32px; font-size: 12px; padding: 3px 0 2px; margin-right: 7px;}
				}
			}
			.cp-current-price{
				font-size: 26px;
				.formated-price{
					.p-small{text-transform: initial;}
					.p-€{right: 10px;}
				} 
			}
		}

		.cp-small-col3{
			position: relative; right: unset; bottom: unset; width: 100%; margin-top: 9px;
			.cp-small-add{
				width: auto; flex-grow: 1; position: relative; font-size: 14px; line-height: 16px;
				span{
					padding-left: 26px;
					&:before{left: 0; top: -2px!important;}
				}
				&:hover{border-color: @gray;}
			}
		}
	}

	.nav{
		display: none; flex-wrap: wrap; padding: 15px 0 10px;
		&>li{width: 50%; margin: 0; font-size: 15px;}
		a{padding: 4px 15px;}
	}
	/*------- /750 navigation -------*/

	/*------- 750 search widget -------*/
	.sw{
		left: 0; width: 100%; top: 48px; height: 48px; border-right: 0; border-left: 0; position: relative;
		form{position: initial; left: initial; right: initial; width: initial;}
		&:before{display: none;}
		button{
			width: 48px; height: 48px; background: none; font-size: 0;
			&:after{.icon-search; font: 16px/48px @fonti; position: absolute; left: 0; top: 0; width: 100%; color: @gray;}
			&:hover{background: none;}
		}
		&.active{
			.sw-input{padding-left: 40px;}
		}
	}
	.autocomplete-wrapper{display: block;}
	.sw-input{height: 48px; padding-left: 16px; font-size: 16px; padding-right: 70px;}
	.sw-toggle{
		left: 14px;
		&:before{font-weight: bold;}
	}
	.sw-list-container{top: 47px; width:auto; box-shadow: none; right: -1px; left: -1px;}
	/*------- /750 search widget -------*/

	/*------- 750 autocomplete -------*/
	.autocomplete-container{top: 43px; left: -1px; right: -1px;}

	body.active-autocomplete{
		.sw{display: block!important;}
		.page-wrapper{
			overflow: initial; position: relative;
			&>*:not(.header):not(.sw):not(.autocomplete-container){overflow: hidden; min-height: 0; height: 0; max-height: 0; visibility: hidden; padding: 0; margin: 0; border: 0;}
		}
	}

	.sw-autocomplete-container{top: 47px; right: 0; left: 0; box-shadow: none; width: 100%;}
	/* .ui-autocomplete{
		background: #fff; list-style: none; width: 100% !important; left: 0px !important; right: -210px !important; max-height: 640px; font-size: 16px; line-height: 22px; z-index: 550 !important; top: 0 !important; overflow: auto; padding: 0!important; margin: 0!important;
		a{
			display: block; color: @textColor; text-decoration: none;
			&:hover{text-decoration: underline;}
		}
	} */
	.autocomplete-wrapper{box-shadow: none; display: block; height: 100%;}
	.autocomplete-col1{width: 100vw; border-bottom: 1px solid @borderColor; border-right: 0;}
	/*------- /750 autocomplete -------*/

	/*------- 750 cart widget -------*/
	.ww{
		width: 48px!important; font-size: 0; height: 48px; margin: 0; border-left: 1px solid @borderColor;
		&.active{
			.ww-items{
				background: none;
				&:after{font-size: 17px; line-height: 48px; left: 0; .icon-cart_active;}
			}
			.ww-counter{
				font-size: 0; display: block; width: initial; bottom: unset; top: 5px; right: 6px; left: unset;
				span{font-size: 11px; display: block; font-weight: normal;}
			}
		}
	}
	.ww-items{
		height: 48px; width: 100%; padding: 0!important; font-size: 0!important; border: 0;
		&:after{width: 100%!important; height: 48px; line-height: 48px; left: initial;}
	}
	.ww-counter{
		color: @textColor; right: 8px; top: 6px; line-height: 11px; position: absolute; font-size: 0;
		.value{font-size: 11px;}
	}
	.ww-counter-label{display: none;}
	/*------- /750 cart widget -------*/

	/*------- 750 compare widget -------*/
	.cw-compare{
		border: 0; margin-left: 0; width: 48px; height: 48px; border-left: 1px solid @borderColor;
		a:before{line-height: 48px; font-size: 19px; width: 48px; height: 48px;}
	}
	/*------- /750 compare widget -------*/

	/*------- 750 auth widget -------*/
	.aw{position: relative; top: auto; right: auto; height: 48px; border-left: 1px solid @borderColor; font-size: 0;}
	.aw-btn{
		width: 48px; height: 48px; padding: 0!important; border: 0; display: flex; justify-content: center; align-items: center;
		//&:before{.icon-user; width: 17px; height: 17px; font: 17px/17px @fonti; color: @textColor; position: relative; background: none;}
		span{
			padding-left: 0; width: 100%; display: flex; align-items: center; justify-content: center; font-size: 0;
			&:before{top: initial; left: initial; font-size: 19px; line-height: 19px;}
		}
		&:hover{background: none;}
	}
	.aw-logout, .aw-signup{display: none!important;}
	/*------- /750 auth widget -------*/

	/*------- 750 wishlist -------*/
	.wishlist{
		width: 48px; height: 48px; border: 0; border-left: 1px solid @borderColor;
		a:before{line-height: 48px; font-size: 16px; width: 48px; height: 48px;}
	}
	/*------- /750 wishlist -------*/

	/*------- 750 main -------*/
	.sidebar{display: none;}
	.cms-content{margin: 0;}
	.main{margin-top: 15px;}
	.terms-pbz{min-height: 110px; content-visibility: auto; contain-intrinsic-size: 110px;}
	.terms-cards1{min-height: 120px; content-visibility: auto; contain-intrinsic-size: 120px;}
	.terms-cards2{min-height: 90px; content-visibility: auto; contain-intrinsic-size: 90px;}
	.main-wrapper{padding: 0 0 35px;}
	.cms-content{
		ul:not(.nav-locations){margin-left: 0;}
		ol{margin: 0 0 0 20px;}
		.btn{width: 100%;}
	}
	.table-wrapper{
		margin-bottom: 20px;
		table{min-width: 500px;}
	}
	/*------- /750 main -------*/

	/*------- 750 contact -------*/
	.contact-row{flex-wrap: wrap;}
	.contact-col{width: 100%; padding: 0;}
	.contact-col1{margin-bottom: 25px;}
	/*------- /750 contact -------*/

	/*------- 750 share -------*/
	.share{margin: 20px 0 0;}
	/*------- /750 share -------*/

	/*------- 750 faq -------*/
	.page-faq .share{padding-left: 25px;}
	.fp-title{
		font-size: 15px; line-height: 19px; padding-left: 25px;
		&:after{height: 2px; width: 13px; top: 10px;}
		&:before{width: 2px; height: 13px; top: 4px;}
	}
	.fp-cnt{font-size: 15px; line-height: 21px; padding-left: 25px;}
	/*------- /750 faq -------*/

	/*------- 750 locations -------*/
	.l-items{padding-top: 50px;}
	.nav-locations li{width: 50%;}
	.lp{flex-wrap: wrap; text-align: center;}
	.lp-col{width: 100%;}
	.lp-title{text-align: center;}
	.lp-col2{padding: 15px 0 0 0;}
	.lp-images{
		.slick-arrow{
			width: 35px; height: 35px; top: calc(~"(100vw - 30px) / 3.5");
			&:before{line-height: 35px;}
		}
		.slick-next{right: -10px;}
		.slick-prev{left: -10px;}
	}
	/*------- /750 locations -------*/

	/*------- 750 newsletter -------*/
	/* .nw{
		min-height: 0;
		&:after{font-size: 20px; line-height: 20px; top: 12px; left: 15px;}
		&:before{top: -90px; left: -90px;}
	} */
	.nw-success{font-size: 13px;}
	/*------- /750 newsletter -------*/

	/*------- 750 hero slider -------*/
	.hero-slider{display: block; /* min-height: 350px; */}
	/* .hero-slider-container{
		margin-left: 0; margin-right: 0; flex-wrap: wrap;
		&:after{display: none;}
		.slick-dots{left: 15px; bottom: 10px;}
		.sidebar-flyer, .sidebar-advisor{
			font-size: 17px;
			a{padding: 15px;}
		}
		.sidebar-flyer{
			img{max-height: 110px;}
			span{font-size: 13px; line-height: 21px;}
		}
		.sidebar-advisor{
			img{max-width: 60px; width: 60px; margin: 10px auto;}
			strong{font-size: 17px; line-height: 18px;}
			.btn-small{height: 30px; font-size: 16px;}
		}
	}
	.hp-header-col{
		width: 100%;
		img{width: 100%;}
	}
	.hp-header-col2{border: 0; border-top: 1px solid #fff; content-visibility: auto; contain-intrinsic-size: 500px;} */

	.hp-header{padding: 0;}
	.hero-slider-container{margin: 0;}
	.c-promo{
		min-height: 450px; max-height: 100%;
		&:after{left: 24px; right: 24px; height: 6px; bottom: -3px;}
	}
	.wrapper-hp-promo{min-height: 450px;}
	.c-promo-container{max-height: 100%; min-height: 450px; min-width: 100%; max-width: 100%; padding: 0 24px 27px; margin-left: 0;}

	.c-promo-title{text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);}
	.c-promo-btn{height: 48px; padding: 2px 15px 0;}
	.c-promo-center{
		.c-promo-container{min-width: 100%; max-width: 100%;}
	}
	/*------- /750 hero slider -------*/

	/*------- 750 popular categories -------*/
	.popular-categories-section{
		.wrapper{margin: 0;}
	}
	.popular-categories-cnt{margin: 0;}
	.pop-hp-categories-title{padding-top: 40px;}
	.pop-hp-categories{border-bottom: 1px solid @borderColor; box-shadow: none;}
	.cat-item{
		width: calc(~"100%/3"); padding: 15px 15px 12px; line-height: 16px;
		&:nth-child(4n){border-right: 1px solid @borderColor;}
		&:nth-child(-n+4){border-top: 1px solid @borderColor;}
		&:nth-child(3n){border-right: 0;}
		img{max-height: 88px;}
	}
	/*------- /750 popular categories -------*/

	/*------- 750 hp-promos -------*/
	.hp-promos-cnt{padding-top: 16px;}
	/*------- /750 hp-promos -------*/

	/*------- 750 hp-brands -------*/
	.hp-brands{
		.mw{height: 56px; contain-intrinsic-size: 56px; padding: 0;}
		.mwp{
			a{height: 100%;}
			img{max-width: 66px; max-height: 30px;}
		}
	}
	/*------- /750 hp-brands -------*/

	/*------- 750 hp promo section -------*/
	.panel-row{flex-wrap: wrap;}
	.panel{
		width: 100%; margin: 0 0 15px; min-height: 0; padding: 24px 24px 137px;
		ul{font-size: 16px; line-height: 20px;}
	}
	.panel-title{font-size: 26px; line-height: 29px; padding-bottom: 9px;}
	.panel-cols{column-gap: 10px;}
	.panel-footer{
		height: 56px;
		img{max-width: 100%;}
	}
	.section-gray{
		.wrapper{margin: 0 15px;}
	}
	.hp-categories-cnt{display: block; margin: 16px 0;}
	.hp-categories-title{
		font-size: 28px; line-height: 32px; font-weight: bold; color: @white; font-weight: 300; padding-bottom: 16px;
		strong{color: @yellow; font-weight: bold;}
	}
	.hp-categories{
		font-size: 0; line-height: 0; list-style: none; margin: 0; padding: 0; width: calc(~"100% - -30px"); margin-left: -15px; position: static; flex-grow: 0; flex-shrink: 0; display: block;
		&>li{
			display: block; position: relative; border-bottom: 1px solid rgba(255,255,255,0.1); border-top: 1px solid rgba(255,255,255,0.1); margin-top: -1px;
			&>a{
				display: block; font-size: 18px; line-height: 23px; color: @white; font-weight: bold; text-decoration: none; padding: 17px 15px 16px;
				&:before{display: block; position: absolute; .icon-arrow-right; color: @yellow; font: 12px/12px @fonti; right: 17px; top: 22px;}
			}
			&.nav-home{display: none;}
		}
	}
	/*------- /750 hp promo section -------*/

	/*------- 750 support - stores -------*/
	.locations{flex-wrap: wrap; margin-bottom: 15px;}
	.locations-col1{width: 100%; margin: 0;}
	.locations-col2{width: 100%; margin-top: 40px;}
	.panel-stores{
		background: none;
		.nav-stores{
			font-size: 16px; max-width: 300px; margin: auto;
			li{
				&:after{width: 6px; height: 6px; top: 9px;}
				a:after{transform: scaleX(1); height: 1px;}
			}
			li:nth-child(4){
				padding: 0; margin: 0;
				&:after{display: none;}
			}
		}
	}
	.panel-stores-title{
		font-size: 23px; padding: 30px 0 55px; top: 0; position: relative;
		&:after{width: 85px; height: 85px; margin-left: -47px;}
		&:before{width: 60px; height: 35px; top: 24px; margin-left: -32px;}
		span{
			&:first-child{padding-right: 40px;}
			&:last-child{padding-left: 80px;}
		}
	}
	.nav-stores{position: relative; flex-wrap: wrap;}
	.panel-support-header{padding: 24px 24px 18px; font-size: 26px; line-height: 29px; height: 100%;}
	.panel-support-footer{
		padding: 28px 24px; padding-right: 10px; font-size: 16px; line-height: 22px;
		p:last-child{font-size: 16px; line-height: 22px;}
	}
	
	/*------- /750 support - stores -------*/

	/*------- 750 miele -------*/
	.panel-miele{
		padding-bottom: 19px; font-size: 14px;
		.flex-col{flex: 0 0 100%; border: 0; padding: 0 15px 0;}
		.flex-col1{border-bottom: 1px solid rgba(0, 0, 0, 0.15); padding-top: 20px; padding-bottom: 15px; margin-bottom: 20px;}
		ul li:last-child{margin-bottom: 8px;}
	}
	.panel-miele-cnt{flex-wrap: wrap;}
	.logo-miele{width: 90px; height: 35px; margin-left: -45px;}
	/*------- /750 miele -------*/

	/*------- 750 hp posts-------*/
	.pw-hp{
		padding: 0 0 40px!important;
		.wrapper{margin: 0;}
		.pw-title{padding: 0 15px 15px;}
		.pw-btns{padding: 24px 15px 0;}
	}
	.hp-p-items{
		position: relative; display: flex; flex-wrap: nowrap; overflow-x: scroll; overflow-y: hidden; width: 100%; gap: 8px;
		&::-webkit-scrollbar, &::-webkit-scrollbar-thumb, &::-webkit-scrollbar-track{background: transparent; display: none;}
		&::-webkit-scrollbar-track{border: none; display: none;}

		.pp{
			margin-top: 0; width: 82%; min-width: 82%;
			&:first-child{margin-left: 15px;}
			&:last-child{margin-right: 15px;}
		}
		.pp-cnt{
			padding-top: 11px;
			&:before{height: 6px; left: 15px; right: 15px;}
		}
		.pp-title{font-size: 20px!important;}

		.pp-featured{
			.pp-cnt{
				padding-top: 11px;
				&:before{height: 6px; left: 15px; right: 15px;}
			}
			.pp-title{padding-bottom: 0; font-size: 20px!important; text-transform: initial;}
		}
		.pp-featured-small{
			.pp-cnt{
				padding-top: 11px;
				&:before{height: 6px; left: 15px; right: 15px;}
			}
			.pp-title{padding-bottom: 0; font-size: 20px!important;}
		}
	}

	/*------- /750 hp posts -------*/

	/*------- 750 catalog widget -------*/
	.cw{padding: 40px 0; border: 0;}
	.cw-title{font-size: 28px; line-height: 32px;}
	.cw-items-wrapper{margin: 0;}
	.cw-slider{display: flex; flex-wrap: wrap;}
	.btn-cw-all{width: 100%; margin: 15px 0 0;}
	.cw-btns{padding: 0 15px;}
	.cw-slider{
		position: relative; display: flex; flex-wrap: nowrap; overflow-x: scroll; overflow-y: hidden; width: 100%; padding-left: 1px;
		&::-webkit-scrollbar, &::-webkit-scrollbar-thumb, &::-webkit-scrollbar-track{background: transparent; display: none;}
		&::-webkit-scrollbar-track{border: none; display: none;}

		.cp{
			margin-top: 0; width: 82%; min-width: 82%;
			&:first-child{margin-left: 15px;}
			&:last-child{margin-right: 15px;}
		}
		.cp-image{
			top: 15px; width: 110px;
			figure{height: 104px;}
		}
		.cp-cnt{padding: 15px;}
		.cp-card-prices{margin-bottom: 9px;}
		.cp-price{padding-top: 10px;}
		.cp-card-price{padding: 0px;}
		.cp-card-prices{flex-flow: unset; flex-wrap: wrap; border: none;}
		.cp-card-price{width: 125px; border: none; padding-bottom: 4px;}
		.cp-current-price{
			font-size: 26px; line-height: 30px;
			ins{height: 20px;}
		}
		.cp-card-discount{padding: 3px 0 2px; font-size: 12px;}
		.cp-title{font-size: 14px; line-height: 18px;}
		.cp-code{font-size: 12px; line-height: 16px;}
		.cp-attrs{
			padding-bottom: 0; font-size: 12px; line-height: 16px;
			&>li{
				margin-right: 5px;
				&:before{top: 4px;}
			}
		}
		.cp-cnt-header{padding-left: 125px; min-height: 138px;}
		.cp-header-cnt{padding-top: 0;}
		.cp-brand{ margin-bottom: 6px;}
		//.cp-cnt{padding: 25px 15px;}
		/* .cp-image{
			position: absolute; top: 15px; left: 15px; width: 120px;
			figure{height: 150px;}
		} */
		.cp-badges{right: auto; left: 13px; top: 20px;}
		/* .cp-unavailable{position: relative; top: auto;}
		.cp-unavailable-btn{
			padding: 8px 14px; margin-bottom: 14px;
			&:after{display: none;}
		} */
		.cp-btns{right: 0px; bottom: 0px;}
		.cp-wishlist{
			font-weight: normal; width: 40px;
			&>a{
				&>span{font-size: 0; line-height: 0;}
				&:before{position: absolute; margin-right: 0; font-size: 15px; line-height: 39px; width: 100%; margin-top: 0;}
			}
		}
		.cp-wishlist-btn{display: block; padding: 0; min-width: unset;}
		.cp-wishlist-remove{display: none;}
		.cp-wishlist.active .cp-wishlist-remove{display: block;}
		.cp-compare-container{width: 40px;}
		.cp-btn-compare{
			font-size: 0; display: block; font-weight: normal; padding: 0; min-width: unset;
			&:before{width: 100%; position: absolute; margin-right: 0; font-size: 16px; line-height: 39px; margin-top: -0;}
			small{font-size: 0; display: none!important;}
		}

		.cp-extra-price-lowest{
			padding-top: 4px; font-size: 11px; max-width: unset;
			strong{display: initial;}
		}
	}
	/*------- /750 catalog widget -------*/

	/*------- 750 promo -------*/
	.promo-col{width: 100%; margin: 0 0 1px;}
	.promo{
		padding: 0 0 20px;
		.slick-prev{left: -10px;}
		.slick-next{right: -10px;}
		.slick-arrow{top: calc(~"(100vw - 30px) / 3.5");}
	}
	/*------- /750 promo -------*/

	/*------- 750 brands widget -------*/
	.mw{
		height: 50px; padding: 0 25px;
		a{height: 50px; padding: 0 15px;}
		.slick-arrow{
			top: 0; border-radius: 0; border: 0; height: 49px; width: 26px; box-shadow: none;
			&:before{line-height: 49px;}
		}
		.slick-prev{left: 0; border-right: 1px solid @borderColor;}
		.slick-next{right: 0; border-left: 1px solid @borderColor;}
	}
	/*------- /750 brands widget -------*/

	/*------- 750 catalog -------*/
	.c-header{padding: 20px 0 15px;}
	.cm-logo{
		margin: 5px 0 15px;
		img{max-height: 20px;}
	}
	.c-desc{
		padding: 0 15px;
		iframe{height: 195px; min-height: 195px;}
	}
	.c-header{padding-bottom: 10px;}
	.c-sidebar-advisor{
		margin-bottom: 10px;
		.btn{height: 40px; top: 12px; font-size: 18px;}
	}
	.sidebar-advisor{
		font-size: 15px;
		strong{font-size: 19px;}
		img{margin: 3px 0 0 0;}
		a{padding: 14px 0 0 68px;}
	}
	.c-wrapper{margin: 0; width: calc(~"100% - -2px");}
	.c-items{padding-bottom: 25px;}
	.c-load-more-container{padding: 0 15px;}
	.btn-load-more{margin: 5px 0 30px;}
	.c-empty{padding: 0 15px 25px;}
	.c-title{padding-bottom: 10px;}
	.category-level2 .c-header{padding: 20px 15px 5px;}
	/*------- /750 catalog -------*/

	/*------- 750 catalog post -------*/
	.cp{width: 100%;}
	.cp-card-prices{border: none; border-bottom: 0; flex-flow: initial; flex-wrap: wrap; margin-bottom: 9px;}
	.cp-rating-container{display: none;}
	.cp-card-price{width: 150px; border: none; padding-bottom: 4px;}
	.cp-price-note{font-size: 12px;}
	.cp-current-price{
		font-size: 28px; line-height: 26px;
		ins{height: 20px;}
	}
	.cp-title{font-size: 15px;}
	.cp-code{font-size: 13px;}
	.cp-attrs{
		padding-bottom: 0;
		&>li{
			margin-right: 5px;
			&:before{top: 5px;}
		}
	}
	.cp-cnt-header{padding-left: 150px; min-height: 155px;}
	.cp-header-cnt{padding-top: 0;}
	.cp-brand{position: relative; top: auto; left: auto; margin-bottom: 10px;}
	.cp-cnt{padding: 25px 15px;}
	.cp-image{
		position: absolute; top: 25px; left: 15px; width: 130px;
		figure{height: 124px;}
	}
	.cp-badges{right: auto; left: 13px; top: 20px;}
	.cp-unavailable{position: relative; top: auto;}
	.cp-unavailable-btn{
		padding: 8px 14px; margin-bottom: 14px;
		&:after{display: none;}
	}
	//.cp-btns{right: 15px; bottom: 25px;}
	/* .cp-wishlist{
		font-weight: bold; width: auto;
		&>a{
			&>span{font-size: 14px; line-height: 1;}
			&:before{position: relative; margin-right: 6px; font-size: 14px; line-height: 1; width: auto; margin-top: -2px;}
		}
	}
	.cp-wishlist-btn{display: flex; align-items: center; justify-content: center; padding: 0 10px; min-width: 95px;}
	.cp-wishlist-remove{display: none;}
	.cp-wishlist.active .cp-wishlist-remove{display: flex; color: #fff;}
	.cp-compare-container{width: auto;}
	.cp-btn-compare{
		font-size: 14px; display: flex; align-items: center; font-weight: bold; padding: 0 10px; min-width: 95px; justify-content: center;
		&:before{width: auto; position: relative; margin-right: 6px; font-size: 16px; line-height: 1; margin-top: -1px;}
		small{font-size: 14px;}
		.l2{display: none;}
		.l2 span{display: none;}
		&.compare_active{color: #fff;
			.l1{display: none;}
			.l2{display: block;}
		}
	} */


	.btn-cp-add-detail{
		font-size: 14px; line-height: 16px;
	}
	.cp-btn-addtocart{
		span{
			padding-left: 25px;
			&:before{display: block;}
		}
	}

	.cp-extra-price-lowest{
		padding-top: 15px;
		strong{display: initial;}
	}
	/*------- /750 catalog post -------*/

	/*------- 750 catalog detail -------*/
	.cd-row{display: block;}
	.cd-col1, .cd-col2{width: 100%;}
	.cd-col2, .cd-col4{padding-left: 0;}
	.cd-col3, .cd-col4{width: 100%;}
	.cd-col3{padding-right: 0;}
	.cd-col1{margin-bottom: 20px;}
	.cd-wrapper{padding-top: 15px;}
	.cd-header-placeholder{
		min-height: 115px;
		.cd-header{display: block;}
	}
	.cd-header{padding-bottom: 20px; width: 100%; display: none;}
	.cd-hero-slide{
		img{max-height: 290px; width: auto;}
		span{height: 310px;}
	}
	.cd-hero-image{height: 310px;}
	.cd-thumbs{margin-left: -15px; width: calc(~"100% - -30px"); height: 55px;}
	.cd-thumb span{height: 50px;}
	.cd-thumbs-slider .slick-arrow:before{line-height: 51px;}
	.cd-payment{height: 45px; position: static;}
	.cd-shipping-price{min-height: 36px;}
	.cd-payment-installments{
		width: 100%; line-height: 11px; font-size: 11px; top: 115%;
		&:before{right: auto; left: 70px;}
	}
	.cd-payment-installments-leanpay:before{left: auto; right: 85px;}
	.cd-benefit-item{
		margin-bottom: 17px;
		&:last-child{margin-left: 0px; margin-bottom: 0;}
		.cd-benefit{
			:before{font-size: 24px;}
			&.benefit-shipping:before{font-size: 21px;}
		}

	}
	.cd-wishlist{order: 1;}
	.cd-compare{order: 2;}
	.add-to-cart-container{order: 3; margin: 10px 0 0;}
	.cd-attrs{flex-wrap: wrap; height: auto;}
	.cp-list-attr{
		width: 50%; min-height: 140px; margin-top: -1px; padding: 17px 0 12px;
		&:first-child, &:last-child{
			&:before{.pseudo(auto,3px); background: @yellow; left: 15px; right: 15px; bottom: -2px; z-index: 10;}
		}
		&:last-child:before{bottom: auto; top: -2px;}
		&:nth-child(3):after{display: none;}
	}
	.cp-list-attr-value{font-size: 18px;}
	.cp-list-attr-image{
		height: 50px;
		img{max-height: 50px;}
	}
	.cd-related-color-item{height: calc(~"(100vw - 30px) / 5");}
	.cd-related-size-item{
		height: calc(~"(100vw - 30px) / 4");
		&>span{padding-bottom: 10px;}
	}
	.cd-row{flex-wrap: wrap;}
	.cd-row2{
		margin-left: -15px; width: calc(~"100% - -30px");
		&:before{display: none;}
	}
	.cd-col3{padding: 0;}
	.cd-attribute-title{text-align: left;}
	.cd-benefits{display: block; background: @gray; position: relative; min-height: 0; padding: 15px 15px 19px;}
	.cd-benefit.benefit-shipping{margin-left: 0;}
	.cd-share{margin: 0; padding: 25px 15px 20px;}
	.cd-related-products{padding: 30px 0 0;}
	.cd-related-title, .cd-upsale-title{text-align: center; font-size: 24px; line-height: 32px;}
	.cd-related-slider{
		width: calc(~"100% - -2px"); display: flex; flex-wrap: wrap;
		.slick-arrow{
			width: 35px; height: 35px;
			&:before{line-height: 35px;}
		}
	}
	.cd-upsale{border: 0; padding-top: 0;}
	.cd-upsale, .cd-related-slider{
		.slick-prev{left: 10px;}
		.slick-next{right: 10px;}
	}
	.cd-upsale-wrapper{margin: 0;}
	.attr-tooltip{width: 85vw;}

	.cd-extra-price-lowest{padding-top: 5px;}
	.cd-exchange-rate{padding-top: 5px;}
	/*------- /750 catalog detail -------*/

	/*------- 750 add to cart modal -------*/
	.product-message-modal{background: none;}
	.modal-box{
		width: 100vw; top: 0; height: 100vh; padding: 15px; overflow: auto; bottom: 0; margin-top: 0; box-shadow: none;
		//.close-button{right: 0; top: 0;}
		.message{
			font-size: 17px; padding-top: 20px; 
			&:before{line-height: 35px; top: -2px;}
		}
		.image, .desc{width: 100%;}
		.desc{text-align: center;}
		.close-button{top: 15px; right: 15px;}
	}
	.modal-title{font-size: 17px; line-height: 18px;}
	.modal-buttons{padding: 20px 0 0;}
	.modal-continue-shopping{width: 100%; margin-top: 20px;}
	.modal-view-cart{padding: 0 15px; font-size: 14px; height: 40px; }
	.cd-modal-price{justify-content: center;}
	.modal-price-main{text-align: right;}
	.modal-price-second{text-align: left;}

	//WARRANTY
	body.active-fancybox{
		.page-wrapper>*{overflow: hidden; min-height: 0; height: 0; max-height: 0; visibility: hidden; padding: 0; margin: 0; border: 0;}
	}
	.fancybox-overlay:has(.fancybox_modal_warranty){overflow: hidden!important;}
	.fancybox_modal_warranty{
		width: 100vw!important; height: 100vh!important; left: 0!important; top: 0!important; overflow-y: hidden!important; min-width: unset !important; max-width: unset !important;
		.fancybox-skin{padding: 15px 0 !important; height: 100vh!important; overflow-y: hidden!important;}
		.fancybox-close{
			top: 15px; right: 15px;
			&:before{font-size: 14px;}
		}
	}
	.warranty-modal{
		.modal-title{padding-top: 5px;}
		.cd-services-price{display: initial;}
		.cd-related-products{width: auto;}
		.cd-related-slider{
			.cp-unavailable{top: auto;}
			.cp-btn-details{font-size: 14px; line-height: 16px; padding: 0 10px;}
		}
	}
	.warranty-modal-intro-basic{
		.message{max-width: 270px; margin: 0 auto;}
		.modal-image{max-width: 150px; display: block;}
		.warranty-intro-cnt{padding: 0;}
	}
	/*------- /750 add to cart modal -------*/

	/*------- 750 toolbar -------*/
	.toolbar, .toolbar-col2{flex-wrap: wrap;}
	.toolbar{margin: 0 0 3px;}
	.btn-toggle-filter{order: 1; margin-bottom: 5px;}
	.c-sort{order: 2; margin: 0; width: 50%; margin-left: -1px; width: calc(~"50% - -1px"); margin-bottom: 5px;}
	.c-layout{display: none;}
	.toolbar-filter{order: 4; padding: 2px 0 0 28px; margin-top: 0; padding-right: 0}
	.toolbar-filter-qty{float: left; padding-right: 0;}
	.toolbar-filter-discount{float: right;}
	.btn-toggle-filter{margin: 0; width: 50%;}
	.c-counter{order: 3; width: calc(~"100% - -30px"); padding-left: 15px; padding-right: 15px; padding-top: 3px; padding-bottom: 8px; margin-bottom: 15px; border-bottom: 1px solid @borderColor; margin-left: -15px; margin-right: -15px;}
	.wrapper-toolbar{margin-bottom: 15px; .clear;}
	/*------- /750 toolbar -------*/

	/*------- 750 publish -------*/
	.p-featured{padding: 0;}
	.p-header{padding: 20px 0 5px;}
	.btn-load-more{margin: 10px 0 35px; width: 100%;}
	/*------- /750 publish -------*/

	/*------- 750 publish widget -------*/
	.pw{
		padding: 10px 0 30px;
		.pp-short-desc{display: none;}
		.pp-title{font-size: 19px!important; line-height: 24px!important; text-align: center;}
		.p-featured{padding: 0;}
		.slick-prev{left: -10px;}
		.slick-next{right: -10px;}
		.slick-arrow{top: calc(~"(100vw - 30px) / 4.7");}
	}
	.pw-title{font-size: 28px; text-align: center; padding: 0 0 15px;}
	.p-featured{flex-wrap: wrap;}
	.pw-btns{padding: 15px 0 0;}
	.btn-all{width: 100%;}
	/*------- /750 publish widget -------*/

	/*------- 750 publish post -------*/
	.pp{width: 100%; margin: 0 0 20px;}
	.pp-cnt{
		margin: 0;
		&:before{left: 20px; right: 20px; width: auto;}
	}
	.pp-featured{
		.pp-cnt{
			margin: 0;
			&:before{height: 4px; top: -2px;}
		}
		.pp-title{line-height: 25px; font-size: 21px;}
	}
	.pp-featured-small .pp-title{line-height: 25px; font-size: 21px;}
	/*------- /750 publish post -------*/

	/*------- 750 publish detail -------*/
	.pd-header{padding-top: 20px;}
	.page-publish-detail .bc{display: none;}
	.pd-title{font-size: 21px; line-height: 25px;}
	.pd-content{
		padding: 0;
		img{margin-left: 0; max-width: 100%;}
		iframe{width: 100%;}
	}
	.pd-related{
		margin-top: 45px; padding-top: 30px;
		.slick-arrow{top: calc(~"(100vw - 30px) / 4.7");}
		.slick-prev{left: -10px;}
		.slick-next{right: -10px;}
	}
	.pd-related-title{font-size: 26px; line-height: 32px; padding: 0 0 15px;}
	.pw-related-btns{padding: 20px 0 40px;}
	.pd-hero-image{margin-left: -15px; width: calc(~"100% - -30px");}
	.pd-info{
		.cp-rate span{
			width: 11px; height: 11px; margin: 2px 2px 0;
			&:after{font-size: 13px; line-height: 13px;}
		}
	}
	/*------- /750 publish detail -------*/

	/*------- 750 comments -------*/
	.comments-title{margin: 0 0 5px;}
	.comments-subtitle{font-size: 15px; padding: 6px 0 10px;}
	.comment-form-note{text-align: center;}
	.comments-list-title{font-size: 18px;}
	.comment_success{font-size: 15px;}
	/*------- /750 comments -------*/

	/*------- 750 brands -------*/
	.page-brands .main-wrapper{padding-bottom: 0;}
	.m-special{margin-left: -15px; margin-top: 0; width: calc(~"100% - -35px");}
	.m-special-item, .m-column{width: 33.333%;}
	.m-letter{font-size: 17px; margin: 0 0 10px;}
	.m-list{
		li{font-size: 15px; line-height: 19px; padding: 0 0 3px;}
	}
	/*------- /750 brands -------*/

	/*------- 750 cart -------*/
	.w-cart{flex-wrap: wrap;}
	.w-col1{padding-right: 0; padding-bottom: 25px;}
	.w-col2{width: calc(~"100% - -30px"); margin-left: -15px;}
	.w-title{font-size: 26px; line-height: 31px; padding-top: 0; padding-bottom: 6px;}
	.w-counter{font-size: 18px; padding-left: 9px;}
	.wp-btn-delete-d{display: none;}
	.wp-btn-delete-m{display: inline-block; vertical-align: top;}
	.wp-title{padding: 0 0 2px;}
	.wp>div{align-self: flex-start;}
	.wp-cnt{margin: 25px 0 0;}
	.w-totals{padding: 20px 15px;}
	.empty-cart{
		text-align: left; padding: 15px 0 0;
		h1{font-size: 22px; line-height: 21px; padding-bottom: 10px;}
	}
	.page-webshop-shopping_cart .main-wrapper{padding: 0 0 15px;}
	.payment-note, .ww-shipping{padding: 0 15px;}
	.wp-row-col2 .wp-total{width: auto; flex-grow: 1; padding-top: 3px;}
	.active-cart-modal{overflow: hidden; position: fixed; width: 100%;}
	/*------- /750 cart -------*/

	/*------- 750 coupons widget -------*/
	.ww-coupons-active{font-size: 15px;}
	.ww-coupon-delete:before{top: 0;}


	.ww-auth-coupons-form{
		top: auto; position: relative; text-align: left;
		.btn{width: 115px;}
	}
	.ww-auth-coupons-table{
		display: block;
		tbody, tr, td{display: block; padding: 0; border: 0; text-align: left!important; width: 100%!important;}
		tr{border-top: 1px solid @borderColor; padding: 10px 0;}
	}
	.ww-coupons-table-header{display: none!important;}
	/*------- /750 coupons widget -------*/

	/*------- 750 wishlist -------*/
	.wishlist-header{margin: 20px 0 10px;}
	.wishlists-title{
		font-size: 26px; line-height: 29px; padding-left: 33px;
		&:before{font-size: 20px; top: 1px;}
	}
	.wishlist-empty{padding: 0 0 20px;}
	.btn-wishslit-delete{position: relative; top: auto; right: auto; width: 100%; height: 40px; margin-bottom: 25px;}
	.wishlist-title-counter{font-size: 18px;}
	.wishlist-items{
		margin-left: -15px; width: calc(~"100% - -30px");
		.cp{width: 100%;}
	}
	/*------- /750 wishlist -------*/

	/*------- 750 compare -------*/
	.c-compare-sidebar-attributes{display: none;}
	.c-compare-items{margin-left: 0; min-height: 280px;}
	.page-compare{
		.main-wrapper{margin: 0;}
		.main{margin-top: 20px;}
	}
	.cp-compare{
		.cp-image figure{height: 230px;}
		&:not(.cp-new-compare){
			height: auto;
			.cp-cnt{
				min-height: 0;
				&>a{padding: 15px 0 15px 150px;}
			}
			.cp-cnt-header{padding-left: 0;}
			.cp-extra-price-lowest{max-width: none;}
		}
		.cp-badges{top: 110px;}
		.cp-badge{left: 0;}
		.cp-brand{top: 0;}
	}
	.page-compare-title{
		position: relative; top: auto; left: auto; width: 100%; padding: 0 0 20px;
		&:before{display: none;}
	}
	.cp-compare-title{display: none;}
	.compare-pager{display: flex; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor;}
	.compare-page{
		background: #000; border-right: 1px solid @borderColor; width: 33.3333%; color: #fff; height: 40px; text-align: center; line-height: 40px; color: @yellow; font-weight: bold;
		&.active{background: #fff; color: #000;}
	}
	.cp-compare{
		.cp-cnt{padding: 0 15px 15px;}
	}
	.cp-compare-header{padding: 17px 15px 15px;}
	.cp-compare-title{font-size: 12px; display: block; top: -5px; left: 0; position: relative;}
	.cp-compare-form{border: 1px solid @borderColor;}
	.compare-slider{
		.slick-arrow{top: 225px;}
		.slick-prev{left: 15px;}
		.slick-next{right: 15px;}
	}
	.no-compare-slider .cp-new-compare{width: 100%!important;}
	.c-compare-m-btns{display: flex; border-top: 1px solid @borderColor;}
	/*------- /750 compare -------*/

	/*------- 750 sweepstake -------*/
	.sws-title{margin: 0; font-size: 26px; line-height: 31px;}
	.wrapper-sweepstake{margin: 0;}
	.sweepstake-title{font-size: 18px; line-height: 23px;}
	.sweepstake-field{
		input[type=checkbox]+label, input[type=radio]+label{font-size: 15px;}
	}
	.sweepstake-buttons{margin: 0; .clear;}
	.btn-sweepstake{font-size: 12px;}
	.btn-sweepstake-next{padding-right: 50px; padding-left: 15px;}
	.btn-sweepstake-prev{padding-right: 15px; padding-left: 50px;}
	.btn-sweepstake-cancel{
		position: relative; width: 127px; margin: auto; top: auto; right: auto; margin-top: 20px; display: block;
		&:after{top: 6px;}
	}
	.sweepstake-field{
		input, textarea{width: 100%;}
	}
	.sweepstake-pager li{padding-left: 25px;}
	.sweepstake-pager li.active, .sweepstake-pager li.completed{padding-left: 23px;}
	.c-sweepstake-heading-right{display: none;}
	.c-sweepstake-wrapper{height: auto; padding: 20px 0; text-align: center;}
	.c-sweepstake-header h1{font-size: 17px; line-height: 23px; max-width: 250px; margin: auto;}
	.c-sidebar-advisor{display: none;}
	.flyer-download{
		margin-top: 20px; display: inline-block; vertical-align: top;
		a{float: none;}
	}
	.page-flyer h1{text-align: center; font-size: 24px; line-height: 27px; text-align: center; padding: 0;}
	.page-subtitle{text-align: center; font-size: 17px; line-height: 23px;}
	.flyer-content{
		margin-top: 10px;
		iframe{min-height: 250px;}
	}
	.page-flyer{
		.main-wrapper{text-align: center;}
		h1{padding: 10px 0 2px 0;}
	}
	/*------- /750 sweepstake -------*/

	/*------- 750 search -------*/
	.s-nav{
		position: relative;
		li{width: 33.3333%;}
		a{height: 45px; min-width: 100%;}
	}
	.s-header{padding-bottom: 0; padding-top: 0; margin: 0 0 20px;}
	.s-header-wrapper{margin: 0;}
	.s-h1{padding: 15px; font-size: 20px; line-height: 21px;}
	.s-headline{font-size: 13px; line-height: 19px;}
	.s-items{max-width: 100%;}
	.s-item-cnt{font-size: 15px;}
	.s-item-title{font-size: 18px; line-height: 25px;}
	/*------- /750 search -------*/

	/*------- 750 auth -------*/
	.auth-wrapper{flex-wrap: wrap;}
	.a-col{width: 100%; padding: 0;}
	.a-col1{border: 0; border-bottom: 1px solid @borderColor; padding: 10px 0 20px; margin-bottom: 20px;}
	.a-col2 h2, .a-subtitle{font-size: 16px; line-height: 20px; padding: 0 0 10px;}
	.a-form-title-personal, .a-have-account{margin-top: 25px;}
	.auth-links{font-size: 13px; line-height: 18px;}
	.btn-signup{width: 100%;}
	.page-auth{
		.cms-content{padding-right: 0;}
		.main-wrapper{flex-wrap: wrap;}
		.sidebar{
			display: block; order: 1; width: 100%; position: relative; margin-bottom: 15px; padding-bottom: 10px;
			&:after{.pseudo(auto,1px); background: @borderColor; bottom: 0; left: -15px; right: -15px;}
		}
		.sidebar-container{background: none; padding: 0;}
		.main-content{order: 2;}
		.sidebar-cols, .sidebar-help{display: none;}
		.nav-sidebar{
			display: flex; flex-wrap: wrap;
			a{color: @textColor; font-size: 15px;}
			li{width: 50%; padding: 0 0 4px;}
			.selected a span:after{.scaleX(1);}
		}
	}
	.a-auth-title{font-size: 26px; line-height: 31px;}
	.a-intro{display: none;}
	.a-intro-user{width: 100%; padding: 0;}
	.a-intro-user-title:before{display: none;}
	.auth-box{padding-bottom: 25px;}
	.a-section-coupons-title{margin-bottom: 15px!important;}
	.a-section-title{font-size: 20px;}
	.auth-box-coupons{padding: 0;}
	.page-auth-change_password, .page-auth-edit{
		.cms-content button{width: 100%;}
	}

	.auth-edit-profile-form{
		.field-address, .field-b_address{width: calc(~"100% - 110px");}
		.field-house_number, .field-b_house_number{
			width: 100px; margin-left: 10px;
			input{width: 100%!important; padding-right: 15px;}
		}
	}
	/*------- /750 auth -------*/

	/*------- 750 orders -------*/
	.page-orders{
		.order-row{
			&:last-child{border:0;}
		}
	}
	.orders{border: 0;}
	.table-order {
		display: block;
		.table-col{display: block; padding: 0; width: 100%; }
		.col-order-btns{position: absolute; right: 0; width: auto; font-size: 12px; top: 2px;}
		.col-order-total{text-align: left;}
	}
	.table-order-header{display: none;}
	.order-details {padding-top: 0; padding-right: 0;}
	.btn-all-orders { float: none; width: 100%; }
	.btn-order-details{font-size: 12px;}
	.a-section-title{border-bottom: 1px solid @borderColor; padding: 0 0 8px; margin-bottom: 10px;}
	.order-row{border:0; border-bottom:1px solid @borderColor; padding-bottom: 8px; font-size: 15px; margin-bottom: 11px;}
	.wp-details{padding: 12px 0; margin: 0;}
	.wp-sum{
		text-align: right; border-top: 1px solid @borderColor; margin-top: 20px; font-size: 20px;
		span{border:0; margin-top: 0; padding: 15px 0 0;}
	}
	.wp-total{width: 100%;}
	.btn-order-details .cf-title-icon{top: 4px;}
	/*------- /750 orders -------*/

	/*------- 750 thank you -------*/
	.page-webshop-thank_you{
		h1{font-size: 23px; padding-bottom: 8px;}
	}
	.invoice-container{padding: 15px 15px 7px; margin: 15px 0 0;}
	.thank-you-login button{width: 100%!important;}
	/*------- /750 thank you -------*/

	/*------- 750 footer -------*/
	.footer{font-size: 16px; line-height: 24px;}
	.cnj-trust-mark-vertical{display: none!important;}
	.tally-chat-btn{width: 45px;}
	.wrapper-footer{padding: 0; margin: 0;}
	.footer-row{flex-wrap: wrap;}
	.footer-row1 .footer-col:not(.footer-col1){
		width: 100%; border-bottom: 1px solid @borderColor; padding: 0; margin: 0;
		&.active{
			.footer-col-cnt{max-height: 1000px; padding-bottom: 15px;}
			.footer-title:before{display: none;}
		}
	}
	.footer-title{
		font-size: 18px; line-height: 20px; padding: 17px 15px 15px; margin: 0;
		&:after{display: none;}
	}
	.footer-col-cnt{overflow: hidden; max-height: 0; padding-left: 15px; padding-right: 15px;}
	
	//ROW1
	.footer-row1 .footer-col1{
		padding: 0 15px 24px; border-bottom: 1px solid @borderColor; width: 100%;
		.footer-title{
			padding: 18px 0 8px;
			&:after, &:before{display: none;}
		}
		.footer-col-cnt{overflow: initial; max-height: initial; padding: 0;}
		a[href^=tel]{text-decoration-color: #FCD002 !important; text-decoration: underline; text-underline-offset: 3px;}
	}
	.footer-col4{
		ul{
			li{line-height: 24px;}
		}
	}
	.footer-row2{
		flex-wrap: nowrap; flex-flow: column; max-width: 360px; width: 100%; padding: 16px 15px 0; align-items: flex-start;
		.footer-col{width: 100%;}
		.footer-col1, .footer-col2{
			img{width: 100%;}
		}
		.footer-col1{padding-bottom: 8px;}
		.footer-col2{padding-bottom: 20px;}
		.footer-col3{
			justify-content: space-between; width: 100%; padding: 0 9px 11px;
			.footer-col3-safe-purchase{max-width: 100%; width: 100%; justify-content: space-between;}
			p{
				margin-right: 0;
				img{max-width: 97px; max-height: 47px; width: auto;}
			}
		}
		.footer-col4{
			.cards{
				position: relative; padding: 0; top: unset;
				p{
					flex-wrap: nowrap;
					a:first-child{
						img{max-height: 28px;}
					}
				}
				img{max-height: 21px;}
			}
			.exchange-rate{padding: 21px 0 0; width: 100%; font-size: 12px; line-height: 16px;}
		}
	}


	.footer-row3{
		padding: 16px 15px 20px;
		.footer-col1{display: none;}
		.footer-col-locations{width: 100%;}
	}
	.nav-footer{
		font-size: 16px; flex-wrap: wrap; max-width: 320px;
		li{
			&:nth-child(4){
				margin: 0; padding: 0;
				&:after{display: none;}
			}
			a{text-decoration: underline; text-underline-offset: 2px; text-decoration-color: @yellow;}
		}
	}

	.footer-row4{
		padding: 0 15px 28px; flex-flow: column; align-items: flex-start; font-size: 12px; line-height: 16px;
		.footer-col1{width: 100%;}
		.footer-col-dev{width: 100%;}
	}

	.footer-row5{display: none;}
	.copy, .dev{border: 0; padding: 0!important; margin: 0!important; display: block;}
	.copy{
		&:after{display: none;}
	}
	.cards{
		padding: 20px 15px 0; text-align: center;
		img{max-width: 35px;}
	}
	.nav-footer-col li{padding: 4px 0;}
	.ontop{right: 15px;}
	.footer-title{
		position: relative;
		&:after, &:before{.pseudo(2px,12px); background: @gray; left: auto; top: 20px; right: 20px; .rotate(0deg); margin-left: 0; bottom: auto;}
		&:after{width: 12px; height: 2px; top: 25px; right: 15px;}
	}
	
	/*------- /750 footer -------*/
}