@import "defaults.less";

.main-wrapper{width: 600px; padding: 0 0 40px; margin: auto;}
.wc-container{width: 100%; position: relative;}
.wc-header{background: @gray; height: 85px; color: #fff;}
.wc-wrapper{width: 600px; margin: auto; position: relative;}
.logo{background: url(images/logo-white.svg) no-repeat left top; background-size: contain; width: 90px; height: 60px; left: 0; top: 13px;}
.w-sidebar-support{
	position: absolute; left: -300px; width: 250px; text-align: center; top: 50px;
	a{text-decoration: none;}
	p{padding: 0;}
	.sidebar-help-title{padding: 0 0 10px;}
}
.w-sidebar-cards{max-width: 190px; margin: 35px auto 0;}
.w-exchange-rate{padding: 15px 0 0; font-size: 14px;}
.sidebar-help-title{font-size: 19px; line-height: 26px;}
.wc-subtitle{font-size: 24px; line-height: 32px; padding: 0 0 20px;}
#field-b_company_oib{width: 100%!important;}
.wc-btns{
	text-align: right;
	@media (max-width: @t){
		text-align: left; padding-top: 22px;
	}
}
.btn-checkout{min-width: 245px;}
.wc-step-note{font-size: 12px; line-height: 24px; width: 235px; text-align: center; text-transform: uppercase; padding: 7px 0 0; margin: 0 0 0 auto;}
.w-sidebar-note{
	position: absolute; top: 52px; right: -380px; width: 315px; font-size: 13px; line-height: 20px;
	ul{
		.list; margin: 0;
		li:before{top:9px}
	}
}
.safe-purchase{
	font-size: 15px; line-height: 20px; padding: 10px 0 3px 85px; min-height: 80px;
	background-size: 65px auto;
}
.w-sidebar-payment-note{
	padding: 30px 0 0; font-size: 16px;
	ul{font-size: 15px;}
}
.webshop_widget_payment_installments{
	label{position: relative; top: auto; left: auto;}
}
.cc_types{margin: 10px 0;}
.cc_installments select{width: 100%!important;}

/*------- steps -------*/
.steps { 
	position: absolute; display: flex; top: -110px; right: 0; text-transform: uppercase; font-weight: bold;
	.step{min-width: 220px; height: 60px; display: flex; align-items: center; justify-content: center; margin: 0 0 0 5px; text-align: center; font-size: 18px; border-bottom: 0; opacity: .5;}
	a { color: #fff; text-decoration: none; display: block;}
	.current-step { 
		opacity: 1; position: relative; color: @textColor; background: @yellow;
		&:after{.pseudo(15px,15px); background: @yellow; .rotate(45deg); left: 50%; margin-left: -7px; bottom: -6px;}
	}
}
/*------- /steps -------*/

.label-payment{display: none;}
.form-label{
	input[type=checkbox]+label, input[type=radio]+label{color: #000; font-size: 17px;}
	textarea{height: 100px; margin-bottom: 15px;}
	.ww-coupons-add{width: 100%;}
}
.field-payment, .section-shipping{
	&>span{display: block; margin:0px 0 8px;}
}
.payment_info, .shipping_info{
	font-size:13px; line-height:18px; display: block; padding: 0 0 5px 34px; color: #5F5F5F;
	&:empty { display: none!important; }
}
.section-shipping {
	.global-error { 
		display: none; 
		&.active { display: block; }
	}
	.cart_info_total_extra_shipping {display: none!important; position:absolute; font-size: 13px; text-transform: uppercase; top:0; right: 0; padding:5px 0 0 28px; color: @green;}
	.btn-change-address{display: inline-block; vertical-align: top; margin-top: 6px; margin-bottom: 10px; color: #000;}
}
.shipping-row{
	padding:0 0 8px 0; position:relative;
	&.single-shipping{
		margin-top: -10px;
		input[type=radio]+label{
			padding-left: 0; font-weight: bold;
			&:before, &:after{display: none!important;}
			&.hide{display: none!important;}
		}
		.shipping_info{
			padding-left: 0;
			&.custom{padding: 0!important; margin-top: 0;}	
		}
	}
	.shipping_info.custom{padding: 0!important; margin-top: -10px;}
	input[type=radio]+label{
		&.hide{display: none!important;}
	}
	&.hidden{display: none;}
	.shipping_info{padding-top: 8px;}
}
.payment-options{padding: 0 0 40px;}
.section-bill>div:last-child{padding-bottom: 30px;}
.wc-bill-address, .wc-r1{
	padding: 0px 0 15px; line-height: 24px; font-size: 15px;
	p{padding: 0;}
}
.step2-shipping{
	padding: 20px 0;
	.wc-subtitle{padding: 0 0 15px;}
	&>span{display: block; margin: 0 0 10px;}
	.shipping_info{padding-top: 10px;}
}

.ww-coupons{
	float: none; text-align: left; padding: 15px 0; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor;
	&.active{
		.wc-coupons-header{display: none;}
		.wc-coupons-body{max-height: none;}
	}
}
.ww-coupons-form{
	label{position: relative; top: auto; left: auto; display: block; font-size: 20px;}
}
.ww-coupons-list{width: 100%; float: none;}
.wc-cart-title{padding: 15px 0; position: relative; font-size: 18px; cursor: default;}
.wc-cart-container{
	overflow: hidden; max-height: 0; .transition(max-height);
	&.active{max-height: 800px;}
}
.ww-coupons-add{
	margin-top: 20px;
	&.active{margin-bottom: 10px;}
}
.wc-coupons-header{position: relative; font-size: 18px; cursor: default;}
.wc-coupons-body{
	overflow: hidden; max-height: 0; .transition(max-height);
	&.active{max-height: 350px;}
}
.wc-btn-toggle{
	position: absolute; display: block; top: 4px; right: 0; padding: 0 22px 0 0; text-transform: uppercase; color: @textColor; text-decoration: underline; font-size: 14px; line-height: 20px; font-weight: bold;
	.toggle-icon{position: absolute; right: 0; top: 2px;}
	&.active{
		.toggle-icon:before{display: none;}
	}
}
.wc-btn-toggle-cart{
	top: 17px;
	.c{display: none;}
	&.active{
		.c{display: block;}
		.o{display: none;}
	}
}
.wc-btn-toggle-coupons{
	.l2{display: none;}
	&.active{
		.l1{display: none;}
		.l2{display: inline;}
	}
}
.step3-message{border-top: 1px solid @borderColor; padding: 30px 0 0 0;}
.step3-field-message{padding-bottom: 0!important;}
.wc-step3-btns-container{
	display: flex; padding: 35px 0 0 0; align-items: center; justify-content: space-between;
	@media (max-width: @t){
		display: block; padding: 25px 0 0 0;
	}
}
.wc-terms{padding-left: 15px;}
.webshop-accept-terms{
	position: relative; margin: 0 0 10px;
	a{font-weight: bold;}
	input[type=checkbox]+label{
		font-size: 15px; line-height: 24px; padding-top: 1px;
		&:before{top: 1px; border-width: 2px; border-color: @gray;}
	}
}
.webshop-newsletter{margin: 0;}
.wwp{padding-left: 0; padding-right: 0;}
.wwp-col1{width: 100px;}
.w-table{margin: 0;}
.w-totals-cnt{padding: 20px 0 30px 110px; font-size: 15px; line-height: 25px;}
.cart-total{font-weight: bold; font-size: 16px; padding-top: 3px; color: #000;}
.w-btn-change{font-size: 14px; text-transform: uppercase; color: @textColor; line-height: 14px; display: inline-block; vertical-align: top; margin: 20px 0 0;}
.section-shipping{padding-bottom: 30px;}

.wc-accept-terms-tooltip{
	position: absolute; top: -3px; left: -150px; display: none; background: @yellow; color: @textColor; font-size: 15px; line-height: 17px; padding: 7px 10px; width: 130px; text-align: center;
	&:before{.pseudo(10px,10px); background: @yellow; .rotate(45deg); right: -3px; top: 11px;}
	&.active{display: block;}
}
.webshop-alert-terms{
	background: @yellow; color: #000; padding: 10px 20px 9px; font-size: 15px; line-height: 22px; position: relative;
	&:after{.icon-danger-red; font: 25px/25px @fonti; color: #fff; position: absolute; top: 8px; right: 15px;}
	&:before{.pseudo(10px,10px); background: @yellow; .rotate(45deg); left: 22px; bottom: -3px;}
}
.wc-shipping-remaining{
	background: @yellow; color: @textColor; padding: 7px 20px; font-size: 15px; margin-top: 5px; position: relative;
	.wc-shipping-remaining-value{font-weight: bold;}
	&:after{.icon-danger-red; font: 25px/25px @fonti; position: absolute; top: 8px; right: 15px; color: #fff;}
}
.btn-finish:not(.active){
	cursor: default;
}

.col-cont{
	display: flex; flex-wrap: wrap;
	.wc-subtitle{width: 100%;}
	.global-error{width: 100%;}
	.field{width: 100%;}
	.field-address, .field-b_address{width: calc(~"100% - 130px");}
	.field-house_number, .field-b_house_number{
		width: 120px; margin-left: 10px;
		input{width: 100%!important;}
	}
}
/*------- credit card payment -------*/
.cc_expire input, .cc_cvv input{padding: 0 5px; text-align: center;}
.cc_installments{max-width: 170px;
	.chzn-container-single .chzn-single span{color: #000;}
}
.cc_type{position: absolute; padding-left: 10px !important;}

.webshop_widget_payment_advanced{
	position: relative; padding-top: 6px;
	#field-error-cc{
		left: 100px;
		&:after{left: 7px;}
	}
	label{width: 100% !important; display: block !important; font-size: 16px !important;}
	div{padding-bottom: 8px; position: relative;}
	.error{left: 130px; right: auto;}
	.clear{padding: 0;}
	#field-cc_cvv{margin-right: 8px;}
	a{color: @textColor;}
	input[type=text]{
		&:hover, &:focus{background: #fff; color: #000;}
	}
}
.payment_info[data-payment_code=kekspay]{
	position: relative; padding-right: 85px;
	&:after{.pseudo(72px,32px); background: url(images/kekspay.svg) no-repeat top left; background-size: contain; right: 0; top: 0;}
}
/*------- /credit card payment -------*/

/*------- shipping restiction -------*/
.shipping-restriction-note{
	position: relative; display: block; padding: 13px 20px; background: @yellow; font-size: 16px; line-height: 22px; margin: 5px 0; color: @textColor;
	&:empty{padding: 0; margin: 0; background: transparent; display: none;}	
	&.payment{
		margin: 0 0 -20px;
		a{
			margin: 10px 0 0;
			&:hover{text-decoration-color: transparent;}	
		}	
	}
}
/*------- /shipping restiction -------*/

@media screen and (max-width: 1400px) {
	.w-sidebar-note{right: -340px;}
}

@media screen and (max-width: 1240px) {
	.main-wrapper, .wc-wrapper{width: auto;}
	.wc-header, .main{padding: 0 30px;}
	.wc-header{height: 70px;}
	.logo{width: 75px; height: 50px; top: 10px;}
	.steps{
		right: auto; left: 140px; top: -80px;
		.step{height: 50px;}
	}
	.form-cart{width: 60%; flex-grow: 0; flex-shrink: 0;}
	.wc-container{display: flex; flex-direction: row-reverse;}
	.w-sidebar-support, .w-sidebar-note{left: auto; right: 0; position: relative;}
	.w-sidebar-support{width: auto;}
	.w-sidebar-note{width: auto; margin-top: 45px;}
	.w-sidebar-container{width: 40%; padding-left: 60px;}
	.w-sidebar-payment-note{padding: 20px 0 0;}
	.wc-accept-terms-tooltip{display: none!important;}
	.w-sidebar-support .sidebar-help-title{padding: 0 0 2px;}
}

@media screen and (max-width: 990px) {
	.wc-subtitle{font-size: 18px; line-height: 23px; padding: 0 0 10px;}	
	.steps{
		top: -75px; left: 110px;
		.step{font-size: 14px; height: 45px;}
	}
	.w-sidebar-container{padding-left: 35px; width: 45%;}
	.form-cart{width: 55%;}
	.form-label{
		input[type=checkbox]+label, input[type=radio]+label{font-size: 15px;}
	}
	.wc-coupons-header, .wc-cart-title{font-size: 15px;}
	.wc-btn-toggle{font-size: 12px;}
	.webshop-accept-terms{font-size: 13px;}
	.page-checkout-step3 .w-sidebar-note{display: none;}
	.w-sidebar-support{top: 30px;}
	.w-sidebar-note{margin-top: 15px;}
}

@media screen and (max-width: 750px) {
	.steps{
		top: -50px; left: 70px;
		.step{min-width: 0; font-size: 12px; height: 35px; padding: 0 10px;}
		.current-step:after{bottom: -3px;}
	}
	.main, .wc-header{padding: 0 15px;}	
	.wc-header{height: 50px;}
	.logo{width: 50px; height: 33px; top: 9px;}
	.wc-container{flex-wrap: wrap; flex-direction: column-reverse;}
	.w-sidebar-container, .form-cart{width: 100%; padding: 0;}
	.wc-step-note, .btn-checkout{width: 100%;}
	.w-sidebar-cards{margin: 25px auto 0;}
	.w-sidebar-note{margin: 0; text-align: center;}
	.w-sidebar-payment-note{
		text-align: center; padding: 20px 0 75px;
		ul li{
			padding: 3px 0;
			&:before{display: none;}
		}
	}	
	.safe-purchase{text-align: left; background-size: 45px auto; padding: 0px 0 3px 60px;}
	.w-sidebar-payment-note{padding-top: 10px;}
	.wc-subtitle{padding: 7px 0 10px;}
	.payment-options{padding: 0 0 20px;}
	.wc-btn-toggle{
		position: relative; top: auto; right: auto; margin-top: 4px; display: inline-block; vertical-align: top; padding-right: 0; padding-left: 22px;
		.toggle-icon{right: auto; left: 0;}
	}
	.wc-btn-toggle-cart{display: block;}
	.wc-shipping-remaining{
		font-size: 12px;
		&:after{font-size: 20px; line-height: 20px;}
	}
	.w-totals-cnt{padding: 20px 0;}
	.wwp-col1{width: 65px;}
	.w-sidebar-support{top: auto; padding-top: 20px;}
	.wc-cart-title, .ww-coupons{padding: 10px 0;}
	.section-bill>div:last-child{padding-bottom: 15px;}
	.webshop-alert-terms{font-size: 12px; line-height: 16px; padding: 13px 45px 15px 15px;}
	.step3-wc-btns{float: none; padding: 20px 0 0;}
	.main-wrapper{padding: 0 0 50px;}
	.w-btn-change{
		background: @gray; font-size: 13px; height: 35px; line-height: 35px; padding: 0 15px; color: @yellow; text-decoration: none; float: right; margin: 10px 0 15px;
		&:hover{text-decoration: none; color: @yellow;}
	}

	.col-cont{
		.field-address, .field-b_address{width: calc(~"100% - 110px");}
		.field-house_number, .field-b_house_number{
			width: 100px; margin-left: 10px;
			input{width: 100%!important; padding-right: 15px;}
		}
	}

	.shipping-restriction-note{
		padding: 12px 16px; font-size: 14px; line-height: 20px; margin: 5px 0;
	}
	.shipping-restriction-note.payment{
		margin: 4px 0 -8px;
		a{margin-top: 8px;}	
	}
}